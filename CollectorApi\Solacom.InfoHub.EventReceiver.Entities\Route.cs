﻿using System.Xml.Serialization;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    [XmlRoot(ElementName = "route")]
    public class Route
    {
        [XmlElement(ElementName = "uri")]
        public string Uri { get; set; }
        [XmlElement(ElementName = "rule")]
        public string Rule { get; set; }
        [XmlElement(ElementName = "reason")]
        public string Reason { get; set; }
        [XmlElement(ElementName = "mediaLabel")]
        public string MediaLabel { get; set; }
        [XmlElement(ElementName = "attempt")]
        public int? Attempt { get; set; }
        [XmlElement(ElementName = "priority")]
        public int? Priority { get; set; }
        [XmlElement(ElementName = "ani")]
        public string Ani { get; set; }
        [XmlElement(ElementName = "aniDomain")]
        public string AniDomain { get; set; }
        [XmlElement(ElementName = "dnis")]
        public string Dnis { get; set; }
        [XmlElement(ElementName = "pani")]
        public string Pani { get; set; }
        [XmlElement(ElementName = "esrn")]
        public string Esrn { get; set; }
        [XmlElement(ElementName = "callerName")]
        public string CallerName { get; set; }
        [XmlElement(ElementName = "aniTranslated")]
        public string AniTranslated { get; set; }
        [XmlElement(ElementName = "dnisTranslated")]
        public string DnisTranslated { get; set; }
        [XmlElement(ElementName = "callerNameTranslated")]
        public string CallerNameTranslated { get; set; }
        [XmlElement(ElementName = "circuit")]
        public string Circuit { get; set; }
        [XmlElement(ElementName = "circuitId")]
        public string CircuitId { get; set; }
        [XmlElement(ElementName = "ruleName")]
        public string RuleName { get; set; }
        [XmlElement(ElementName = "concurrentCalls")]
        public string ConcurrentCalls { get; set; }
        [XmlElement(ElementName = "trunkGroupId")]
        public string TrunkGroupId { get; set; }
    }
}
