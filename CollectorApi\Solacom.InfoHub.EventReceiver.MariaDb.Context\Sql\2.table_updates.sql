﻿/** 
	Created: Aug 2021
	Updated: Jul 2025 - Added missing indexes
	Author: <PERSON>
	Contains any update calls specific to existing tables.  If net new table, it should be in the Creation file.  
**/

INSERT INTO CollectorAPI.processstate(State) VALUES('new');
INSERT INTO CollectorAPI.processstate(State) VALUES('in-process');
INSERT INTO CollectorAPI.processstate(State) VALUES('processed');
INSERT INTO CollectorAPI.processstate(State) VALUES('error');
INSERT INTO CollectorAPI.processstate(State) VALUES('expired');


-- Create Indexes

-- Create AgentSession Indexes
CREATE INDEX IX_Client_Id ON CollectorAPI.agentsession (Client_Id);
CREATE INDEX IX_Date_Created ON CollectorAPI.agentsession (Date_Created);
CREATE INDEX IX_MediaLabel ON CollectorAPI.agentsession (MediaLabel);

-- Create Events Indexes
CREATE INDEX IX_Call_Identifier ON CollectorAPI.events(Call_Identifier);
CREATE INDEX IX_Client_Id ON CollectorAPI.events (Client_Id);
CREATE INDEX IX_Date_Created ON CollectorAPI.events(Date_Created);
CREATE INDEX IX_EventType ON CollectorAPI.events(EventType);
CREATE INDEX IX_State_Id ON CollectorAPI.events(State_Id);

-- Create HashedEvents Indexes
CREATE INDEX IX_Client_Id ON CollectorAPI.hashedevents (Client_Id);
CREATE INDEX IX_Date_Created ON CollectorAPI.hashedevents (Date_Created);
CREATE INDEX IX_hashed_key ON CollectorAPI.hashedevents (hashed_key);

-- Create ProcessQueue Indexes
CREATE INDEX IX_callIdentifier ON CollectorAPI.processqueue (callIdentifier);
CREATE INDEX IX_clientId ON CollectorAPI.processqueue (clientId);
CREATE INDEX IX_dateUpdated ON CollectorAPI.processqueue (dateUpdated);

-- Create CallSummary Indexes
CREATE INDEX IX_timeStamp ON InsightsData.callsummary (timeStamp);
CREATE INDEX IX_callDetailsIndex ON InsightsData.callsummary (callDetailsIndex);
CREATE INDEX IX_callIdentifier ON InsightsData.callsummary (callIdentifier);
CREATE INDEX IX_CustomerName ON InsightsData.callsummary (CustomerName);

-- Create CallEvent Indexes
CREATE INDEX IX_callIdentifier ON InsightsData.callevent (callIdentifier);
CREATE INDEX IX_customerName ON InsightsData.callevent (customerName);
