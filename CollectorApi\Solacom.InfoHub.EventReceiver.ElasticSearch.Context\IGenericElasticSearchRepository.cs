﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Solacom.InfoHub.EventReceiver.ElasticSearch.Entities;

namespace Solacom.InfoHub.EventReceiver.ElasticSearch.Context
{
    public interface IGenericElasticSearchRepository<T> where T : class
    {
        Task Insert(T document, string indexPrefix);
        Task InsertMany(List<T> document, string indexPrefix);
        Task Delete(Guid id, string indexPrefix);
        Task DeleteByCallId(string callId, string indexPrefix);
        Task Update(T document, Guid id, string indexPrefix, string original_index);
        Task Update(T document, string id, string indexPrefix, string original_index);
        Task<T> GetById(Guid id, string indexPrefix);
        Task<List<CallSummary>> GetByCallId(string id, string indexPrefix);
    }
}