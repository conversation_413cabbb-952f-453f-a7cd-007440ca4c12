﻿using System.Xml.Serialization;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    [XmlRoot(ElementName = "acdLogout")]
    public class AcdLogout
    {
        [XmlElement(ElementName = "mediaLabel")]
        public string MediaLabel { get; set; }
        [XmlElement(ElementName = "agentUri")]
        public string AgentUri { get; set; }
        [XmlElement(ElementName = "agentRole")]
        public string AgentRole { get; set; }
        [XmlElement(ElementName = "tenantGroup")]
        public string TenantGroup { get; set; }
        [XmlElement(ElementName = "operatorId")]
        public string OperatorId { get; set; }
        [XmlElement(ElementName = "workstation")]
        public string Workstation { get; set; }
        [XmlElement(ElementName = "deviceName")]
        public string DeviceName { get; set; }
        [XmlElement(ElementName = "ringGroupName")]
        public string RingGroupName { get; set; }
        [XmlElement(ElementName = "ringGroupUri")]
        public string RingGroupUri { get; set; }
    }
}
