﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>

    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="8.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.2.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="2.2.6" />
    <PackageReference Include="Microsoft.Extensions.Identity.Core" Version="3.1.1" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="16.0.1" />
    <PackageReference Include="Moq" Version="4.12.0" />
    <PackageReference Include="MSTest.TestAdapter" Version="1.4.0" />
    <PackageReference Include="MSTest.TestFramework" Version="1.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Solacom.InfoHub.EventReceiver.ElasticSearch.Entities\Solacom.InfoHub.EventReceiver.ElasticSearch.Entities.csproj" />
    <ProjectReference Include="..\Solacom.InfoHub.EventReceiver.Entities\Solacom.InfoHub.EventReceiver.Entities.csproj" />
    <ProjectReference Include="..\Solacom.InfoHub.EventReceiver.Web.API\Solacom.InfoHub.EventReceiver.Web.API.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Microsoft.AspNetCore.Mvc.Abstractions">
      <HintPath>..\..\..\..\Program Files\dotnet\sdk\NuGetFallbackFolder\microsoft.aspnetcore.mvc.abstractions\2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Mvc.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Mvc.Core">
      <HintPath>..\..\..\..\Program Files\dotnet\sdk\NuGetFallbackFolder\microsoft.aspnetcore.mvc.core\2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Mvc.Core.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="i3Logs\" />
  </ItemGroup>

</Project>
