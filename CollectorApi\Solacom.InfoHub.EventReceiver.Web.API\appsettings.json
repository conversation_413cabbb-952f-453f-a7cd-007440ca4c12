{"version": "<GIT BRANCH HASH> - <SHORT DESCRIPTION>", "Logging": {"LogLevel": {"Default": "Debug", "System": "Warning", "Microsoft": "Warning"}, "elasticsearchSettings": {"serilog": {"enableSSL": "true", "restrictedToMinimumLevel": "Information", "indexFormat": "application_logs_guardian_insights-{0:yyyy.MM.dd}", "autoRegisterTemplate": false, "autoRegisterTemplateVersion": "ESv7"}}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "Properties": {"ApplicationName": "CollectorAPI", "Instance": "ComtechAnalytics"}, "WriteTo": [{"Name": "File", "Args": {"restrictedToMinimumLevel": "Debug", "path": "Logs/logs_.log", "rollingInterval": "Day", "retainedFileCountLimit": 31}}]}, "clientSettings": {"clients": "solacom,flagler-fl,roan<PERSON>ounty-wv,clinton-oh", "clientcodeIndexPrefix": {"solacom": "solacom", "flfl": "flagler-fl", "rowv": "roanecounty-wv", "cloh": "clinton-oh"}, "clientTenantMapping": {"solacom": {"tenantgroupeveryone": "tenantgroupeveryone"}, "flfl": {"flaglercountyfl": "flaglercountyfl"}, "rowv": {"roane": "roane"}, "cloh": {"ccso": "ccso", "wpd": "wpd"}}, "clientTimezoneMapping": {"solacom": "America/New_York", "flfl": "America/New_York", "rowv": "America/New_York", "cloh": "America/New_York"}, "clientEsiNetSupportedList": ""}, "classofservice": {"Values": {"RESD": "Landline", "BUSN": "Landline", "WPH1": "Wireless", "WPH2": "Wireless", "WRLS": "Wireless", "VOIP": "Voip", "PBXA": "Landline", "PBXB": "Landline", "PBXC": "Landline", "CNTX": "Landline", "BSNX": "Landline", "MOBL": "Wireless", "OMBL": "Wireless", "VRES": "Voip", "VPAY": "Voip", "VBUS": "Voip", "COCT": "Landline", "RSDX": "Landline", "COIN": "Landline", "RESX": "Landline", "BUSX": "Landline", "P2P1": "Wireless", "NA": "Unknown", "PAY$": "Landline", "VMBL": "Voip", "AFF": "Landline", "BOP": "Landline", "BUS": "Landline", "CCC": "Landline", "CCX": "Landline", "CEL": "Wireless", "CIP": "Voip", "COM": "Landline", "CPB": "Landline", "CTX": "Landline", "DAT": "Landline", "DEA": "Landline", "DID": "Landline", "DRT": "Wireless", "FEX": "Landline", "HDC": "Landline", "LRR": "Landline", "MIP": "Voip", "MUL": "Landline", "OUT": "Landline", "PCS": "Wireless", "PUB": "Landline", "PUC": "Landline", "RES": "Landline", "REV": "Landline", "ROP": "Landline", "SPP": "Landline", "TXE": "Wireless", "TXF": "Wireless", "VEL": "Landline", "VIP": "Landline", "WL2": "Wireless", "ANON": "Landline", "BPBX": "Landline", "CELL": "Wireless", "CTRX": "Landline", "CCRS": "Landline", "RPBX": "Landline", "CCTX": "Landline", "WATS": "Landline", "HDCP": "Landline", "OUTO": "Landline", "ROPX": "Landline", "BOPX": "Landline"}}, "userKeyData": {"userId": "Cdefcy5ha8hfQJBSMOU1+rIi2oYprS+mnEGvqktiEk7jDjJog0YJTgL5ucBUcepGHbggSxAqWLfllophbhb9+g==", "hashedApiKey": "BgSPu7kxqPqTr76PRxRuB7pV5x7a94d1sNkUyNfwFrpNFjGVA5Qy9K8K4fn6XOXI1v7pqv6h9VMp2DTRMPRxwj9hfMMR8cAVDv+ZWKazU+pfT1UypxIMg5z7ahzSATr2YO8+C54eoi8NRQGgSdQjXw==", "rawApiKey": "QSBdDNGfW+BYp7Q8+eax1qSPTdYA0P1e8EIFbH6vMsAkLPMsCxb1MlUUNBb4YYP4e/6xNzY4SaWsQSIePB9+rA=="}, "scheduledServicesFrequenciesInMinutes": {"expiredEventService": 1, "dbCleanupService": 180, "processQueueservice": 1}, "expiredEventInHours": 8, "AllowedHosts": "*", "Database": {"CleanupOlderThanInHours": {"hashevents": 48, "events": 48, "agentsession": 48}}}