FROM mcr.microsoft.com/dotnet/core/aspnet:3.1-buster-slim AS base
WORKDIR /app
COPY Solacom.InfoHub.EventReceiver.Web.API/*.xml /app/
EXPOSE 80

FROM mcr.microsoft.com/dotnet/core/sdk:3.1-buster AS build
WORKDIR /src
COPY ["Solacom.InfoHub.EventReceiver.Web.API/Solacom.InfoHub.EventReceiver.Web.API.csproj", "Solacom.InfoHub.EventReceiver.Web.API/"]
COPY ["Solacom.InfoHub.EventReceiver.Exceptions/Solacom.InfoHub.EventReceiver.Exceptions.csproj", "Solacom.InfoHub.EventReceiver.Exceptions/"]
COPY ["Solacom.InfoHub.EventReceiver.Entities/Solacom.InfoHub.EventReceiver.Entities.csproj", "Solacom.InfoHub.EventReceiver.Entities/"]
COPY ["Solacom.InfoHub.EventReceiver.BusinessLogic/Solacom.InfoHub.EventReceiver.BusinessLogic.csproj", "Solacom.InfoHub.EventReceiver.BusinessLogic/"]
COPY ["Solacom.InfoHub.EventReceiver.MariaDb.Context/Solacom.InfoHub.EventReceiver.MariaDb.Context.csproj", "Solacom.InfoHub.EventReceiver.MariaDb.Context/"]
COPY ["Solacom.InfoHub.EventReceiver.ElasticSearch.Context/Solacom.InfoHub.EventReceiver.ElasticSearch.Context.csproj", "Solacom.InfoHub.EventReceiver.ElasticSearch.Context/"]
COPY ["Solacom.InfoHub.EventReceiver.ElasticSearch.Entities/Solacom.InfoHub.EventReceiver.ElasticSearch.Entities.csproj", "Solacom.InfoHub.EventReceiver.ElasticSearch.Entities/"]
COPY ["Solacom.InfoHub.EventReceiver.Web.Dtos/Solacom.InfoHub.EventReceiver.Web.Dtos.csproj", "Solacom.InfoHub.EventReceiver.Web.Dtos/"]
COPY ["Solacom.InfoHub.EventReceiver.AppService/Solacom.InfoHub.EventReceiver.AppService.csproj", "Solacom.InfoHub.EventReceiver.AppService/"]
RUN dotnet restore "Solacom.InfoHub.EventReceiver.Web.API/Solacom.InfoHub.EventReceiver.Web.API.csproj"
COPY . .
WORKDIR "/src/Solacom.InfoHub.EventReceiver.Web.API"
RUN dotnet build "Solacom.InfoHub.EventReceiver.Web.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Solacom.InfoHub.EventReceiver.Web.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Solacom.InfoHub.EventReceiver.Web.API.dll"]
