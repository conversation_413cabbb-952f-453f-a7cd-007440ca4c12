﻿using System;
using System.Collections.Generic;
using Nest;
public class CallSummary : ICloneable
    {
        public CallSummary()
        {
        /////**DIFFERENT FROM SOURCE**- commented out as not required / part of the ES index + did not need to include the additional objects. **//
        //            OutboundCalls = new List<OutboundCallDetails>();
        AnsweredLessThan10s =
                AnsweredLessThan15s =
                    AnsweredLessThan40s =
                        AnsweredMoreThan40s = 0;
        }

        /// <summary>
        /// Stores the Elasticsearch Index the Call Summary is stored in
        /// </summary>
        /// <remarks>Set to be ignored on document write to ES</remarks>
        [Text(Ignore = true)]
        public string ElasticIndex { get; set; }

        public string MediaLabel { get; set; }
        public string Callid { get; set; }
        public string Incidentid { get; set; }
        public DateTime? Starttime { get; set; }
        public DateTime? Endtime { get; set; }
        public DateTime TimeStamp { get; set; }
        public string Ani { get; set; }
        public string Callbacknumber { get; set; }
        public string AgentCallbacknumber { get; set; }
        public string Calltype { get; set; }
        public string Callstate { get; set; }
        public bool IsAdmin { get; set; }
        public bool IsTandem { get; set; }
        public bool IsAdminEmergency { get; set; }
        public bool IsUnknownType { get; set; }
        public bool IsEmergency { get; set; }
        public string Id { get; set; }
        public string OriginalCos { get; set; }
        public string FinalCos { get; set; }
        public string Carrier { get; set; }
        public string Callmobilitytype { get; set; }
        public float IsTransferred { get; set; }
        public float IsOutbound { get; set; }
        public float EmergencyCall { get; set; }
        public float AdminCall { get; set; }
        public float TandemCall { get; set; }
        public float AdminEmergencyCall { get; set; }
        public float UnknownCall { get; set; }

        public float InProgressState { get; set; }
        public float CompletedState { get; set; }
        public float AbandonedState { get; set; }

        public float LandlineType { get; set; }
        public float WirelessType { get; set; }
        public float VoipType { get; set; }
        public float UnknownType { get; set; }
        public float SMSType { get; set; }
        public float RTTType { get; set; }
        public float TDDType { get; set; }
        public float TDDChallengeOnly { get; set; }
        public float NotFoundType { get; set; }
        public double? Uncertainty { get; set; }
        public double? Confidence { get; set; }
        public double? TimeToAnswerInSeconds { get; set; }
        
        public double? NonEmergencyTimeToAnswerInSeconds { get; set; }
        public double? HoldTimeInSeconds { get; set; }
        public double? NonEmergencyHoldTimeInSeconds { get; set; }
        public double? TalkTimeInSeconds { get; set; }
        public double? NonEmergencyTalkTimeInSeconds { get; set; }
        public float? AnsweredLessThan10s { get; set; }
        public float? AnsweredMoreThan10s { get; set; }
        public float? NonEmergencyAnsweredLessThan10s { get; set; }
        public float? NonEmergencyAnsweredMoreThan10s { get; set; }
        public float? AnsweredLessThan15s { get; set; }
        public float? NonEmergencyAnsweredLessThan15s { get; set; }
        public float? AnsweredLessThan20s { get; set; }
        public float? NonEmergencyAnsweredLessThan20s { get; set; }
        public float? AnsweredMoreThan20s { get; set; }
        public float? NonEmergencyAnsweredMoreThan20s { get; set; }
        public float? AnsweredLessThan40s { get; set; }
        public float? NonEmergencyAnsweredLessThan40s { get; set; }
        public float? AnsweredMoreThan40s { get; set; }
        public float? NonEmergencyAnsweredMoreThan40s { get; set; }
        public double? PsapTimeToAnswerInSeconds { get; set; }
        public double? NonEmergencyPsapTimeToAnswerInSeconds { get; set; }
        public double? AgentTimeToAnswerInSeconds { get; set; }
        public float? AgentAnsweredLessThan10s { get; set; }
        public float? AgentAnsweredMoreThan10s { get; set; }
        public float? AgentAnsweredLessThan15s { get; set; }
        public float? AgentAnsweredLessThan20s { get; set; }
        public float? AgentAnsweredMoreThan20s { get; set; }
        public float? AgentAnsweredLessThan40s { get; set; }
        public float? AgentAnsweredMoreThan40s { get; set; }

        #region Time Helpers
            
        /// <summary>
        /// Stores the Time Zone information for IANA when deployed to Linux OS
        /// </summary>
        /// <remarks>see @ConvertTimestamp for usage of this lookup.</remarks>
        private Dictionary<string, string> _timeZoneLookupIANA = new Dictionary<string, string> {
            {"Eastern Standard Time", "America/New_York" },
            {"Central Standard Time", "America/Chicago" },
            {"Mountain Standard Time", "America/Phoenix" },
            {"Pacific Standard Time", "America/Los_Angeles" },
            {"Alaskan Standard Time", "America/Anchorage" },
            {"Hawaiian Standard Time", "America/Adak" },
            {"Newfoundland Standard Time", "America/St_Johns" },
            {"Atlantic Standard Time", "America/Puerto_Rico" },
        };

        /// <summary>
        /// Converts the timestamp to the passed timezone
        /// </summary>
        /// <param name="timeZoneIdString">String identifier of the timezone - as based on the TimeZoneInfo object.</param>
        /// <returns></returns>
        private DateTime ConvertTimestamp(string timeZoneIdString)
        {
            ///This was introduced due a issue when deploying .netcore to a Linux environment, the timezone resources that are referenced are using a different standard.  Therefore we need to have logic to pull for different ZoneIds
            /// https://stackoverflow.com/questions/17348807/how-to-translate-between-windows-and-iana-time-zones Good reference of the issue. 
            /// Original Error for search reference if required: "The time zone ID 'Eastern Standard Time' was not found on the local computer., The time zone ID 'Eastern Standard Time' was not found on the local computer., Could not find file '/usr/share/zoneinfo/Eastern Standard Time'."
            TimeZoneInfo timeZone;
            if (System.Runtime.InteropServices.RuntimeInformation.IsOSPlatform(System.Runtime.InteropServices.OSPlatform.Windows))
            {
                timeZone= TimeZoneInfo.FindSystemTimeZoneById(timeZoneIdString);
            }
            //if (System.Runtime.InteropServices.RuntimeInformation.IsOSPlatform(System.Runtime.InteropServices.OSPlatform.Linux))
            else //Doing a global else to ALWAYS  attempt a return type - if this fails than we have a configuration issue to follow up on 
            {
                timeZone= TimeZoneInfo.FindSystemTimeZoneById(_timeZoneLookupIANA[timeZoneIdString]);
            }

            DateTime dtime = TimeZoneInfo.ConvertTimeFromUtc(this.TimeStamp, timeZone);

            return dtime;
        }

        /// <summary>
        /// Retrieves the integer index of the Week in the year.
        /// </summary>
        /// <param name="dTime"></param>
        /// <returns></returns>
        private int GetWeekOfMonth(DateTime dTime)
        {
            DateTime firstOfMonth = new DateTime(dTime.Year, dTime.Month, 1);
            int weekOfMonth = System.Globalization.ISOWeek.GetWeekOfYear(dTime) - System.Globalization.ISOWeek.GetWeekOfYear(firstOfMonth) + 1;
            if (weekOfMonth < 0)    //required if the week starts in the previous year (i.e. First Of Month Week == 52) - if this case occurs, add 52 to get the actual week count (recall, it will be a negative number i.e. -50 for week 2)
            {
                weekOfMonth = 52 + weekOfMonth;
            }

            return weekOfMonth;
        }

        /// <summary>
        /// Retrieves the string representation of the Day of Week
        /// </summary>
        /// <param name="dTime"></param>
        /// <returns></returns>
        private string GetDayOfWeekString(DateTime dTime)
        {
            return $"0{(int)dTime.DayOfWeek}-{dTime.DayOfWeek}";
        }

        #endregion

        #region Time Properities - UTC (core)
        /// <summary>
        /// Internal calculation for timezone usage specific fields.  Excluded from final Index write.
        /// Currently NO re-calculation is done, but keeping it here top be explicit on timestamp source
        /// </summary>
        [Text(Ignore = true)]
        private DateTime _timestampUTC
        {
            get
            {
                return this.TimeStamp;
            }
        }
        /// <summary>
        /// Returns the hour of the day
        /// </summary>
        public int HourOfDayUTC
        {
            get
            {
                return this._timestampUTC.Hour;
            }
        }
        /// <summary>
        /// Returns the Month index
        /// </summary>
        public int DateOfMonthUTC
        {
            get
            {
                return this._timestampUTC.Day;
            }
        }
        /// <summary>
        /// Returns a string representation of Day of Week
        /// </summary>
        public string DayOfWeekUTC
        {
            get
            {
                return this.GetDayOfWeekString(this._timestampUTC);
            }
        }
        /// <summary>
        /// Returns the numeric month of the year
        /// </summary>
        public int MonthOfYearUTC
        {
            get
            {
                return this._timestampUTC.Month;
            }
        }
        /// <summary>
        /// Returns the Week of the year
        /// </summary>
        public int WeekOfYearUTC
        {
            get
            {
                return System.Globalization.ISOWeek.GetWeekOfYear(this._timestampUTC);
            }

        }
        /// <summary>
        /// Returns the week of the month
        /// </summary>
        public int WeekOfMonthUTC
        {
            get
            {
                return this.GetWeekOfMonth(_timestampUTC);
            }
        }
        #endregion

        #region Time Properities - EST (Eastern Standard Time)
        /// <summary>
        /// Internal calculation for timezone usage specific fields.  Excluded from final Index write.
        /// Coverts the timeStamp to the targetted timezone
        /// </summary>
        [Text(Ignore = true)]
        private DateTime _timestampEST
        {
            get
            {
                return this.ConvertTimestamp("Eastern Standard Time");
            }
        }
        /// <summary>
        /// Returns the hour of the day
        /// </summary>
        public int HourOfDayEST
        {
            get
            {
                return this._timestampEST.Hour;
            }
        }
        /// <summary>
        /// Returns the Month index
        /// </summary>
        public int DateOfMonthEST
        {
            get
            {
                return this._timestampEST.Day;
            }
        }
        /// <summary>
        /// Returns a string representation of Day of Week
        /// </summary>
        public string DayOfWeekEST
        {
            get
            {
                return this.GetDayOfWeekString(this._timestampEST);
            }
        }
        /// <summary>
        /// Returns the numeric month of the year
        /// </summary>
        public int MonthOfYearEST
        {
            get
            {
                return this._timestampEST.Month;
            }
        }
        /// <summary>
        /// Returns the Week of the year
        /// </summary>
        public int WeekOfYearEST
        {
            get
            {
                return System.Globalization.ISOWeek.GetWeekOfYear(this._timestampEST);
            }

        }
        /// <summary>
        /// Returns the week of the month
        /// </summary>
        public int WeekOfMonthEST
        {
            get
            {
                return this.GetWeekOfMonth(_timestampEST);
            }
        }
        #endregion

        #region Time Properities - PST (Pacific Standard Time)
        /// <summary>
        /// Internal calculation for timezone usage specific fields.  Excluded from final Index write.
        /// Coverts the timeStamp to the targetted timezone
        /// </summary>
        [Text(Ignore = true)]
        private DateTime _timestampPST
        {
            get
            {
                return this.ConvertTimestamp("Pacific Standard Time");
            }
        }
        /// <summary>
        /// Returns the hour of the day
        /// </summary>
        public int HourOfDayPST
        {
            get
            {
                return this._timestampPST.Hour;
            }
        }
        /// <summary>
        /// Returns the Month index
        /// </summary>
        public int DateOfMonthPST
        {
            get
            {
                return this._timestampPST.Day;
            }
        }
        /// <summary>
        /// Returns a string representation of Day of Week
        /// </summary>
        public string DayOfWeekPST
        {
            get
            {
                return this.GetDayOfWeekString(this._timestampPST);
            }
        }
        /// <summary>
        /// Returns the numeric month of the year
        /// </summary>
        public int MonthOfYearPST
        {
            get
            {
                return this._timestampPST.Month;
            }
        }
        /// <summary>
        /// Returns the Week of the year
        /// </summary>
        public int WeekOfYearPST
        {
            get
            {
                return System.Globalization.ISOWeek.GetWeekOfYear(this._timestampPST);
            }

        }
        /// <summary>
        /// Returns the week of the month
        /// </summary>
        public int WeekOfMonthPST
        {
            get
            {
                return this.GetWeekOfMonth(_timestampPST);
            }
        }
        #endregion

        #region Time Properities - CST (Central Standard Time)
        /// <summary>
        /// Internal calculation for timezone usage specific fields.  Excluded from final Index write.
        /// Coverts the timeStamp to the targetted timezone
        /// </summary>
        [Text(Ignore = true)]
        private DateTime _timestampCST
        {
            get
            {
                return this.ConvertTimestamp("Central Standard Time");
            }
        }
        /// <summary>
        /// Returns the hour of the day
        /// </summary>
        public int HourOfDayCST
        {
            get
            {
                return this._timestampCST.Hour;
            }
        }
        /// <summary>
        /// Returns the Month index
        /// </summary>
        public int DateOfMonthCST
        {
            get
            {
                return this._timestampCST.Day;
            }
        }
        /// <summary>
        /// Returns a string representation of Day of Week
        /// </summary>
        public string DayOfWeekCST
        {
            get
            {
                return this.GetDayOfWeekString(this._timestampCST);
            }
        }
        /// <summary>
        /// Returns the numeric month of the year
        /// </summary>
        public int MonthOfYearCST
        {
            get
            {
                return this._timestampCST.Month;
            }
        }
        /// <summary>
        /// Returns the Week of the year
        /// </summary>
        public int WeekOfYearCST
        {
            get
            {
                return System.Globalization.ISOWeek.GetWeekOfYear(this._timestampCST);
            }

        }
        /// <summary>
        /// Returns the week of the month
        /// </summary>
        public int WeekOfMonthCST
        {
            get
            {
                return this.GetWeekOfMonth(_timestampCST);
            }
        }
        #endregion

        #region Time Properities - MST (Mountain Standard Time)
        /// <summary>
        /// Internal calculation for timezone usage specific fields.  Excluded from final Index write.
        /// Coverts the timeStamp to the targetted timezone
        /// </summary>
        [Text(Ignore = true)]
        private DateTime _timestampMST
        {
            get
            {
                return this.ConvertTimestamp("Mountain Standard Time");
            }
        }
        /// <summary>
        /// Returns the hour of the day
        /// </summary>
        public int HourOfDayMST
        {
            get
            {
                return this._timestampMST.Hour;
            }
        }
        /// <summary>
        /// Returns the Month index
        /// </summary>
        public int DateOfMonthMST
        {
            get
            {
                return this._timestampMST.Day;
            }
        }
        /// <summary>
        /// Returns a string representation of Day of Week
        /// </summary>
        public string DayOfWeekMST
        {
            get
            {
                return this.GetDayOfWeekString(this._timestampMST);
            }
        }
        /// <summary>
        /// Returns the numeric month of the year
        /// </summary>
        public int MonthOfYearMST
        {
            get
            {
                return this._timestampMST.Month;
            }
        }
        /// <summary>
        /// Returns the Week of the year
        /// </summary>
        public int WeekOfYearMST
        {
            get
            {
                return System.Globalization.ISOWeek.GetWeekOfYear(this._timestampMST);
            }

        }
        /// <summary>
        /// Returns the week of the month
        /// </summary>
        public int WeekOfMonthMST
        {
            get
            {
                return this.GetWeekOfMonth(_timestampMST);
            }
        }
        #endregion

        #region Time Properities - AKST (Alaskan Standard Time)
        /// <summary>
        /// Internal calculation for timezone usage specific fields.  Excluded from final Index write.
        /// Coverts the timeStamp to the targetted timezone
        /// </summary>
        [Text(Ignore = true)]
        private DateTime _timestampAKST
        {
            get
            {
                return this.ConvertTimestamp("Alaskan Standard Time");
            }
        }
        /// <summary>
        /// Returns the hour of the day
        /// </summary>
        public int HourOfDayAKST
        {
            get
            {
                return this._timestampAKST.Hour;
            }
        }
        /// <summary>
        /// Returns the Month index
        /// </summary>
        public int DateOfMonthAKST
        {
            get
            {
                return this._timestampAKST.Day;
            }
        }
        /// <summary>
        /// Returns a string representation of Day of Week
        /// </summary>
        public string DayOfWeekAKST
        {
            get
            {
                return this.GetDayOfWeekString(this._timestampAKST);
            }
        }
        /// <summary>
        /// Returns the numeric month of the year
        /// </summary>
        public int MonthOfYearAKST
        {
            get
            {
                return this._timestampAKST.Month;
            }
        }
        /// <summary>
        /// Returns the Week of the year
        /// </summary>
        public int WeekOfYearAKST
        {
            get
            {
                return System.Globalization.ISOWeek.GetWeekOfYear(this._timestampAKST);
            }

        }
        /// <summary>
        /// Returns the week of the month
        /// </summary>
        public int WeekOfMonthAKST
        {
            get
            {
                return this.GetWeekOfMonth(_timestampAKST);
            }
        }
        #endregion

        #region Time Properities - HAST (Hawaii-Aleutian Standard Time)
        /// <summary>
        /// Internal calculation for timezone usage specific fields.  Excluded from final Index write.
        /// Coverts the timeStamp to the targetted timezone
        /// </summary>
        [Text(Ignore = true)]
        private DateTime _timestampHAST
        {
            get
            {
                return this.ConvertTimestamp("Hawaiian Standard Time");
            }
        }
        /// <summary>
        /// Returns the hour of the day
        /// </summary>
        public int HourOfDayHAST
        {
            get
            {
                return this._timestampHAST.Hour;
            }
        }
        /// <summary>
        /// Returns the Month index
        /// </summary>
        public int DateOfMonthHAST
        {
            get
            {
                return this._timestampHAST.Day;
            }
        }
        /// <summary>
        /// Returns a string representation of Day of Week
        /// </summary>
        public string DayOfWeekHAST
        {
            get
            {
                return this.GetDayOfWeekString(this._timestampHAST);
            }
        }
        /// <summary>
        /// Returns the numeric month of the year
        /// </summary>
        public int MonthOfYearHAST
        {
            get
            {
                return this._timestampHAST.Month;
            }
        }
        /// <summary>
        /// Returns the Week of the year
        /// </summary>
        public int WeekOfYearHAST
        {
            get
            {
                return System.Globalization.ISOWeek.GetWeekOfYear(this._timestampHAST);
            }

        }
        /// <summary>
        /// Returns the week of the month
        /// </summary>
        public int WeekOfMonthHAST
        {
            get
            {
                return this.GetWeekOfMonth(_timestampHAST);
            }
        }
        #endregion

        #region Time Properities - NST (Newfoundland Standard Time)
        /// <summary>
        /// Internal calculation for timezone usage specific fields.  Excluded from final Index write.
        /// Coverts the timeStamp to the targetted timezone
        /// </summary>
        [Text(Ignore = true)]
        private DateTime _timestampNST
        {
            get
            {
                return this.ConvertTimestamp("Newfoundland Standard Time");
            }
        }
        /// <summary>
        /// Returns the hour of the day
        /// </summary>
        public int HourOfDayNST
        {
            get
            {
                return this._timestampNST.Hour;
            }
        }
        /// <summary>
        /// Returns the Month index
        /// </summary>
        public int DateOfMonthNST
        {
            get
            {
                return this._timestampNST.Day;
            }
        }
        /// <summary>
        /// Returns a string representation of Day of Week
        /// </summary>
        public string DayOfWeekNST
        {
            get
            {
                return this.GetDayOfWeekString(this._timestampNST);
            }
        }
        /// <summary>
        /// Returns the numeric month of the year
        /// </summary>
        public int MonthOfYearNST
        {
            get
            {
                return this._timestampNST.Month;
            }
        }
        /// <summary>
        /// Returns the Week of the year
        /// </summary>
        public int WeekOfYearNST
        {
            get
            {
                return System.Globalization.ISOWeek.GetWeekOfYear(this._timestampNST);
            }

        }
        /// <summary>
        /// Returns the week of the month
        /// </summary>
        public int WeekOfMonthNST
        {
            get
            {
                return this.GetWeekOfMonth(_timestampNST);
            }
        }
    #endregion

        #region Time Properities - AST (Atlantic Standard Time)
        /// <summary>
        /// Internal calculation for timezone usage specific fields.  Excluded from final Index write.
        /// Coverts the timeStamp to the targetted timezone
        /// </summary>
        [Text(Ignore = true)]
        private DateTime _timestampAST
        {
            get
            {
                return this.ConvertTimestamp("Atlantic Standard Time");
            }
        }
        /// <summary>
        /// Returns the hour of the day
        /// </summary>
        public int HourOfDayAST
        {
            get
            {
                return this._timestampAST.Hour;
            }
        }
        /// <summary>
        /// Returns the Month index
        /// </summary>
        public int DateOfMonthAST
        {
            get
            {
                return this._timestampAST.Day;
            }
        }
        /// <summary>
        /// Returns a string representation of Day of Week
        /// </summary>
        public string DayOfWeekAST
        {
            get
            {
                return this.GetDayOfWeekString(this._timestampAST);
            }
        }
        /// <summary>
        /// Returns the numeric month of the year
        /// </summary>
        public int MonthOfYearAST
        {
            get
            {
                return this._timestampAST.Month;
            }
        }
        /// <summary>
        /// Returns the Week of the year
        /// </summary>
        public int WeekOfYearAST
        {
            get
            {
                return System.Globalization.ISOWeek.GetWeekOfYear(this._timestampAST);
            }

        }
        /// <summary>
        /// Returns the week of the month
        /// </summary>
        public int WeekOfMonthAST
        {
            get
            {
                return this.GetWeekOfMonth(_timestampAST);
            }
        }
        #endregion

        public float IsCallback { get; set; }
        public float IsAbandonedCallback { get; set; }
        /// <summary>
        /// Used to track a Empty PSAP case into a call sequence with populated PSAP data, termed a "alternative Route"
        /// </summary>
        /// <remarks>https://solacomtech.atlassian.net/browse/INFO-1366</remarks>
        public float IsAlternativeRoute { get; set; }

        /////**DIFFERENT FROM SOURCE**- commented out as not required / part of the ES index + did not need to include the additional objects. **//
        //public List<OutboundCallDetails> OutboundCalls { get; set; }
        [GeoPoint(Name = "location")]
        public CallLocation Location { get; set; }

        public double? TotalCallTimeInSeconds { get; set; }
        public double? NonEmergencyTotalCallTimeInSeconds { get; set; }

        public Guid CallDetailsId { get; set; }
        public string PsapName { get; set; }
        public string LoginId { get; set; }
        public DateTime? CallArrived { get; set; }
        public string AgentName { get; set; }
        public string Address { get; set; }
        public string Zipcode { get; set; }
        public double? Esn { get; set; }
        public DateTime? CallPresented { get; set; }
        public DateTime? CallAnswered { get; set; }
        public DateTime? CallReleased { get; set; }
        public bool? AnsweredBySystem { get; set; }
        public DateTime CallDetailsTimeStamp { get; set; }
        public bool? IsAbandoned { get; set; }
        public float AgentEmergencyCall { get; set; }
        public float AgentAdminCall { get; set; }
        public float AgentTandemCall { get; set; }
        public float AgentAdminEmergencyCall { get; set; }
        public float AgentUnknownCall { get; set; }

        public float AgentInProgressState { get; set; }
        public float AgentCompletedState { get; set; }
        public float AgentAbandonedState { get; set; }

        public float AgentLandlineType { get; set; }
        public float AgentWirelessType { get; set; }
        public float AgentVoipType { get; set; }
        public float AgentUnknownType { get; set; }
        public float AgentSMSType { get; set; }
        public float AgentRTTType { get; set; }
        public float AgentTDDType { get; set; }
        public float AgentTDDChallengeOnly { get; set; }
        public float AgentNotFoundType { get; set; }
        public bool EventsNotCompleted { get; set; }

        public string TransferFrom { get; set; }
        public string TransferTo { get; set; }

        /// <summary>
        /// Stores if the transfer event is a internal transfer.
        /// </summary>
        /// <remarks>
        /// Calculated based on if TransferFrom and Psap are equal (when populated)
        /// </remarks>
        public float IsInternalTransferCall { get; set; }

        /// <summary>
        /// Stores the time it takes for the Agent to transfer the call
        /// </summary>
        /// <remarks>
        /// Calculated based on CallAnswered to CallTransferred based on the transfer leg of the call.
        /// </remarks>
        public double? TimeToTransferInSeconds { get; set; }
        /// <summary>
        /// Stores the time that the transferred event occured.  
        /// </summary>
        /// <remarks>
        /// Based the timestamp of the Event type = 'TransferCall'
        /// </remarks>
        public DateTime? CallTransferred { get; set; }

        /// <summary>
        /// Date of when the given Call Summary was processed / added to the data instance.  Set in UTC.
        /// </summary>
        public DateTime? ProcessedTime
        {
            get
            {
                return DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Performs a Clone of the full object 
        /// </summary>
        /// <returns>New instance of the Call Summary object</returns>
        public CallSummary ShallowClone()
        {
            return this.MemberwiseClone() as CallSummary;
        }

        public object Clone()
        {
            return new CallSummary
            {
                
                Callid = Callid,
                Incidentid = Incidentid,
                Starttime = Starttime,
                Endtime = Endtime,
                TimeStamp = DateTime.UtcNow,
                Ani = Ani,
                Callbacknumber = Callbacknumber,

                CallDetailsId = Guid.NewGuid(),
                PsapName = PsapName,
                LoginId = LoginId,
                AgentName = AgentName,

                CallDetailsTimeStamp = CallDetailsTimeStamp,

                IsAdmin = IsAdmin,
                IsTandem = IsTandem,
                IsAdminEmergency = IsAdminEmergency,
                IsUnknownType = IsUnknownType,
                IsEmergency = IsEmergency,
                IsTransferred = IsTransferred,

                EmergencyCall = EmergencyCall,
                AdminCall = AdminCall,
                TandemCall = TandemCall,
                AdminEmergencyCall = AdminEmergencyCall,
                UnknownCall = UnknownCall,
                AgentEmergencyCall = AgentEmergencyCall,
                AgentAdminCall = AgentAdminCall,
                AgentTandemCall = AgentTandemCall,
                AgentAdminEmergencyCall = AdminEmergencyCall,
                AgentUnknownCall = AgentUnknownCall,
                Calltype = Calltype,
        };
        }
    }

public class CallLocation
{
    public CallLocation(double lat, double lon)
    {
        Lat = lat;
        Lon = lon;
    }
    [Number(NumberType.Double, Name = "lat")]
    public double Lat { get; set; }
    [Number(NumberType.Double, Name = "lon")]
    public double Lon { get; set; }
}

/// <summary>
/// Limited data object to allow for ease of usage specifically for data Validation.
/// </summary>
public class CallSummaryLimited
{
    public DateTime TimeStamp { get; set; }
    public string Callid { get; set; }
    public Guid CallDetailsId { get; set; }
    /// <summary>
    /// Used to track a Empty PSAP case into a call sequence with populated PSAP data, termed a "alternative Route"
    /// </summary>
    /// <remarks>Required to skip over when doing the Root Summary check.</remarks>
    public float IsAlternativeRoute { get; set; }

    /// <summary>
    /// Determines if a given Call Summary is a Root element.  
    /// </summary>
    public bool IsRootElement
    {
        get
        {
            return (CallDetailsId.ToString() == "00000000-0000-0000-0000-000000000000");
        }
    }

}