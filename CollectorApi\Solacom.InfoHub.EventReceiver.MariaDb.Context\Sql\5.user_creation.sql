﻿/** 
	Created: Aug 2021
	Author: <PERSON>
	Generates the User if they do not exists.  Granting required permissions.  NOTE: non destructive - can be rerun.
**/
/** GRANT EXECUTE ON PROCEDURE GuardianInsights.PROCEDURENAME TO 'Guardian_User'@'%'; 
$$
**/

GRANT SELECT ON CollectorAPI.processstate TO 'Collector_User'@'%' IDENTIFIED BY 'SolaCom';
GRANT INSERT, SELECT, UPDATE, DELETE ON CollectorAPI.events TO 'Collector_User'@'%' IDENTIFIED BY 'SolaCom';
GRANT INSERT, SELECT, UPDATE, DELETE ON CollectorAPI.hashedevents TO 'Collector_User'@'%' IDENTIFIED BY 'SolaCom';
GRANT INSERT, SELECT, UPDATE, DELETE ON CollectorAPI.agentsession TO 'Guardian_User'@'%' IDENTIFIED BY 'SolaCom';
GRANT INSERT, SELECT, UPDATE, DELETE ON CollectorAPI.processerror TO 'Guardian_User'@'%' IDENTIFIED BY 'SolaCom';
GRANT INSERT, SELECT, UPDATE, DELETE ON CollectorAPI.processqueue TO 'Guardian_User'@'%' IDENTIFIED BY 'SolaCom';

GRANT Execute ON CollectorAPI.* TO 'Collector_User'@'%';

GRANT INSERT, SELECT, UPDATE, DELETE ON InsightsData.callevent TO 'Collector_User'@'%' IDENTIFIED BY 'SolaCom';
GRANT INSERT, SELECT, UPDATE, DELETE ON InsightsData.callsummary TO 'Collector_User'@'%' IDENTIFIED BY 'SolaCom';

GRANT Execute ON InsightsData.* TO 'Collector_User'@'%';
