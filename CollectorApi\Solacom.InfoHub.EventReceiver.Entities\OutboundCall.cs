﻿using System.Xml.Serialization;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    [XmlRoot(ElementName = "outboundCall")]
    public class OutboundCall
    {
        [XmlElement(ElementName = "outboundTarget")]
        public string OutboundTarget { get; set; }
        [XmlElement(ElementName = "rule")]
        public string Rule { get; set; }
        [XmlElement(ElementName = "reason")]
        public string Reason { get; set; }
        [XmlElement(ElementName = "mediaLabel")]
        public string MediaLabel { get; set; }
        [XmlElement(ElementName = "attempt")]
        public string Attempt { get; set; }
        [XmlElement(ElementName = "priority")]
        public string Priority { get; set; }
        [XmlElement(ElementName = "ani")]
        public string Ani { get; set; }
        [XmlElement(ElementName = "aniDomain")]
        public string AniDomain { get; set; }
        [XmlElement(ElementName = "dnis")]
        public string Dnis { get; set; }
        [XmlElement(ElementName = "pani")]
        public string Pani { get; set; }
        [XmlElement(ElementName = "callerName")]
        public string CallerName { get; set; }
        [XmlElement(ElementName = "aniTranslated")]
        public string AniTranslated { get; set; }
        [XmlElement(ElementName = "dnisTranslated")]
        public string DnisTranslated { get; set; }
        [XmlElement(ElementName = "callerNameTranslated")]
        public string CallerNameTranslated { get; set; }
        [XmlElement(ElementName = "method")]
        public string Method { get; set; }
        [XmlElement(ElementName = "targetType")]
        public string TargetType { get; set; }
        [XmlElement(ElementName = "targetName")]
        public string TargetName { get; set; }
    }
}
