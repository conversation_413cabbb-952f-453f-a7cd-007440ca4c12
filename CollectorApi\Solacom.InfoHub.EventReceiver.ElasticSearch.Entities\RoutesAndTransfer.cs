﻿using System;

namespace Solacom.InfoHub.EventReceiver.ElasticSearch.Entities
{
    public class RoutesAndTransfer
    {
        public Guid Id { get; set; }
        public string Agency { get; set; }
        public string Agent { get; set; }
        public string Phone { get; set; }
        public int? Attemptnumber { get; set; }
        public int? Callpriority { get; set; }
        public bool IsAnswered { get; set; }
        public bool IsTransfer { get; set; }
        public DateTime TimeStamp { get; set; }
    }
}