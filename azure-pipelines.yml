# ASP.NET Core (.NET Framework)
# Build and test ASP.NET Core projects targeting the full .NET Framework.
# Add steps that publish symbols, save build artifacts, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/dotnet-core

trigger:
  branches:
    include:
    - master

pool:
  name: InfoHub

variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'

steps:
- task: NuGetToolInstaller@1
  displayName: 'NuGet Tool Installer'

- task: NuGetCommand@2
  displayName: 'Restoring solution'
  inputs:
    restoreSolution: '$(solution)'

- task: VSBuild@1
  displayName: 'Building Applicaton'
  inputs:
    solution: '$(solution)'
    msbuildArgs: '/p:DeployOnBuild=true /p:WebPublishMethod=Package /p:PackageAsSingleFile=true /p:SkipInvalidConfigurations=true /p:DesktopBuildPackageLocation="$(build.artifactStagingDirectory)\\" /p:DeployIisAppPath="Default Web Site"'
    platform: '$(buildPlatform)'
    configuration: '$(buildConfiguration)'

- task: DotNetCoreCLI@2
  displayName: 'Running UnitTests'
  inputs:
    command: test
    projects: '**/*Tests/*.csproj'
    arguments: '--configuration $(buildConfiguration)'

- task: DotNetCoreCLI@2
  displayName: 'Collecting Code Coverage'
  inputs:
    command: test
    projects: '**/*Tests/*.csproj'
    arguments: '--configuration $(buildConfiguration) --collect "Code coverage"'

- task: Docker@2
  displayName: 'Pushing Docker Image to AWS Elastic Container Registry'
  inputs:
    containerRegistry: 'ECR Connection'
    repository: 'infohub/api'
    command: 'buildAndPush'
    Dockerfile: '**/Dockerfile'
    buildContext: 'CollectorApi'
    tags: 'latest'

- task: CopyFiles@2
  displayName: 'Copy Files to: $(build.artifactStagingDirectory)'
  inputs:
    SourceFolder: '$(build.SourcesDirectory)'
    Contents: |
      **\bin\$(buildConfiguration)\**
      src\*.testsettings
    TargetFolder: '$(build.artifactStagingDirectory)'

- task: DotNetCoreCLI@2
  displayName: 'Publish Artifact'
  inputs:
    command: 'publish'
    publishWebProjects: true
    arguments: '-c $(buildConfiguration) -o $(build.artifactStagingDirectory)'
    workingDirectory: '$(System.DefaultWorkingDirectory)'

- task: PublishSymbols@2
  displayName: 'Publish Symbols Path:'
  inputs:
    SearchPattern: '**/bin/**/*.pdb'
  continueOnError: true

- task: BeanstalkDeployApplication@1
  displayName: 'Push API to AWS Elastic BeanStalk'
  inputs:
    awsCredentials: 'AWS Connection'
    regionName: 'us-east-1'
    applicationName: 'InfoHub'
    environmentName: 'CollectorAPI'
    applicationType: 'aspnet'
    webDeploymentArchive: '$(build.artifactStagingDirectory)\Solacom.InfoHub.EventReceiver.Web.API.zip'
    versionLabel: '$(Build.BuildNumber)'
