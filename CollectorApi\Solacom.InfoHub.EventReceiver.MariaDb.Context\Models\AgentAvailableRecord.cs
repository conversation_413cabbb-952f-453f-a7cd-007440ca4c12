﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Solacom.InfoHub.EventReceiver.MariaDb.Context.Models
{
    public class AgentAvailableRecord
    {
        public int Id { get; set; }
        public int AgentSessionRecordId { get; set; }
        public DateTime? StartAvailableInterval { get; set; }
        public DateTime? EndAvailableInterval { get; set; }
        public float? IntervalTimeInMinutes { get; set; }
    }
}
