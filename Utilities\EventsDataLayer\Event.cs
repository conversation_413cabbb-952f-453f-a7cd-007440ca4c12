namespace EventsDataLayer
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Data.Entity.Spatial;

    public partial class Event
    {
        public int Id { get; set; }

        [Column(TypeName = "xml")]
        [Required]
        public string XmlContent { get; set; }

        [StringLength(200)]
        public string CallId { get; set; }

        public DateTime? TimeStamp { get; set; }

        [Required]
        [StringLength(20)]
        public string ClientCode { get; set; }

        public bool? IsLastPushSuccess { get; set; }

        public DateTime? LastPushTimeStamp { get; set; }

        public string LastPushMessage { get; set; }
    }
}
