﻿using System.Xml.Serialization;
using Nest;
using Newtonsoft.Json;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    [XmlRoot(ElementName = "startCall")]
    public class StartCall
    {
        [XmlElement(ElementName = "header")]
        public string Header { get; set; }
        /// <summary>
        /// 
        /// </summary>
        /// <remarks>Location is defined as a string, mapping to '.' when empty based on the spec.
        /// Original code mapped to the HeldResponse.Location object (removed on this update), sub mapped to Presense object. (i.e. Location.Presense.*)
        /// Ignore was added to not write the node to the EventLog Index to avoid Event template issues.  If we start to process this node, this will need to be revisited.
        /// [XMLIgnore] is used to make sure the node isn't parsed fully on serialization.
        /// </remarks>
        [XmlElement(ElementName = "location")]
        [Ignore]
        [XmlIgnore]
        [JsonIgnore]
        public string Location { get; set; }
        [XmlElement(ElementName = "mediaLabel")]
        public string MediaLabel { get; set; }
        [XmlElement(ElementName = "incomingCallPolicy")]
        public string IncomingCallPolicy { get; set; }
        [XmlElement(ElementName = "callType")]
        public string CallType { get; set; }
        [XmlElement(ElementName = "signallingType")]
        public string SignallingType { get; set; }
        [XmlElement(ElementName = "circuit")]
        public string Circuit { get; set; }
        [XmlElement(ElementName = "circuitId")]
        public string CircuitId { get; set; }
        [XmlElement(ElementName = "trunkGroupId")]
        public string TrunkGroupId { get; set; }
        [XmlElement(ElementName = "ani")]
        public string Ani { get; set; }
        [XmlElement(ElementName = "aniDomain")]
        public string AniDomain { get; set; }
        [XmlElement(ElementName = "dnis")]
        public string Dnis { get; set; }
        [XmlElement(ElementName = "dnisDomain")]
        public string DnisDomain { get; set; }
        [XmlElement(ElementName = "pani")]
        public string Pani { get; set; }
        [XmlElement(ElementName = "esrn")]
        public string Esrn { get; set; }
        [XmlElement(ElementName = "callerName")]
        public string CallerName { get; set; }
        [XmlElement(ElementName = "concurrentCalls")]
        public string ConcurrentCalls { get; set; }
    }
}
