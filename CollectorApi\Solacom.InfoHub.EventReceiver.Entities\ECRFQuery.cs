﻿using System.Xml.Serialization;
using Nest;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    [XmlRoot(ElementName = "ecrfQuery")]
    public class EcrfQuery
    {
        [XmlElement(ElementName = "mediaLabel")]
        public string MediaLabel { get; set; }
        [XmlElement(ElementName = "ecrfDomain")]
        public string EcrfDomain { get; set; }
        [XmlElement(ElementName = "service-urn")]
        public string Serviceurn { get; set; }
        [XmlElement(ElementName = "ecrfPurpose")]
        public string EcrfPurpose { get; set; }
        
        /// <summary>
        /// Stores additional location information - currently being excluded from the data parsing.
        /// </summary>
        /// <remarks>
        /// Example XML structure:
        /// <location id="_CI_18637347088C000000AA" profile="geodetic-2d">
        ///  <location xmlns:gml="http://www.opengis.net/gml" xmlns:ca="urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr" xmlns:ns7="urn:ietf:params:xml:ns:pidf:geopriv10:basicPolicy" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:ns4="urn:ietf:params:xml:ns:pidf" xmlns:ns8="http://www.opengis.net/pidflo/1.0" xmlns="urn:ietf:params:xml:ns:pidf:geopriv10" xmlns:ns5="urn:ietf:params:xml:ns:pidf:data-model" xmlns:ns9="urn:ietf:params:xml:ns:geopriv:held:id" xmlns:ns6="urn:ietf:params:xml:ns:pidf:geopriv10" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
        ///    <gml:Point srsName = "urn:ogc:def:crs:EPSG::4326" >
        ///      < gml:pos>28.113055 -81.64747</gml:pos>
        ///    </gml:Point>
        ///  </location>
        ///</location>
        /// </remarks>
        [XmlElement(ElementName = "location")]
        [XmlIgnore]
        [Ignore]
        public string Location { get; set; }
    }
}
