﻿using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Serilog;
using Serilog.Enrichers.AspnetcoreHttpcontext;
using System;
using System.IO;
using Amazon.SecretsManager;
using Amazon;
using Amazon.SecretsManager.Model;
using System.Collections.Generic;

namespace Solacom.InfoHub.EventReceiver.Web.API
{
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateWebHostBuilder(args).Build().Run();
        }

        public static IWebHostBuilder CreateWebHostBuilder(string[] args) =>
            WebHost.CreateDefaultBuilder(args)
                //.ConfigureAppConfiguration((hostingContext, config) =>
                //{
                //    var envName = hostingContext.HostingEnvironment.EnvironmentName.ToString().ToLower();
                //    config.AddSystemsManager(configureSource =>
                //    {
                //        configureSource.Path = $"/{envName}/collectorapi";
                //        configureSource.ReloadAfter = TimeSpan.FromMinutes(1440);
                //        configureSource.OnLoadException += exceptionContext =>
                //        {
                //            exceptionContext.Ignore = true;
                //        };
                //    });
                //})
                .ConfigureAppConfiguration((hostingContext, config) =>
                {
                    var region = Environment.GetEnvironmentVariable("aws_region");
                    var awsEnv = Environment.GetEnvironmentVariable("aws_environment");
                    var awsCountry = Environment.GetEnvironmentVariable("aws_country");
                    var secretPath = $"{awsEnv}/{awsCountry}/collectorapi/configuration";
                    if (!string.IsNullOrWhiteSpace(region))
                    {
                        config.AddSecretsManager(
                            region: RegionEndpoint.GetBySystemName(region),
                            configurator:
                            options =>
                                {
                                    options.KeyGenerator = (_, secretName) => secretName.Replace($"{secretPath}:", "");
                                    options.ListSecretsFilters.Add(new Filter { Key = FilterNameStringType.Name, Values = { secretPath } });
                                    options.PollingInterval = TimeSpan.FromHours(4);
                                }
                            );
                    }         
                })
                .UseSerilog((provider, hostingContext, loggerConfiguration) =>
                {
                    loggerConfiguration.ReadFrom.Configuration(hostingContext.Configuration)
                        .Enrich.WithAspnetcoreHttpcontext(provider);
                }).UseKestrel()
                .UseContentRoot(Directory.GetCurrentDirectory())
                .UseIISIntegration()
                .UseStartup<Startup>();
    }
}
