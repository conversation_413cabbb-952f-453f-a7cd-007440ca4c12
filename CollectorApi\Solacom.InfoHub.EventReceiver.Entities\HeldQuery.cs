﻿using System.Xml.Serialization;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    [XmlRoot(ElementName = "heldQuery")]
    public class HeldQuery
    {
        [XmlElement(ElementName = "mediaLabel")]
        public string MediaLabel { get; set; }
        [XmlElement(ElementName = "heldDomain")]
        public string HeldDomain { get; set; }
        [XmlElement(ElementName = "heldPurpose")]
        public string HeldPurpose { get; set; }
        [XmlElement(ElementName = "held-uri")]
        public string Helduri { get; set; }
    }
}
