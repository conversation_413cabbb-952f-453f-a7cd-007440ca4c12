﻿using System;
using System.Collections.Generic;
using Nest;
namespace Solacom.InfoHub.EventReceiver.ElasticSearch.Entities
{
    public class CallSummary : ICloneable
    {
        public CallSummary()
        {
            SystemAnsweredWithin10s =
                SystemAnsweredWithin15s =
                    SystemAnsweredWithin40s =
                        SystemAnsweredMoreThan40s = 0;

            LocationDataList = new List<LocationData>();
        }

        /// <summary>
        /// Stores the Elasticsearch Index the Call Summary is stored in
        /// </summary>
        /// <remarks>Set to be ignored on document write to ES</remarks>
        [Text(Ignore = true)]
        public string ElasticIndex { get; set; }

        public string MediaLabel { get; set; }
        public string CallIdentifier { get; set; }
        public string IncidentIdentifier { get; set; }
        public DateTime? StartCallTime { get; set; }
        public DateTime? StartCallTimeToLocal { get; set; }
        public DateTime? EndCallTime { get; set; }
        public DateTime? EndCallTimeToLocal { get; set; }
        public DateTime TimeStamp { get; set; }
        public DateTime TimeStampToLocal { get; set; }
        public string CallBackNumber { get; set; }
        public string AgentCallbacknumber { get; set; }
        public string CallType { get; set; }
        public string CallState { get; set; }
        public bool IsAdmin { get; set; }
        public bool IsTandem { get; set; }
        public bool IsAdminEmergency { get; set; }
        public int IsUnknownType { get; set; }
        public bool IsEmergency { get; set; }
        public string Id { get; set; }
        public string OriginalCos { get; set; }
        public string FinalCos { get; set; }
        public string Carrier { get; set; }
        public string CallMobilityType { get; set; }
        public int IsTransferred { get; set; }
        public int IsOutbound { get; set; }
        public int EmergencyCall { get; set; }
        public int AdminCall { get; set; }
        public int TandemCall { get; set; }
        public int AdminEmergencyCall { get; set; }
        public int UnknownCall { get; set; }
        public int InProgress { get; set; }
        public int IsCompleted { get; set; }
        public int AbandonedState { get; set; }

        public int LandlineType { get; set; }
        public int WirelessType { get; set; }
        public int VoipType { get; set; }
        public int SMSType { get; set; }
        public int RTTType { get; set; }
        public int TDDType { get; set; }
        public int TDDChallenge { get; set; }
        public int NotFoundType { get; set; }
        public double? Uncertainty { get; set; }
        public double? Confidence { get; set; }
        public double? TimeToAnswerInSeconds { get; set; }
        public double? NonEmergencyTimeToAnswerInSeconds { get; set; }

        public double? HoldTimeInSeconds { get; set; }
        public double? NonEmergencyHoldTimeInSeconds { get; set; }

        public double? TalkTimeInSeconds { get; set; }
        public double? NonEmergencyTalkTimeInSeconds { get; set; }
        public int? SystemAnsweredWithin10s { get; set; }
        public int? NonEmergencyAnsweredWithin10s { get; set; }
        public int? SystemAnsweredMoreThan10s { get; set; }
        public int? NonEmergencyAnsweredMoreThan10s { get; set; }
        public int? SystemAnsweredWithin15s { get; set; }
        public int? NonEmergencyAnsweredWithin15s { get; set; }
        public int? SystemAnsweredWithin20s { get; set; }
        public int? NonEmergencyAnsweredWithin20s { get; set; }
        public int? SystemAnsweredMoreThan20s { get; set; }
        public int? NonEmergencyAnsweredMoreThan20s { get; set; }
        public int? SystemAnsweredWithin40s { get; set; }
        public int? NonEmergencyAnsweredWithin40s { get; set; }
        public int? SystemAnsweredMoreThan40s { get; set; }
        public int? NonEmergencyAnsweredMoreThan40s { get; set; }

        public double? PsapTimeToAnswerInSeconds { get; set; }
        public double? NonEmergencyPsapTimeToAnswerInSeconds { get; set; }

        public double? AgentTimeToAnswerInSeconds { get; set; }
        public int? AgentAnsweredWithin10s { get; set; }
        public int? AgentAnsweredMoreThan10s { get; set; }
        public int? AgentAnsweredWithin15s { get; set; }
        public int? AgentAnsweredWithin20s { get; set; }
        public int? AgentAnsweredMoreThan20s { get; set; }
        public int? AgentAnsweredWithin40s { get; set; }
        public int? AgentAnsweredMoreThan40s { get; set; }
        
        public int IsCallback { get; set; }
        public int IsAbandonedCallback { get; set; }
        /// <summary>
        /// Used to track a Empty PSAP case into a call sequence with populated PSAP data, termed a "alternative Route"
        /// </summary>
        /// <remarks>https://solacomtech.atlassian.net/browse/INFO-1366</remarks>
        public int IsAlternativeRoute { get; set; }
        
        [GeoPoint(Name = "location")]
        public CallLocation Location { get; set; }

        public double? TotalCallTimeInSeconds { get; set; }
        public double? NonEmergencyTotalCallTimeInSeconds { get; set; }

        public int CallDetailsIndex { get; set; }
        public string PsapName { get; set; }
        public DateTime? CallArrivedSystem { get; set; }
        public DateTime? CallArrivedSystemToLocal { get; set; }
        public string AgentName { get; set; }
        public string Address { get; set; }
        public string Zipcode { get; set; }
        public double? Esn { get; set; }
        public DateTime? CallPresented { get; set; }
        public DateTime? CallPresentedToLocal { get; set; }
        public DateTime? CallAnswered { get; set; }
        public DateTime? CallAnsweredToLocal { get; set; }
        public DateTime? CallReleased { get; set; }
        public DateTime? CallReleasedToLocal { get; set; }
        public int? AnsweredBySystem { get; set; }
        public bool? IsAbandoned { get; set; }
        public string TransferFrom { get; set; }
        public string TransferTo { get; set; }
        /// <summary>
        /// Stores if the transfer event is a internal transfer.
        /// </summary>
        /// <remarks>
        /// Calculated based on if TransferFrom and Psap are equal (when populated)
        /// </remarks>
        public int IsInternalTransferCall { get; set; }

        /// <summary>
        /// Stores the time it takes for the Agent to transfer the call
        /// </summary>
        /// <remarks>
        /// Calculated based on CallAnswered to CallTransferred based on the transfer leg of the call.
        /// </remarks>
        public double? TimeToTransferInSeconds { get; set; }
        /// <summary>
        /// Stores the time that the transferred event occured.  
        /// </summary>
        /// <remarks>
        /// Based the timestamp of the Event type = 'TransferCall'
        /// </remarks>
        public DateTime? CallTransferred { get; set; }
        public DateTime? CallTransferredToLocal { get; set; }

        /// <summary>
        /// Date of when the given Call Summary was processed / added to the data instance.  Set in UTC.
        /// </summary>
        public DateTime? ProcessedTime
        {
            get
            {
                return DateTime.UtcNow;
            }
        }

        public List<LocationData> LocationDataList { get; set; }


        /// <summary>
        /// Performs a Clone of the full object 
        /// </summary>
        /// <returns>New instance of the Call Summary object</returns>
        public CallSummary ShallowClone()
        {
            return this.MemberwiseClone() as CallSummary;
        }

        public object Clone()
        {
            return new CallSummary
            {

                CallIdentifier = CallIdentifier,
                IncidentIdentifier = IncidentIdentifier,
                StartCallTime = StartCallTime,
                EndCallTime = EndCallTime,
                TimeStamp = DateTime.UtcNow,
                CallBackNumber = CallBackNumber,

                PsapName = PsapName,
                AgentName = AgentName,

                IsAdmin = IsAdmin,
                IsTandem = IsTandem,
                IsAdminEmergency = IsAdminEmergency,
                IsUnknownType = IsUnknownType,
                IsEmergency = IsEmergency,
                IsTransferred = IsTransferred,
                IsAlternativeRoute = IsAlternativeRoute,

                EmergencyCall = EmergencyCall,
                AdminCall = AdminCall,
                TandemCall = TandemCall,
                AdminEmergencyCall = AdminEmergencyCall,
                CallType = CallType,
                LocationDataList = LocationDataList 
        };
        }
    }
}