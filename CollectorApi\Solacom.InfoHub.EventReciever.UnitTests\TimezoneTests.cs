using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using TimezoneHelper = Solacom.InfoHub.EventReceiver.BusinessLogic.TimezoneHelper;


namespace Solacom.InfoHub.EventReciever.UnitTests
{
    /// <summary>
    /// Test cases around specific EziNet functionality / ADR/EIDD
    /// </summary>
    [TestClass]
    public class TimezoneTests
    {
        [TestMethod]
        public void CoreCheckUTC()
        {
            DateTime input = new DateTime(2000, 06, 10, 12, 0, 0);

            NodaTime.DateTimeZone timezone = NodaTime.DateTimeZoneProviders.Tzdb["Etc/UTC"];

            DateTime result = TimezoneHelper.GetTimeAsLocal(input, timezone);
 
            Assert.IsTrue(input.Hour == result.Hour);
        }


        [TestMethod]
        public void CheckEST()
        {
            DateTime input = new DateTime(2023, 02, 06, 12, 0, 0);

            NodaTime.DateTimeZone timezone = NodaTime.DateTimeZoneProviders.Tzdb["America/New_York"];

            DateTime result = TimezoneHelper.GetTimeAsLocal(input, timezone);

            Assert.IsTrue((result.Hour - input.Hour) == -5 );
        }

        [TestMethod]
        public void CheckEST_DST()
        {
            DateTime input = new DateTime(2023, 08, 06, 12, 0, 0);

            NodaTime.DateTimeZone timezone = NodaTime.DateTimeZoneProviders.Tzdb["America/New_York"];

            DateTime result = TimezoneHelper.GetTimeAsLocal(input, timezone);

            Assert.IsTrue((result.Hour - input.Hour) == -4);
        }

    }
}