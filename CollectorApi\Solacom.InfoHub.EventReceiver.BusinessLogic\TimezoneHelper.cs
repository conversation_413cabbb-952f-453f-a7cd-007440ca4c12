﻿using System;
using NodaTime;

namespace Solacom.InfoHub.EventReceiver.BusinessLogic
{
    public class TimezoneHelper
    {
        /// <summary>
        /// Shifts a given Datetime from UTC to targetted TimeZone
        /// </summary>
        /// <param name="sourceDateTime">DateTime to shift</param>
        /// <param name="customerTimezone">Defined NodaTime DateTimeZone</param>
        /// <returns>Shifted datetime</returns>
        /// <remarks>definition of available timezones can be found at https://nodatime.org/TimeZones </remarks>
        public static DateTime GetTimeAsLocal(DateTime sourceDateTime, DateTimeZone customerTimezone)
        {
            DateTime now = DateTime.UtcNow;
            string fromZoneId = "Etc/UTC";

            //string toZoneId = "America/New_York";
            //DateTimeZone to_zone = NodaTime.DateTimeZoneProviders.Tzdb[toZoneId];

            NodaTime.DateTimeZone from_zone = NodaTime.DateTimeZoneProviders.Tzdb[fromZoneId];
            LocalDateTime from_local = NodaTime.LocalDateTime.FromDateTime(sourceDateTime);
            ZonedDateTime from_datetime = from_zone.AtStrictly(from_local);

            ZonedDateTime toLocalDateTime = from_datetime.WithZone(customerTimezone);

            return new DateTime(toLocalDateTime.Year, toLocalDateTime.Month, toLocalDateTime.Day, toLocalDateTime.Hour, toLocalDateTime.Minute, toLocalDateTime.Second, toLocalDateTime.Millisecond);
        }
    }
}