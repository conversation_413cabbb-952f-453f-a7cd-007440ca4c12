resource "aws_vpc" "main" {
  cidr_block       = "${var.vpc_cidr_block}"
  instance_tenancy = "${var.vpc_instance_tenancy}"

  tags  = {
    Environment = "${terraform.workspace}"
    Name        = "${var.vpc_tag_name}-${terraform.workspace}"
  }
}


resource "aws_subnet" "main" {
  vpc_id     = "${var.vpc_id}"
  cidr_block = "${var.vpc_subnet_cidr_block}"

  tags  = {
    Environment = "${terraform.workspace}"
    Name        = "${var.subnet_tag_name}-${terraform.workspace}"

  }
}
