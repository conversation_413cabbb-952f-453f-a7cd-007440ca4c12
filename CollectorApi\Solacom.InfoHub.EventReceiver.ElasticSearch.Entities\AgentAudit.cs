﻿using System;
using System.Collections.Generic;
using Nest;
namespace Solacom.InfoHub.EventReceiver.ElasticSearch.Entities
{
    public class AgentAudit
    {
        public float? dutyTimeMinutes { get; set; }
        public float? availableTimeMinutes { get; set; }
        public float? busiedOutTimeMinutes { get; set; }
        public string MediaLabel { get; set; }
        public DateTime? Logintime { get; set; }
        public DateTime? Logouttime { get; set; }
        public DateTime TimeStamp { get; set; }
        public int? isOnDuty { get; set; }
        public int? isAvailable { get; set; }
        public List<AvailableIntervalLog> AvailableIntervalLogs { get; set; }
        public List<BusiedoutIntervalLog> BusiedoutIntervalLogs { get; set; }
        public string PsapName { get; set; }
        public string AgentName { get; set; }
        public string Uri { get; set; }
        public string AgentRole { get; set; }
        public string TenantGroup { get; set; }
        public string OperatorId { get; set; }
        public string Workstation { get; set; }
        public string Devicename { get; set; }
        public string ResponseCode { get; set; }
        public string Reason { get; set; }
        public Guid Id { get; set; }
    }
}