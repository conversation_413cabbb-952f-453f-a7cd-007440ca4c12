﻿using System;
using System.Text;

namespace Solacom.InfoHub.EventReceiver.BusinessLogic
{
    public static class ExceptionFormatter
    {
        public static string FormatException(this Exception e)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"Message: {e.Message}, StackTrace: {e.StackTrace}");

            var inner = e.InnerException;

            while (inner != null)
            {
                sb.AppendLine($"InnerExceptionMessage: {inner.Message}, InnerExceptionStackTrace: {inner.StackTrace}");
                inner = inner.InnerException;
            }

            return sb.ToString();
        }
    }
}