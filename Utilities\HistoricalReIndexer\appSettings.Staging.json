﻿{
    "Logging": {
        "LogLevel": {
            "Default": "Debug",
            "System": "Warning",
            "Microsoft": "Warning"
        }
    },
    "Serilog": {
        "MinimumLevel": {
            "Default": "Debug",
            "Override": {
                "Microsoft": "Warning",
                "System": "Warning"
            }
        },
        "Properties": { "ApplicationName": "CollectorAPI" }
    },
    "elasticsearchSettings": {
        "url": "https://6d21a09259f540938662d88d2f3ec2f0.us-east-1.aws.found.io:9243",
        "userName": "elastic",
        "password": "Ug8l4xkxGcsxAJQaOY9nG1xq",
        "listLimitOfQuery": 1000
    },
    "client": "info_1230_test_wiregrass-al",
    "tenantCode": "wial",
    "filter": {
        "dateStart": "2020-05-10T22:58:10",
        "dateEnd": "2022-05-12T23:58:10"
    },
    "loop_interval": {
        "period": 2,
        "periodType": "week",
        "dateStart": "2020-05-10T22:58:10",
        "dateEnd": "2022-05-12T23:58:10"
    },
    "clientTenantMapping": {
        "wial": {
            "covington": "covington",
            "crenshaw": "crenshaw",
            "geneva": "geneva",
            "enterprise": "enterprise",
            "coffee": "coffee",
            "headland": "headland",
            "bullock": "bullock",
            "ozark-dale": "ozark-dale",
            "houston": "houston",
            "dothan": "dothan",
            "daleville": "daleville"
        }
    },
    "version": "1.0.0.0"
}
