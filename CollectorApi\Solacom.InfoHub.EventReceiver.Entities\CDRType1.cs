﻿using System.Xml.Serialization;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    /// <summary>
    /// Defines the CDRType1 Event type - a custom i3log event that contains end of call information
    /// </summary>
    /// <remarks>This data should not be used in parsing, due to current limitation on handling multiple tenant cases.  
    /// Current usage is restricted to only CallBackNumber which is populated independent of this issue.
    /// </remarks>
    [XmlRoot(ElementName = "cdrType1")]
    public class CdrType1
    {
        [XmlElement(ElementName = "startTime")]
        public string StartTime { get; set; }
        [XmlElement(ElementName = "operatorId")]
        public string OperatorId { get; set; }
        [XmlElement(ElementName = "ani")]
        public string Ani { get; set; }
        [XmlElement(ElementName = "presentedTime")]
        public string PresentedTime { get; set; }
        [XmlElement(ElementName = "answeredTime")]
        public string AnsweredTime { get; set; }
        [XmlElement(ElementName = "jobNumber")]
        public string JobNumber { get; set; }
        [XmlElement(ElementName = "transferTime")]
        public string TransferTime { get; set; }
        [XmlElement(ElementName = "transferAnswerTime")]
        public string TransferAnswerTime { get; set; }
        [XmlElement(ElementName = "disassociatedTime")]
        public string DisassociatedTime { get; set; }
        [XmlElement(ElementName = "transferTargetType")]
        public string TransferTargetType { get; set; }
        [XmlElement(ElementName = "transferTargetName")]
        public string TransferTargetName { get; set; }
        [XmlElement(ElementName = "transferTarget")]
        public string TransferTarget { get; set; }
        [XmlElement(ElementName = "disconnectReason")]
        public string DisconnectReason { get; set; }
        [XmlElement(ElementName = "ivrOutcome")]
        public string IvrOutcome { get; set; }
        [XmlElement(ElementName = "externalTransferAttempts")]
        public string ExternalTransferAttempts { get; set; }
        [XmlElement(ElementName = "dnis")]
        public string Dnis { get; set; }
        [XmlElement(ElementName = "endTime")]
        public string EndTime { get; set; }
        [XmlElement(ElementName = "routingGroupTime")]
        public string RoutingGroupTime { get; set; }
        [XmlElement(ElementName = "routedRingGroupTime")]
        public string RoutedRingGroupTime { get; set; }

        /// <summary>
        /// Callbacknumber for the scope of the call.
        /// </summary>
        /// <remarks>Introduced in i3Logging.v1_47</remarks>
        [XmlElement(ElementName = "callbackNumber")]
        public string CallbackNumber { get; set; }

    }
}
