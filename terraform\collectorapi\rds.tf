# TODO: Remove the comments when the code is needed
# module "infohub_vpc" {
#   source          = "../modules/vpc"
#   subnet_tag_name = "collectorapi"
#   vpc_tag_name    = "collectorapi"
#   vpc_id          = "${module.infohub_vpc.vpc_id}"
# }


# module "infohub_security_group" {
#   source                  = "../modules/security_group"
#   security_group_tag_name = "collectorapi"
#   vpc_id                  = "${module.infohub_vpc.vpc_id}"
# }


module "infohub_db_instance" {
  source      = "../modules/rds"
  db_instance_identifier  = "${var.db_instance_identifier}"
  db_instance_name        = "${var.db_instance_name}"
  db_instance_username    = "${var.db_instance_username}"
  db_instance_password    = "${var.db_instance_password}"
  db_instance_parameter_group_name    = "${var.db_instance_parameter_group_name}"
  db_instance_tag_name                = "${var.db_instance_tag_name}"
  # TODO: Get this to work db_instance_vpc_security_group_ids  = ["${module.infohub_security_group.security_group_id}"]
  db_instance_vpc_security_group_ids  = "${var.db_instance_vpc_security_group_ids}"
}


output "mariadb_endpoint" {
   value = "${module.infohub_db_instance.db_instance_endpoint}"
 }


 output "mariadb_port" {
   value = "${module.infohub_db_instance.db_instance_port}"
 }


 output "mariadb_username" {
   value = "${module.infohub_db_instance.db_instance_username}"
 }


  output "mariadb_password" {
   value = "${module.infohub_db_instance.db_instance_password}"
 }
 