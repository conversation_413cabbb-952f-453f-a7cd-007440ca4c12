{"Logging": {"LogLevel": {"Default": "Debug", "System": "Warning", "Microsoft": "Warning"}, "elasticsearchSettings": {"url": "Wf7NVAkOTa3wXbJC34k3MJooQ0v9+YMCd8UZvsxlPwDnXZGwCr5Ap8xl8Fo0xRYeTKPR+J6oFTXhzxrjvsyZ2w==", "userName": "QIeQKMWLoOalzjXjgQRCP6UK227q+1i8xfhDFOTwoQM=", "password": "unFwFlO8av2c+lVRvchWJFyesun8i0mYP2yNu1/FZVhXI+XpYRRsyj7mLQaLn6ok"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "Properties": {"Environment": "Stage"}}, "clientSettings": {"clients": "pv-gatineau,pv-legacy-gatineau", "clientcodeIndexPrefix": {"pvgt": "pv-gatineau", "pvlggt": "pv-legacy-gatineau", "foab": "foothills-ab", "woon": "woodstock-on"}, "clientTenantMapping": {"pvgt": {"tng000": "tng000", "tng001": "tng001", "map": "map"}, "pvlggt": {"tng000": "tng000", "tng001": "tng001", "map": "map"}, "foab": {"foothillsab": "foothillsab"}, "woon": {"woodstock": "woodstock"}}, "clientTimezoneMapping": {"pvgt": "America/New_York", "pvlggt": "America/New_York", "foab": "America/New_York", "woon": "America/New_York"}, "clientEsiNetSupportedList": "pvgt"}, "Database": {"mysql": {"connectionstring.CollectorAPI": "", "connectionstring.InsightsData": ""}}}