﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Solacom.InfoHub.EventReceiver.Entities;
using Solacom.InfoHub.EventReceiver.MariaDb.Context.Interfaces;
using Solacom.InfoHub.EventReceiver.MariaDb.Context.Models;
using Microsoft.Extensions.Configuration;
using Solacom.InfoHub.EventReceiver.Entities.Security;

namespace Solacom.InfoHub.EventReceiver.MariaDb.Context
{
    public class UnitOfWork_InsightsData : IUnitOfWork_InsightsData
    {
        private readonly ILogger<UnitOfWork> _logger;
        private readonly IDataProvider_InsightsData _dataProvider;

        public UnitOfWork_InsightsData(IServiceProvider services, ILogger<UnitOfWork> logger, IConfiguration configuration)
        {
            _logger = logger;
            _dataProvider = new MySQLProvider_InsightsData( Encryption.Decrypt(configuration["Database:mysql:connectionstring.InsightsData"]));
        }

        public async Task SetCallSummary(string customerName, string tenantPsapName, EventReceiver.ElasticSearch.Entities.CallSummary callsummary)
        {
            var start = DateTime.Now;
            try
            {
                await _dataProvider.SetCallSummary(customerName, tenantPsapName, callsummary);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork_InsightsData : SetCallSummary took {duration.TotalMilliseconds} ms for {callsummary.CallIdentifier}");
            }
        }

        public async Task SetCallEventByList(string customerName, string tenantPsapName, List<EventLog> eventLogList)
        {
            var start = DateTime.Now;
            try
            {
                await _dataProvider.SetCallEventByList(customerName, tenantPsapName, eventLogList);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork_InsightsData : SetCallEventByList took {duration.TotalMilliseconds} ms");
            }
        }

        public async Task<(bool, DateTime, long)> CallSummaryExists(string customerName, string callIdentifier)
        {
            var start = DateTime.Now;
            try
            {
                return await _dataProvider.CallSummaryExists(customerName, callIdentifier);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork_InsightsData : CallSummaryExists took {duration.TotalMilliseconds} ms for {callIdentifier}");
            }
        }

        public async Task<(long, long)> DeleteCallData(string customerName, string callIdentifier)
        {
            var start = DateTime.Now;
            try
            {
                return await _dataProvider.DeleteCallData(customerName, callIdentifier);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork_InsightsData : DeleteCallData took {duration.TotalMilliseconds} ms for {callIdentifier}");
            }
        }

        private bool _disposed = false;

        private void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                }
            }
            _disposed = true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}