﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Solacom.InfoHub.EventReceiver.BusinessLogic.Interfaces;
using Solacom.InfoHub.EventReceiver.Entities;
using Solacom.InfoHub.EventReceiver.AppService.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Generic;

namespace Solacom.InfoHub.EventReceiver.Web.API.ScheduledTasks
{
    public class ProcessQueueService : IHostedService, IDisposable
    {
        private readonly IEventsManager _eventsManager;
        private Timer _timer;
        private readonly ScheduledServicesFrequenciesInMinutes _settings;
        private readonly ILogger<ProcessQueueService> _logger;
        /// <summary>
        /// Stores the full lookup of all Client/Tenant mapping
        /// </summary>
        /// <remarks>
        /// Required since expiry isn't per tenant, but based on the DB pull on available tenants
        /// </remarks>
        private Dictionary<string, Dictionary<string,string>> _tenantLookupCollection;
        /// <summary>
        /// Collection of Clients timezone configuration
        /// </summary>
        private Dictionary<string, NodaTime.DateTimeZone> _clientTimeZoneMapping;

        public ProcessQueueService(IServiceScopeFactory serviceScopeFactory, ILogger<ProcessQueueService> logger, IOptions<ScheduledServicesFrequenciesInMinutes> options, IConfiguration configuration)
        {
            _logger = logger;

            var scope = serviceScopeFactory.CreateScope();
            _eventsManager = scope.ServiceProvider.GetService<IEventsManager>();

            _settings = options.Value;


            //Required to capture the avialable Tenant mapping for parsing from the expired data source.
            Dictionary<string, string> _clientCodeVsIndexprefix = new Dictionary<string, string>();
            var section = configuration.GetSection("clientSettings:clientcodeIndexPrefix");
            foreach (var kv in section.GetChildren())
            {
                _clientCodeVsIndexprefix.Add(kv.Key, kv.Value);
            }

            _tenantLookupCollection = new Dictionary<string, Dictionary<string,string>>();

            IConfigurationSection inner_section;
            section = configuration.GetSection("clientSettings:clientcodeIndexPrefix");
            
            //DB stores the Tenants under the full name, not the shorten name; therefore doing a tenant lookup mapping based on the expected long name 
            //Logic retrieves the full mapping of available Client/Tenants for usage in the expired event logic.
            string tenantReMap;
            foreach (var kv in section.GetChildren())
            {
                inner_section = configuration.GetSection("clientSettings:clientTenantMapping:" + kv.Key);

                if (section != null)
                {
                    tenantReMap = _clientCodeVsIndexprefix[kv.Key];
                    _tenantLookupCollection.Add(tenantReMap, new Dictionary<string, string>());
                    foreach (var innerKey in inner_section.GetChildren())
                    {
                        //ensuring lowercase for lookup
                        _tenantLookupCollection[tenantReMap].Add(innerKey.Key.ToLower(), innerKey.Value.ToLower());
                    }
                }
            }

            //retrieving the new Timzone configuration settings - if the setting isn't valid, the client information will not be available, and will not process any events from the given Client.
            _clientTimeZoneMapping = new Dictionary<string, NodaTime.DateTimeZone>();
            section = configuration.GetSection("clientSettings:clientTimezoneMapping");
            foreach (var kv in section.GetChildren())
            {
                if (NodaTime.DateTimeZoneProviders.Tzdb.Ids.Contains(kv.Value))
                {
                    tenantReMap = _clientCodeVsIndexprefix[kv.Key];
                    _clientTimeZoneMapping.Add(tenantReMap, NodaTime.DateTimeZoneProviders.Tzdb[kv.Value]);
                }
                else
                {
                    _logger.LogError($"ExpiredEventService Init: Client {kv.Key} timezone of {kv.Value} is not valid.  Please cross check https://nodatime.org/TimeZones for proper syntax.  Not adding it as a configured client.");
                }
            }


            _logger.LogInformation($"ProcessQueueService started, running every {_settings.ProcessQueueService} minutes");
        }

        public Task StartAsync(CancellationToken stoppingToken)
        {
            _timer = new Timer(async o =>await DoWork(o), null, TimeSpan.Zero,
                TimeSpan.FromMinutes(_settings.ProcessQueueService));

            return Task.CompletedTask;
        }

        private async Task DoWork(object state)
        {
            //The process Queue timing is matched to the frequency of the service running. 
            await _eventsManager.ProcessQueueCalls(_tenantLookupCollection, _clientTimeZoneMapping, _settings.ProcessQueueService);
        }

        public Task StopAsync(CancellationToken stoppingToken)
        {
            _timer?.Change(Timeout.Infinite, 0);

            return Task.CompletedTask;
        }

        public void Dispose()
        {
            _timer?.Dispose();
        }
    }
}