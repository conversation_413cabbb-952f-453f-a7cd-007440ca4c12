﻿using Solacom.InfoHub.EventReceiver.Exceptions;
using Solacom.InfoHub.EventReceiver.MariaDb.Context.Models;
using System;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using Microsoft.Extensions.Caching.Memory;
using Amazon.SecretsManager;
using Amazon.SecretsManager.Model;
using System.Xml;
using System.IO;

namespace Solacom.InfoHub.EventReceiver.BusinessLogic.ALI
{
    public class AliParser
    {
        private Alidata aliData = new Alidata();
        /// <summary>
        /// Stores the ALI definition files when found
        /// </summary>
        private IMemoryCache _cache; 
        public AliParser(string ali, string indexPrefix, IMemoryCache cache)
        {
            _cache = cache;
            ali = CheckCDATAString(ali);
            MapCarrier(ali, indexPrefix);
        }

        /// <summary>
        /// Retrieves the ALI XML mapping file if possible.   NULL if not found/defined.
        /// </summary>
        /// <param name="clientCode">Unique client long identifier</param>
        /// <returns>ALI object if found for usage, or NULL if not defined/found.</returns>
        private ALISchema GetParsingSchemaFromConfig(string clientCode)
        {
            try
            {
                ALISchema cachedAliSchema = null;
                string aliCacheKey = $"ALIXmlCache_{clientCode}";
                if (!_cache.TryGetValue(aliCacheKey, out cachedAliSchema))  //NOTE: this will throw an exception when it is set to "--", this is expected, as it falls into the condition of none retrieved.
                {


                    try
                    {
                        //first, attempt to retrieve the ALI from a secret 
                        var region = Environment.GetEnvironmentVariable("aws_region");
                        var awsEnv = Environment.GetEnvironmentVariable("aws_environment");
                        var awsCountry = Environment.GetEnvironmentVariable("aws_country");
                        IAmazonSecretsManager client = new AmazonSecretsManagerClient(Amazon.RegionEndpoint.GetBySystemName(region));
                        GetSecretValueRequest request = new GetSecretValueRequest
                        {
                            SecretId = $"{awsEnv}/{awsCountry}/alixml/{clientCode}",
                            VersionStage = "AWSCURRENT", // VersionStage defaults to AWSCURRENT if unspecified.
                        };

                        GetSecretValueResponse response = client.GetSecretValueAsync(request).Result;

                        XmlReader xmlStringFromSecret = XmlReader.Create(new StringReader(response.SecretString));

                        XDocument aliXmlDoc = XDocument.Load(xmlStringFromSecret);
                        cachedAliSchema = XmlHelper.DeserializeXml<ALISchema>(aliXmlDoc.ToString());
                    }
                    catch (Exception e)
                    {
                        //No action required, as indicates no variable available, continue;
                    }

                    //if not found, try local storage (for dev)
                    if (cachedAliSchema == null)
                    {                       
                        try
                        {
                            XDocument aliXmlDoc = XDocument.Load($"ALIParsingXML/{clientCode}_ALISchema.xml");

                            cachedAliSchema = XmlHelper.DeserializeXml<ALISchema>(aliXmlDoc.ToString());
                        }
                        catch (Exception ex)
                        {
                            //Surpressing the exception as the logic handles the roll over case.  
                        }
                    }
                    

                    //Adding entry to the cache - note: no set expiry, leveraging the default behaviour of non-expiry except under memory pressure or system reset.
                    if( cachedAliSchema == null)   //This will enable the setting of "--" to indicate none was avaiable - this exception needs to control the flow as to set the cache to avoid re-fetches once ALI definition is not found.
                    {
                        _cache.Set(aliCacheKey, "--");  
                    }
                    else
                    {
                        _cache.Set(aliCacheKey, cachedAliSchema);
                    }
                    
                }

                return cachedAliSchema;
            }
            catch(Exception ex)
            {
                //Surpressing the exception as the logic handles the roll over case.  
                // Note, this does not set a default cache value as this failure condition should not trigger normally - and re-try fetch is appropriate in this condition.
                return null;
            }
        }

        private void MapCarrier(string ali, string clientCode)
        {
            try
            {
                ALISchema aliObj = GetParsingSchemaFromConfig(clientCode);
                if (aliObj == null)
                {
                    //If there is no mapping file, attempt to parse it with the XML structure
                    ALIRawData rawALI = ALIHelper.parseALIBody(ali);

                    //If no object comes back, it is disgnated as the original failure case
                    if( rawALI == null)
                    {
                        throw new InvalidParemeterException($"Could not deserialize ali response\r\n{ali}");
                    }

                    //perform mapping of XML ALI resulting data to the used ALI objects.
                    MapRawXMLALI(rawALI);
                }
                else if (aliObj.carriers.Carrier.Count > 0)
                {
                    ali = ali.Replace("\r", "");
                    ali = ali.Replace("\n", "");
                    
                    if (ali.Length > aliObj.CarrierEnd)
                    {
                        aliData.Carrier = ali.Substring(aliObj.CarrierStart.GetValueOrDefault(), aliObj.CarrierEnd.GetValueOrDefault() - aliObj.CarrierStart.GetValueOrDefault() + 1).Trim().ToString();
                    }
                    Carrier defaultCarrier = new Carrier();
                    foreach (Carrier car in aliObj.carriers.Carrier)
                    {
                        if (ali.Length>aliObj.CarrierEnd && ali.Substring(aliObj.CarrierStart.GetValueOrDefault(),aliObj.CarrierEnd.GetValueOrDefault()-aliObj.CarrierStart.GetValueOrDefault()+1).Trim().Equals(car.Name))
                        {
                            MapCOS(car, ali);
                            return;
                        }
                        else if (car.Name.ToLower().Equals("default"))
                        {
                            defaultCarrier = car;
                        }
                    }
                    MapCOS(defaultCarrier, ali);
                }
            }
            catch (Exception ex)
            {
                throw new ALIParsingException("Could not complete the parsing operation: " + FormatException(ex));
            }
        }

        private void MapCOS(Carrier carrier, string ali)
        {
            //Extract Class of Service first
            if (carrier.CosStart!=null && carrier.CosLength!=null && ali.Length>=(carrier.CosStart+ carrier.CosLength-1))
            {
                aliData.Classofservice = ali.Substring(carrier.CosStart.Value,carrier.CosLength.Value).Trim().ToUpperInvariant();
            }
            if (carrier.ClassOfService != null)
            {
                Cos defaultCOS = new Cos();
                foreach (Cos cos in carrier.ClassOfService.Cos)
                {
                    if (!string.IsNullOrEmpty(cos.Name) && cos.Name.Equals(aliData.Classofservice))
                    {
                        MapAliFields(cos, ali);
                        return;
                    }
                    else if (!string.IsNullOrEmpty(cos.Name) && cos.Name.ToLower().Equals("default"))
                    {
                        defaultCOS = cos;
                    }
                }
                MapAliFields(defaultCOS, ali);
            }
        }

        private void MapAliFields(Cos cos, string ali)
        {
            if (cos.Type!=null && cos.Type.Start != null && cos.Type.Length != null && ali.Length >= (cos.Type.Start + cos.Type.Length - 1))
            {
                aliData.Type = ali.Substring(cos.Type.Start.Value, cos.Type.Length.Value).Trim();
                if (aliData.Type.Equals("9"))
                    return;
            }
            if (cos.Date!=null && cos.Date.Start!=null && cos.Date.Length != null && ali.Length >= (cos.Date.Start + cos.Date.Length - 1))
            {
                aliData.Date = ali.Substring(cos.Date.Start.Value, cos.Date.Length.Value).Trim();
            }
            if (cos.CallbackNumber!=null && cos.CallbackNumber.Start!=null && cos.CallbackNumber.Length != null && ali.Length >= (cos.CallbackNumber.Start + cos.CallbackNumber.Length - 1))
            {
                //TODO: Use Regex.Replace instead of string replace to improve performance
                aliData.Callbacknumber = RemoveExtraCharacters(ali.Substring(cos.CallbackNumber.Start.Value, cos.CallbackNumber.Length.Value).Trim());
            }
            if (cos.Time!=null && cos.Time.Start!=null && cos.Time.Length!=null && ali.Length >= (cos.Time.Start + cos.Time.Length - 1))
            {
                //TODO: Currently keeping time and date separate because not sure what to do if index of only one field is provided.To be reviewed
                aliData.Time = ali.Substring(cos.Time.Start.Value, cos.Time.Length.Value).Trim();
            }
            if (cos.StreetNumber!=null && cos.StreetNumber.Start != null && cos.StreetNumber.Length != null && ali.Length >= (cos.StreetNumber.Start + cos.StreetNumber.Length - 1))
            {
                aliData.StreetNumber = ConvertToDouble(ali.Substring(cos.StreetNumber.Start.Value, cos.StreetNumber.Length.Value).Trim());
            }
            if (cos.Esn!=null && cos.Esn.Start != null && cos.Esn.Length != null && ali.Length >= (cos.Esn.Start + cos.Esn.Length - 1))
            {
                aliData.Esn = ConvertToDouble(ali.Substring(cos.Esn.Start.Value, cos.Esn.Length.Value).Trim());
            }
            if (cos.Address!=null && cos.Address.Start!=null && cos.Address.Length!=null && ali.Length >= (cos.Address.Start + cos.Address.Length - 1))
            {
                aliData.Address = ali.Substring(cos.Address.Start.Value, cos.Address.Length.Value).Trim();
            }
            if (cos.City!=null && cos.City.Start!=null && cos.City.Length!=null && ali.Length >= (cos.City.Start + cos.City.Length - 1))
            {
                aliData.City = ali.Substring(cos.City.Start.Value, cos.City.Length.Value).Trim();
            }
            if (cos.Country!=null && cos.Country.Start!=null && cos.Country.Length!=null && ali.Length >= (cos.Country.Start + cos.Country.Length - 1))
            {
                aliData.Country = ali.Substring(cos.Country.Start.Value, cos.Country.Length.Value).Trim();
            }
            if (cos.Latitude!=null && cos.Latitude.Start!=null && cos.Latitude.Length!=null && ali.Length >= (cos.Latitude.Start + cos.Latitude.Length - 1))
            {
                aliData.Latitude = ConvertToDouble(ali.Substring(cos.Latitude.Start.Value, cos.Latitude.Length.Value).Trim());
            }
            if (cos.Latitude!=null && cos.Longitude.Start!=null && cos.Longitude.Length!=null && ali.Length >= (cos.Longitude.Start + cos.Longitude.Length - 1))
            {
                aliData.Longitude = ConvertToDouble(ali.Substring(cos.Longitude.Start.Value, cos.Longitude.Length.Value).Trim());
            }
            if (cos.Zipcode!=null && cos.Zipcode.Start!=null && cos.Zipcode.Length!=null && ali.Length >= (cos.Zipcode.Start + cos.Zipcode.Length - 1))
            {
                aliData.Zipcode = ali.Substring(cos.Zipcode.Start.Value, cos.Zipcode.Length.Value).Trim();
            }
            //Commented out as not being used in code - keep for potential re-use when requried.  
            //if (cos.PositionId!=null && cos.PositionId.Start!=null && cos.PositionId.Length!=null && ali.Length >= (cos.PositionId.Start + cos.PositionId.Length - 1))
            //{
            //    aliData.Positionid = ali.Substring(cos.PositionId.Start.Value, cos.PositionId.Length.Value).Trim();
            //}
            if (cos.Uncertainty!=null && cos.Uncertainty.Start!=null && cos.Uncertainty.Length!=null && ali.Length >= (cos.Uncertainty.Start + cos.Uncertainty.Length - 1))
            {
                aliData.Uncertainty = ConvertToDouble(ali.Substring(cos.Uncertainty.Start.Value, cos.Uncertainty.Length.Value).Trim());
            }
            if (cos.Confidence!=null && cos.Confidence.Start!=null && cos.Confidence.Length!=null && ali.Length >= (cos.Confidence.Start + cos.Confidence.Length - 1))
            {
                aliData.Confidence = ConvertToDouble(ali.Substring(cos.Confidence.Start.Value, cos.Confidence.Length.Value).Trim());
            }
            if (cos.State != null && cos.State.Start != null && cos.State.Length != null && ali.Length >= (cos.State.Start + cos.State.Length - 1))
            {
                aliData.State = ali.Substring(cos.State.Start.Value, cos.State.Length.Value).Trim();
            }
        }

        /// <summary>
        /// Takes the completed XML ALI data and sets it to the expected ALI object.
        /// </summary>
        /// <param name="aliRawData">The XML based ALI data</param>
        private void MapRawXMLALI(ALIRawData aliRawData)
        {
            //List of calculated data after ALI retrieval - not required upfront data 
            // time
            // date

            //Logic instroduced as Bell stores using CallbackNum vs Telus stores in MainTelNum field.
            aliData.Callbacknumber = (string.IsNullOrWhiteSpace(aliRawData.CallBackNum) ? aliRawData.MainTelNum : aliRawData.CallBackNum);
            aliData.Esn = ConvertToDouble(aliRawData.ESN);

            aliData.Carrier = aliRawData.AccessProvider.Name;            

            aliData.City = aliRawData.LocationInfo.StreetAddress.MSAGCommunity;
            aliData.StreetNumber = ConvertToDouble(aliRawData.LocationInfo.StreetAddress.HouseNum);
            aliData.State = aliRawData.LocationInfo.StreetAddress.StateProvince;
            aliData.Country = aliRawData.LocationInfo.StreetAddress.Country;

            //required sub logic due to Bell definition containing potential streetname information in a Extension node vs the generic nodes.
            if (string.IsNullOrWhiteSpace(aliRawData.LocationInfo.StreetAddress.StreetName) && aliRawData.BELLExtension != null)
            {
                aliData.Address = $"{aliRawData.BELLExtension.StreetName}";
            }
            else
            {
                aliData.Address = $"{aliRawData.LocationInfo.StreetAddress.StreetName}";
            }            

            aliData.Latitude = ConvertToDouble(aliRawData.LocationInfo.GeoLocation.Latitude);
            aliData.Longitude = ConvertToDouble(aliRawData.LocationInfo.GeoLocation.Longitude);
            aliData.Zipcode = aliRawData.LocationInfo.StreetAddress.PostalZipCode;
            //Excluded as it is not being used at all - and currently don't have it mapped to the XML sourced ALI data
            //aliData.Positionid = null; 
            
            aliData.Uncertainty = ConvertToDouble(aliRawData.LocationInfo.GeoLocation.Uncertainty);
            aliData.Confidence = ConvertToDouble(aliRawData.LocationInfo.GeoLocation.Confidence);

            if( aliRawData.TELUSExtension != null)
            {
                aliData.Classofservice = aliRawData.TELUSExtension.ClassOfService;
            }
            else if( aliRawData.BELLExtension != null)
            {
                aliData.Classofservice = aliRawData.BELLExtension.ClassOfService;
            }

            //If the Class of service is not defined, set it to 9 - else, default of 0 is used.
            aliData.Type = (string.IsNullOrEmpty(aliData.Classofservice) ? "9" : "0");
        }

        private string RemoveExtraCharacters(string callbackNum)
        {
            callbackNum = callbackNum.Replace("(", "");
            callbackNum = callbackNum.Replace(" ", "");
            callbackNum = callbackNum.Replace(")", "");
            callbackNum = callbackNum.Replace("-", "");
            return callbackNum;
        }
        private Double? ConvertToDouble(string stringToDouble)
        {
            double temp;
            double? returnDouble;
            if (!Double.TryParse(stringToDouble, out temp))
                returnDouble = null;
            else
                returnDouble = temp;
            return returnDouble;
        }

        private string CheckCDATAString(string ali)
        {
            if (!string.IsNullOrEmpty(ali) && ali.ToLower().Contains("cdata"))
            {
                return ali.Substring(ali.IndexOf("cdata", StringComparison.InvariantCultureIgnoreCase) + 6);
            }
            else
                return ali;
        }
        public Alidata GetAlidata()
        {
            return aliData;
        }

        private string FormatException(Exception e)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"Message: {e.Message}, StackTrace: {e.StackTrace}");

            var inner = e.InnerException;

            while (inner != null)
            {
                sb.AppendLine($"InnerExceptionMessage: {inner.Message}, InnerExceptionStackTrace: {inner.StackTrace}");
                inner = inner.InnerException;
            }

            return sb.ToString();
        }
    }
}
