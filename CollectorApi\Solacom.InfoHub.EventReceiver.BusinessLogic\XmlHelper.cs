﻿using System.IO;
using System.Linq;
using System.Xml;
using System.Xml.Serialization;
using Solacom.InfoHub.EventReceiver.Exceptions;
using System.Collections.Generic;
using System;
using Serilog;

namespace Solacom.InfoHub.EventReceiver.BusinessLogic
{
    public class XmlHelper
    {
        /// <summary>
        /// Stores any exceptions that are triggered during the deserialization.
        /// </summary>
        public List<Exception> _ExceptionList;

        public XmlHelper()
        {
        }

        /// <summary>
        /// Wrapped static version of the call. 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="sourceXML"></param>
        /// <returns></returns>
        public static T DeserializeXml<T>(string sourceXML) where T : class
        {
            XmlHelper selfObj = new XmlHelper();
            T rtn = selfObj.DeserializeObjectXml<T>(sourceXML);
            if(selfObj._ExceptionList.Count > 0)
            {
                throw selfObj._ExceptionList[0];
            }
            return rtn;
        }

        /// <summary>
        /// Deserlizes a passed XML to a given object.  Setting the _ExceptionList property with any failures.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="sourceXML"></param>
        /// <returns>Returned object - requires checking the _ExceptionList for any issues in deserialization</returns>
        public T DeserializeObjectXml<T>(string sourceXML) where T : class
        {
            //Force clearing on each execution pass.
            _ExceptionList = new List<Exception>();

            if (string.IsNullOrEmpty(sourceXML))
            {
                throw new InvalidParemeterException("Event data string is empty");
            }
            var serializer = new XmlSerializer(typeof(T));
            //Main exception handler for deserialization exceptions
            serializer.UnknownNode += new XmlNodeEventHandler(serializer_UnknownNode);

            T result = null;

            using (TextReader reader = new StringReader(sourceXML))
            {
                result = (T) serializer.Deserialize(reader);
            }

            return result;
        }
        public static string SerializeXml<T>(T obj) where T : class
        {
            var serializer = new XmlSerializer(typeof(T));
            var xml = "";

            using (var sww = new StringWriter())
            {
                using (XmlWriter writer = XmlWriter.Create(sww))
                {
                    serializer.Serialize(writer, obj);
                    xml = sww.ToString(); // Your XML
                }
            }

            return xml;
        }

        
        /// <summary>
        /// Event to capture any exceptions around the structure of the object being deserialized.  This exception captures node, element or attribute errors.
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void serializer_UnknownNode(object sender, XmlNodeEventArgs e)
        {
            //Check to see if the given unknown node is set to be ignored in the Attribute definition of the passed object.  ([XMLIgnore]) 
            // if so, exclude it from the resulting collection set. 
            bool ignoreNode =
                e.ObjectBeingDeserialized.GetType().GetProperties().Where(p => p.GetCustomAttributes(false).Any(a => a is XmlIgnoreAttribute))
                .Where(p => p.GetCustomAttributes(false)
                    .Where(a => a is XmlElementAttribute)
                        .ToLookup(ea => ((XmlElementAttribute)ea).ElementName)
                            .Contains(e.Name))
                .Any();

            if( !ignoreNode)
            {
                this._ExceptionList.Add(new System.NotImplementedException($"{e.NodeType}({e.LineNumber}:{e.LinePosition}):{e.Name}"));
            }
            
        }

        /// <summary>
        /// Processes for single data element
        /// </summary>
        /// <param name="xmlDoc">XML to process</param>
        /// <param name="nsmgr">Namespace definitions</param>
        /// <param name="xpath">xpath to retrieve for</param>
        /// <returns>Found data - NULL if not found</returns>
        public static string ParseNode(XmlDocument xmlDoc, XmlNamespaceManager nsmgr, string xpath)
        {
            string rtnString = null;

            XmlNode node = xmlDoc.SelectSingleNode(xpath, nsmgr);

            if (node == null)
            {
                return null;
            }

            rtnString = node.InnerText;

            return rtnString;
        }

        /// <summary>
        /// Processes for single data element
        /// </summary>
        /// <param name="xmlDoc">XML to process</param>
        /// <param name="xpath">xpath to retrieve for</param>
        /// <returns>Found data - NULL if not found</returns>
        /// <remarks>use *[local-name()='NODE'] for xpath seeking</remarks>
        public static string ParseNode(XmlDocument xmlDoc, string xpath)
        {
            string rtnString = null;

            XmlNode node = xmlDoc.SelectSingleNode(xpath);

            if (node == null)
            {
                return null;
            }

            rtnString = node.InnerText;

            return rtnString;
        }

        /// <summary>
        /// Checks to see if a existing Node exists / not empty - does not support namespace seeking.
        /// </summary>
        /// <param name="xmlDoc">XML to process</param>
        /// <param name="xpath">xpath to retrieve for</param>
        /// <returns></returns>
        /// <remarks>use *[local-name()='NODE'] for xpath seeking</remarks>
        public static bool NodeExists(XmlDocument xmlDoc, string xpath)
        {
            XmlNode node = xmlDoc.SelectSingleNode(xpath);

            if (node == null || string.IsNullOrEmpty(node.InnerText))
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// Parsing string to double if possible
        /// </summary>
        /// <param name="inputStr">source string</param>
        /// <returns>double, null if not valid double</returns>
        /// <remarks>Information log of failure</remarks>
        public static double? ConvertToDouble(string inputStr)
        {
            double rtnDouble;

            if( double.TryParse(inputStr, out rtnDouble) )
            {
                return rtnDouble;
            }
            else
            {
                Log.Logger.Information($"could not parse to double. String: {inputStr}");
            }

            return null;
        }
    }
}