﻿using Solacom.InfoHub.EventReceiver.Exceptions;
using Solacom.InfoHub.EventReceiver.MariaDb.Context.Models;
using System;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using EventLog = Solacom.InfoHub.EventReceiver.Entities.EventLog;
using System.Xml;
using Serilog;

namespace Solacom.InfoHub.EventReceiver.BusinessLogic.EventDataHandler
{
    public class LocationData
    {

        public LocationData()
        {

        }

        /// <summary>
        /// Processes shared nodes that can be contained in either source (HELD or EIDD)
        /// </summary>
        /// <param name="eventLog">Defined eventLog object - by ref in nature</param>
        /// <param name="xmlDoc">Source xml to parse</param>
        private void PopulateEventLogShared(EventLog eventLog, XmlDocument xmlDoc)
        {
        
            if( xmlDoc == null || eventLog == null || eventLog.heldResponse == null || eventLog.heldResponse.LocationData == null)
            {
                Log.Logger.Error("PopulateEventLogShared - Empty objects being passed into helper function.  Code issue detected.");
                return;
            }

            
            if (XmlHelper.NodeExists(xmlDoc, "//*[local-name()='method']"))
            {
                eventLog.heldResponse.LocationData.Method = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='method']");
            }

            if (XmlHelper.NodeExists(xmlDoc, "//*[local-name()='EmergencyCallData.ServiceInfo']"))
            {
                eventLog.heldResponse.LocationData.ServiceInfo = new Entities.ServiceInfo();

                eventLog.heldResponse.LocationData.ServiceInfo.ServiceEnvironment = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='EmergencyCallData.ServiceInfo']/*[local-name()='ServiceEnvironment']");
                eventLog.heldResponse.LocationData.ServiceInfo.ServiceType = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='EmergencyCallData.ServiceInfo']/*[local-name()='ServiceType']");
                eventLog.heldResponse.LocationData.ServiceInfo.ServiceMobility = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='EmergencyCallData.ServiceInfo']/*[local-name()='ServiceMobility']");
                eventLog.heldResponse.LocationData.ServiceInfo.Legacy_Class_Of_service = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='EmergencyCallData.ServiceInfo']/*[local-name()='legacy_class_of_service']");
            }

            if (XmlHelper.NodeExists(xmlDoc, "//*[local-name()='EmergencyCallData.DeviceInfo']"))
            {
                eventLog.heldResponse.LocationData.DeviceInfo = new Entities.DeviceInfo();

                eventLog.heldResponse.LocationData.DeviceInfo.DataProviderReference = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='EmergencyCallData.DeviceInfo']/*[local-name()='DataProviderReference']");
                eventLog.heldResponse.LocationData.DeviceInfo.DeviceClassification = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='EmergencyCallData.DeviceInfo']/*[local-name()='DeviceClassification']");
                eventLog.heldResponse.LocationData.DeviceInfo.DeviceMfgr = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='EmergencyCallData.DeviceInfo']/*[local-name()='DeviceMfgr']");
                eventLog.heldResponse.LocationData.DeviceInfo.DeviceModelNr = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='EmergencyCallData.DeviceInfo']/*[local-name()='DeviceModelNr']");
                eventLog.heldResponse.LocationData.DeviceInfo.TypeOfDeviceID = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='EmergencyCallData.DeviceInfo']/*[local-name()='UniqueDeviceID']/@TypeOfDeviceID");
                eventLog.heldResponse.LocationData.DeviceInfo.UniqueDeviceID = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='EmergencyCallData.DeviceInfo']/*[local-name()='UniqueDeviceID']");
            }

            if (XmlHelper.NodeExists(xmlDoc, "//*[local-name()='EmergencyCallData.ProviderInfo']"))
            {
                eventLog.heldResponse.LocationData.ProviderInfo = new Entities.ProviderInfo();

                eventLog.heldResponse.LocationData.ProviderInfo.DataProviderReference = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='EmergencyCallData.ProviderInfo']/*[local-name()='DataProviderReference']");
                eventLog.heldResponse.LocationData.ProviderInfo.DataProviderString = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='EmergencyCallData.ProviderInfo']/*[local-name()='DataProviderString']");
                eventLog.heldResponse.LocationData.ProviderInfo.ProviderID = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='EmergencyCallData.ProviderInfo']/*[local-name()='ProviderID']");
                eventLog.heldResponse.LocationData.ProviderInfo.ProviderIDSeries = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='EmergencyCallData.ProviderInfo']/*[local-name()='ProviderIDSeries']");
                eventLog.heldResponse.LocationData.ProviderInfo.TypeOfProvider = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='EmergencyCallData.ProviderInfo']/*[local-name()='TypeOfProvider']");
                eventLog.heldResponse.LocationData.ProviderInfo.ContactURI = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='EmergencyCallData.ProviderInfo']/*[local-name()='ContactURI']");
                eventLog.heldResponse.LocationData.ProviderInfo.Language = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='EmergencyCallData.ProviderInfo']/*[local-name()='Language']");
            }

            if (XmlHelper.NodeExists(xmlDoc, "//*[local-name()='EmergencyCallData.Comment']"))
            {
                eventLog.heldResponse.LocationData.EmergencyCallDataComment = new Entities.EmergencyCallDataComment();
                eventLog.heldResponse.LocationData.EmergencyCallDataComment.DataProviderReference = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='EmergencyCallData.Comment']/*[local-name()='DataProviderReference']");
                eventLog.heldResponse.LocationData.EmergencyCallDataComment.Comment = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='EmergencyCallData.Comment']/*[local-name()='Comment']");
            }

            Enums.LocationSource locationsource = Enums.LocationSource.undefined;

            if (XmlHelper.NodeExists(xmlDoc, "//*[local-name()='civicAddress']"))
            {
                locationsource = GetLocationSource(xmlDoc, "civicAddress");

                //now, retrieve the information based on the heirarchy source
                if (locationsource != Enums.LocationSource.undefined)
                {
                    string path = $"//*[local-name()='{locationsource.ToString()}']//*[local-name()='civicAddress']/";

                    eventLog.heldResponse.LocationData.CivicAddress = new Entities.CivicAddress();
                    eventLog.heldResponse.LocationData.CivicAddress.Country = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='country']");
                    eventLog.heldResponse.LocationData.CivicAddress.A1 = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='A1']");
                    eventLog.heldResponse.LocationData.CivicAddress.A2 = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='A2']");
                    eventLog.heldResponse.LocationData.CivicAddress.A3 = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='A3']");
                    eventLog.heldResponse.LocationData.CivicAddress.A4 = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='A4']");
                    eventLog.heldResponse.LocationData.CivicAddress.A5 = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='A5']");
                    eventLog.heldResponse.LocationData.CivicAddress.A6 = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='A6']");
                    eventLog.heldResponse.LocationData.CivicAddress.PRM = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='PRM']");
                    eventLog.heldResponse.LocationData.CivicAddress.PRD = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='PRD']");
                    eventLog.heldResponse.LocationData.CivicAddress.RD = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='RD']");
                    eventLog.heldResponse.LocationData.CivicAddress.STS = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='STS']");
                    eventLog.heldResponse.LocationData.CivicAddress.POD = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='POD']");
                    eventLog.heldResponse.LocationData.CivicAddress.POM = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='POM']");
                    eventLog.heldResponse.LocationData.CivicAddress.RDSEC = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='RDSEC']");
                    eventLog.heldResponse.LocationData.CivicAddress.RDSUBBR = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='RDSUBBR']");
                    eventLog.heldResponse.LocationData.CivicAddress.HNO = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='HNO']");
                    eventLog.heldResponse.LocationData.CivicAddress.HNS = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='HNS']");
                    eventLog.heldResponse.LocationData.CivicAddress.LMK = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='LMK']");
                    eventLog.heldResponse.LocationData.CivicAddress.LOC = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='LOC']");
                    eventLog.heldResponse.LocationData.CivicAddress.FLR = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='FLR']");
                    eventLog.heldResponse.LocationData.CivicAddress.NAM = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='NAM']");
                    eventLog.heldResponse.LocationData.CivicAddress.PC = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='PC']");
                    eventLog.heldResponse.LocationData.CivicAddress.BLD = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='BLD']");
                    eventLog.heldResponse.LocationData.CivicAddress.UNIT = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='UNIT']");
                    eventLog.heldResponse.LocationData.CivicAddress.ROOM = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='ROOM']");
                    eventLog.heldResponse.LocationData.CivicAddress.SEAT = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='SEAT']");
                    eventLog.heldResponse.LocationData.CivicAddress.PLC = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='PLC']");
                    eventLog.heldResponse.LocationData.CivicAddress.PCN = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='PCN']");
                    eventLog.heldResponse.LocationData.CivicAddress.POBOX = XmlHelper.ParseNode(xmlDoc, $"{path}*[local-name()='POBOX']");

                    eventLog.heldResponse.LocationData.CivicAddress.LocationSource = locationsource;
                }

                locationsource = Enums.LocationSource.undefined;
            }
                        
            if (XmlHelper.NodeExists(xmlDoc, "//*[local-name()='Point']"))
            {
                locationsource = GetLocationSource(xmlDoc, "Point");

                if (locationsource != Enums.LocationSource.undefined)
                {
                    eventLog.heldResponse.LocationData.Point = new Entities.Point();
                    eventLog.heldResponse.LocationData.Point.POS = XmlHelper.ParseNode(xmlDoc, $"//*[local-name()='{locationsource.ToString()}']//*[local-name()='Point']/*[local-name()='pos']");
                    
                    eventLog.heldResponse.LocationData.Point.LocationSource = locationsource;
                }

                locationsource = Enums.LocationSource.undefined;
            }

            if (XmlHelper.NodeExists(xmlDoc, "//*[local-name()='confidence']"))
            {
                //confidence follows the same heirarchy logic for location - where it is assoicated to the location.

                locationsource = GetLocationSource(xmlDoc, "confidence");

                //now, retrieve the information based on the heirarchy source
                if (locationsource != Enums.LocationSource.undefined)
                {
                    string confidencepath = $"//*[local-name()='{locationsource.ToString()}']//*[local-name()='confidence']";

                    eventLog.heldResponse.LocationData.Confidence = new Entities.Confidence();
                    eventLog.heldResponse.LocationData.Confidence.Text = XmlHelper.ParseNode(xmlDoc, $"{confidencepath}");
                    eventLog.heldResponse.LocationData.Confidence.Pdf = XmlHelper.ParseNode(xmlDoc, $"{confidencepath}/@pdf");

                    eventLog.heldResponse.LocationData.Confidence.LocationSource = locationsource;
                }

                locationsource = Enums.LocationSource.undefined;

            }

        }

        /// <summary>
        /// retrieves the Location source parent for a given target node
        /// </summary>
        /// <param name="xmlDoc">source XML</param>
        /// <param name="targetNode">what child location node that is being looked for</param>
        /// <returns>Source/what heirarchy location the node is contained within</returns>
        private Enums.LocationSource GetLocationSource(XmlDocument xmlDoc, string targetNode)
        {
            //Logic to retrieve based on heirarchy of importance
            //First, Device
            if (XmlHelper.NodeExists(xmlDoc, $"//*[local-name()='device']//*[local-name()='{targetNode}']"))
            {
                return Enums.LocationSource.device;
            }
            else if (XmlHelper.NodeExists(xmlDoc, $"//*[local-name()='tuple']//*[local-name()='{targetNode}']"))  //aka. Geodetic
            {
                return Enums.LocationSource.tuple;
            }
            else if (XmlHelper.NodeExists(xmlDoc, $"//*[local-name()='person']//*[local-name()='{targetNode}']")) //person is lowest source
            {
                return Enums.LocationSource.person;
            }
            else
            {
                Log.Logger.Warning($"Location data detected that does not have expected Parent node (device, tuple or person). Location Node: {targetNode}");
            }

            return Enums.LocationSource.undefined;
        }


        /// <summary>
        /// Processing the Location specific data from available Event Log source
        /// </summary>
        /// <param name="eventLog">Event Log object</param>
        /// <param name="eventDataXML">Source XML event data</param>
        public void PopulateEventLogByHELD(EventLog eventLog, string eventDataXML)
        {
            XmlDocument xmlDoc = new XmlDocument();
            try
            {
                xmlDoc.LoadXml(eventDataXML);
            }
            catch (Exception ex)
            {
                Log.Logger.Error($"Exception on event data deserilization (callId: {eventLog.callIdentifier}) : {ex.Message}");
                return;
            }

            BusinessLogic.XmlHelper xmlHelper = new BusinessLogic.XmlHelper();

            XmlNode heldXmlNode;

            heldXmlNode = xmlDoc.SelectSingleNode("//*[local-name()='held']");

            if (heldXmlNode != null)
            {
                string xmlRawString = heldXmlNode.InnerXml;
                
                //simple check to determine if XML structure is likely part of the contents.
                //Catches cases when the HELD comes back as a peroid (i.e. <held>.</held>) or other string based syntax.
                //This isn't a full validation, later code does this and will capture when unexpected data appears.
                if(!xmlRawString.TrimStart().StartsWith("<"))
                {
                    Log.Logger.Debug($"HELDResponse is not a valid XML structure. {eventLog.callIdentifier}. \r\n{xmlRawString}");
                    return;
                }

                //base check to capture when HELD Response returns non-standard error informaiton.
                //The Errors inner XML is non-standard and will be different per provider.  Therefore no standard XML parsing can occur on this node.
                Regex regex = new Regex("^(<error|<held:error)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                if (regex.IsMatch(xmlRawString))
                {
                    Log.Logger.Warning($"HELDResponse returned error information {eventLog.callIdentifier} timestamp:{eventLog.timestamp}. \r\n{xmlRawString}");
                    return;
                }

                try
                {
                    eventLog.heldResponse.Held = xmlHelper.DeserializeObjectXml<Entities.Held>($"<held xmlns=\"http://solacom.com/Logging\">{xmlRawString}</held>");
                }
                catch (Exception ex)
                {
                    Log.Logger.Error($"LocationData general exception (callId: {eventLog.callIdentifier}). {ex.Message} \r\n Held XML: {xmlRawString}");
                }

                if (xmlHelper._ExceptionList.Count > 0)
                {
                    Log.Logger.Error($"LocationData: Failed to parse Held information. (callId: {eventLog.callIdentifier}).   Exceptions.\r\n{string.Join("\r\n", xmlHelper._ExceptionList)} \r\nHeld XML: {xmlRawString} ");
                }

                #region Custom processing of nodes via XPath

                eventLog.heldResponse.LocationData = new Entities.LocationData(Enums.LocationEventSource.HELD);

                PopulateEventLogShared(eventLog, xmlDoc);

                Enums.LocationSource locationsource = Enums.LocationSource.undefined;

                if (XmlHelper.NodeExists(xmlDoc, "//*[local-name()='Ellipse']"))
                {
                    locationsource = GetLocationSource(xmlDoc, "Ellipse");

                    //now, retrieve the information based on the heirarchy source
                    if (locationsource != Enums.LocationSource.undefined)
                    {
                        string ellispepath = $"//*[local-name()='{locationsource.ToString()}']//*[local-name()='Ellipse']/";

                        eventLog.heldResponse.LocationData.Ellipse = new Entities.Ellipse();
                        eventLog.heldResponse.LocationData.Ellipse.POS = XmlHelper.ParseNode(xmlDoc, $"{ellispepath}*[local-name()='pos']");
                        eventLog.heldResponse.LocationData.Ellipse.SemiMajorAxis = XmlHelper.ParseNode(xmlDoc, $"{ellispepath}*[local-name()='semiMajorAxis']");
                        eventLog.heldResponse.LocationData.Ellipse.SemiMinorAxis = XmlHelper.ParseNode(xmlDoc, $"{ellispepath}*[local-name()='semiMinorAxis']");
                        eventLog.heldResponse.LocationData.Ellipse.Orientation = XmlHelper.ParseNode(xmlDoc, $"{ellispepath}*[local-name()='orientation']");

                        eventLog.heldResponse.LocationData.Ellipse.LocationSource = locationsource;

                    }

                    locationsource = Enums.LocationSource.undefined;

                }
                if (XmlHelper.NodeExists(xmlDoc, "//*[local-name()='Ellipsoid']"))
                {
                    locationsource = GetLocationSource(xmlDoc, "Ellipsoid");

                    //now, retrieve the information based on the heirarchy source
                    if (locationsource != Enums.LocationSource.undefined)
                    {
                        string ellipsoidpath = $"//*[local-name()='{locationsource.ToString()}']//*[local-name()='Ellipsoid']/";

                        eventLog.heldResponse.LocationData.Ellipsoid = new Entities.Ellipsoid();
                        eventLog.heldResponse.LocationData.Ellipsoid.POS = XmlHelper.ParseNode(xmlDoc, $"{ellipsoidpath}*[local-name()='pos']");
                        eventLog.heldResponse.LocationData.Ellipsoid.SemiMajorAxis = XmlHelper.ParseNode(xmlDoc, $"{ellipsoidpath}*[local-name()='semiMajorAxis']");
                        eventLog.heldResponse.LocationData.Ellipsoid.SemiMinorAxis = XmlHelper.ParseNode(xmlDoc, $"{ellipsoidpath}*[local-name()='semiMinorAxis']");
                        eventLog.heldResponse.LocationData.Ellipsoid.VerticalAxis = XmlHelper.ParseNode(xmlDoc, $"{ellipsoidpath}*[local-name()='verticalAxis']");
                        eventLog.heldResponse.LocationData.Ellipsoid.Orientation = XmlHelper.ParseNode(xmlDoc, $"{ellipsoidpath}*[local-name()='orientation']");

                        eventLog.heldResponse.LocationData.Ellipsoid.LocationSource = locationsource;
                    }

                    locationsource = Enums.LocationSource.undefined;
                }
                                
                if (XmlHelper.NodeExists(xmlDoc, "//*[local-name()='Circle']"))
                {
                    locationsource = GetLocationSource(xmlDoc, "Circle");

                    //now, retrieve the information based on the heirarchy source
                    if (locationsource != Enums.LocationSource.undefined)
                    {
                        string circlepath = $"//*[local-name()='{locationsource.ToString()}']//*[local-name()='Circle']/";

                        eventLog.heldResponse.LocationData.Circle = new Entities.Circle();
                        eventLog.heldResponse.LocationData.Circle.POS = XmlHelper.ParseNode(xmlDoc, $"{circlepath}*[local-name()='pos']");
                        eventLog.heldResponse.LocationData.Circle.Radius = XmlHelper.ParseNode(xmlDoc, $"{circlepath}*[local-name()='radius']");

                        eventLog.heldResponse.LocationData.Circle.LocationSource = locationsource;
                    }

                    locationsource = Enums.LocationSource.undefined;
                }
                if (XmlHelper.NodeExists(xmlDoc, "//*[local-name()='Sphere']"))
                {
                    locationsource = GetLocationSource(xmlDoc, "Sphere");

                    //now, retrieve the information based on the heirarchy source
                    if (locationsource != Enums.LocationSource.undefined)
                    {
                        string spherepath = $"//*[local-name()='{locationsource.ToString()}']//*[local-name()='Sphere']/";

                        eventLog.heldResponse.LocationData.Sphere = new Entities.Sphere();
                        eventLog.heldResponse.LocationData.Sphere.POS = XmlHelper.ParseNode(xmlDoc, $"{spherepath}*[local-name()='pos']");
                        eventLog.heldResponse.LocationData.Sphere.Radius = XmlHelper.ParseNode(xmlDoc, $"{spherepath}*[local-name()='radius']");
        
                        eventLog.heldResponse.LocationData.Sphere.LocationSource = locationsource;
                    }
                    locationsource = Enums.LocationSource.undefined;
                }

                if (XmlHelper.NodeExists(xmlDoc, "//*[local-name()='Polygon' and not(ancestor::*[local-name()='Prism'])]"))
                {
                    locationsource = GetLocationSource(xmlDoc, "Polygon");

                    //now, retrieve the information based on the heirarchy source
                    if (locationsource != Enums.LocationSource.undefined)
                    {
                        string polygonpath = $"//*[local-name()='{locationsource}']" + $"//*[local-name()='Polygon' and not(ancestor::*[local-name()='Prism'])]/";

                        XmlNodeList nodeList = xmlDoc.SelectNodes($"{polygonpath}*[local-name()='exterior']/*[local-name()='LinearRing']/*[local-name()='pos']");
                        if (nodeList != null && nodeList.Count > 0)
                        {
                            //only initialize the object after confirmation of the exact path to polygon.  This is due to Prism also contains a polygon sub-object.
                            eventLog.heldResponse.LocationData.Polygon = new Entities.Polygon();
                            foreach (XmlNode node in nodeList)
                            {
                                eventLog.heldResponse.LocationData.Polygon.ExteriorPointCollection.Add(node.InnerText);
                            }
                            eventLog.heldResponse.LocationData.Polygon.LocationSource = locationsource;
                        }
                    }

                    locationsource = Enums.LocationSource.undefined;
                }
                if (XmlHelper.NodeExists(xmlDoc, "//*[local-name()='Prism']"))
                {
                    locationsource = GetLocationSource(xmlDoc, "Prism");

                    //now, retrieve the information based on the heirarchy source
                    if (locationsource != Enums.LocationSource.undefined)
                    {
                        string prismpath = $"//*[local-name()='{locationsource.ToString()}']//*[local-name()='Prism']/";

                        eventLog.heldResponse.LocationData.Prism = new Entities.Prism();
                        eventLog.heldResponse.LocationData.Prism.Height = XmlHelper.ParseNode(xmlDoc, $"{prismpath}*[local-name()='height']");

                        string posString = XmlHelper.ParseNode(xmlDoc, $"{prismpath}*[local-name()='base']/*[local-name()='Polygon']/*[local-name()='exterior']/*[local-name()='LinearRing']/*[local-name()='posList']");

                        //Point breakout logic is here to have a single polygon string collection result
                        if (!string.IsNullOrEmpty(posString))
                        {
                            string[] posItems = posString.Split(
                                new[] { "\r\n", "\r", "\n" },
                                StringSplitOptions.RemoveEmptyEntries
                            );

                            foreach (string posLine in posItems)
                            {
                                var trimmedLine = posLine.Trim();
                                if (!string.IsNullOrWhiteSpace(trimmedLine))
                                {
                                    eventLog.heldResponse.LocationData.Prism.ExteriorPointCollection.Add(trimmedLine);
                                }
                            }
                        }

                        eventLog.heldResponse.LocationData.Prism.LocationSource = locationsource;
                    }

                    locationsource = Enums.LocationSource.undefined;
                }

                if (XmlHelper.NodeExists(xmlDoc, "//*[local-name()='ArcBand']"))
                {
                    locationsource = GetLocationSource(xmlDoc, "ArcBand");

                    //now, retrieve the information based on the heirarchy source
                    if (locationsource != Enums.LocationSource.undefined)
                    {
                        string archbandpath = $"//*[local-name()='{locationsource.ToString()}']//*[local-name()='ArcBand']/";

                        eventLog.heldResponse.LocationData.ArcBand = new Entities.ArcBand();
                        eventLog.heldResponse.LocationData.ArcBand.POS = XmlHelper.ParseNode(xmlDoc, $"{archbandpath}*[local-name()='pos']");
                        eventLog.heldResponse.LocationData.ArcBand.InnerRadius = XmlHelper.ParseNode(xmlDoc, $"{archbandpath}*[local-name()='innerRadius']");
                        eventLog.heldResponse.LocationData.ArcBand.OuterRadius = XmlHelper.ParseNode(xmlDoc, $"{archbandpath}*[local-name()='outerRadius']");
                        eventLog.heldResponse.LocationData.ArcBand.StartAngle = XmlHelper.ParseNode(xmlDoc, $"{archbandpath}*[local-name()='startAngle']");
                        eventLog.heldResponse.LocationData.ArcBand.OpeningAngle = XmlHelper.ParseNode(xmlDoc, $"{archbandpath}*[local-name()='openingAngle']");

                        eventLog.heldResponse.LocationData.ArcBand.LocationSource = locationsource;
                    }
                    locationsource = Enums.LocationSource.undefined;
                }
                #endregion
            }
        }


        /// <summary>
        /// Processing the Location specific data from available Event Log source
        /// </summary>
        /// <param name="eventLog">Event Log object</param>
        /// <param name="eventDataXML">Source XML event data</param>
        public void PopulateEventLogByEIDD(EventLog eventLog, string eventDataXML)
        {
            XmlDocument xmlDoc = new XmlDocument();
            try
            {
                xmlDoc.LoadXml(eventDataXML);
            }
            catch (Exception ex)
            {
                Log.Logger.Error($"Exception on event data deserilization: {ex.Message}");
                return;
            }

            BusinessLogic.XmlHelper xmlHelper = new BusinessLogic.XmlHelper();

            if (xmlDoc != null)
            {
                #region Custom processing of nodes via XPath

                eventLog.heldResponse = new Entities.HeldResponse();
                eventLog.heldResponse.LocationData = new Entities.LocationData(Enums.LocationEventSource.EIDD);

                PopulateEventLogShared(eventLog, xmlDoc);

                if (XmlHelper.NodeExists(xmlDoc, "//*[local-name()='method']"))
                {
                    eventLog.heldResponse.LocationData.Method = XmlHelper.ParseNode(xmlDoc, "//*[local-name()='method']");
                }

                #endregion
            }
        }
    }
}
