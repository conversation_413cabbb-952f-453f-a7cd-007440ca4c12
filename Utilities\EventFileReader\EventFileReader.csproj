<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>netcoreapp3.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="EntityFramework" Version="6.4.0" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="5.2.7" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EventsDataLayer\EventsDataLayer.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Solacom.InfoHub.EventReceiver.AppService">
      <HintPath>..\Solacom-2\InfoHub\CollectorApi\Solacom.InfoHub.EventReceiver.Web.API\bin\Debug\netcoreapp2.2\Solacom.InfoHub.EventReceiver.AppService.dll</HintPath>
    </Reference>
    <Reference Include="Solacom.InfoHub.EventReceiver.BusinessLogic">
      <HintPath>..\Solacom-2\InfoHub\CollectorApi\Solacom.InfoHub.EventReceiver.Web.API\bin\Debug\netcoreapp2.2\Solacom.InfoHub.EventReceiver.BusinessLogic.dll</HintPath>
    </Reference>
    <Reference Include="Solacom.InfoHub.EventReceiver.ElasticSearch.Context">
      <HintPath>..\Solacom-2\InfoHub\CollectorApi\Solacom.InfoHub.EventReceiver.Web.API\bin\Debug\netcoreapp2.2\Solacom.InfoHub.EventReceiver.ElasticSearch.Context.dll</HintPath>
    </Reference>
    <Reference Include="Solacom.InfoHub.EventReceiver.ElasticSearch.Entities">
      <HintPath>..\Solacom-2\InfoHub\CollectorApi\Solacom.InfoHub.EventReceiver.Web.API\bin\Debug\netcoreapp2.2\Solacom.InfoHub.EventReceiver.ElasticSearch.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Solacom.InfoHub.EventReceiver.Entities">
      <HintPath>..\Solacom-2\InfoHub\CollectorApi\Solacom.InfoHub.EventReceiver.Web.API\bin\Debug\netcoreapp2.2\Solacom.InfoHub.EventReceiver.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Solacom.InfoHub.EventReceiver.Exceptions">
      <HintPath>..\Solacom-2\InfoHub\CollectorApi\Solacom.InfoHub.EventReceiver.Web.API\bin\Debug\netcoreapp2.2\Solacom.InfoHub.EventReceiver.Exceptions.dll</HintPath>
    </Reference>
    <Reference Include="Solacom.InfoHub.EventReceiver.Mappers">
      <HintPath>..\Solacom-2\InfoHub\CollectorApi\Solacom.InfoHub.EventReceiver.Web.API\bin\Debug\netcoreapp2.2\Solacom.InfoHub.EventReceiver.Mappers.dll</HintPath>
    </Reference>
    <Reference Include="Solacom.InfoHub.EventReceiver.MariaDb.Context">
      <HintPath>..\Solacom-2\InfoHub\CollectorApi\Solacom.InfoHub.EventReceiver.Web.API\bin\Debug\netcoreapp2.2\Solacom.InfoHub.EventReceiver.MariaDb.Context.dll</HintPath>
    </Reference>
    <Reference Include="Solacom.InfoHub.EventReceiver.Web.API">
      <HintPath>..\Solacom-2\InfoHub\CollectorApi\Solacom.InfoHub.EventReceiver.Web.API\bin\Debug\netcoreapp2.2\Solacom.InfoHub.EventReceiver.Web.API.dll</HintPath>
    </Reference>
    <Reference Include="Solacom.InfoHub.EventReceiver.Web.Dtos">
      <HintPath>..\Solacom-2\InfoHub\CollectorApi\Solacom.InfoHub.EventReceiver.Web.API\bin\Debug\netcoreapp2.2\Solacom.InfoHub.EventReceiver.Web.Dtos.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
