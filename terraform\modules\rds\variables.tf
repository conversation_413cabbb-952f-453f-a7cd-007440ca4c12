# ---------------------------------------------------------------------------------------------------------------------
# General Variables
# ---------------------------------------------------------------------------------------------------------------------


variable "db_instance_allocated_storage" {
  description = "The allocated storage in gibibytes"
  default     = "25"
  type        = "string"
}


variable "db_instance_storage_type" {
  description = "One of standard (magnetic), gp2 (general purpose SSD), or io1 (provisioned IOPS SSD)"
  default     = "gp2"
  type        = "string"
}


variable "db_instance_engine" {
  description = "The database engine to use"
  default     = "mariadb"
  type        = "string"
}


variable "db_instance_engine_version" {
  description = "The engine version to use"
  default     = "10.3.13"
  type        = "string"
}


variable "db_instance_instance_class" {
  description = "The instance type of the RDS instance"
  default     = "db.t2.micro"
  type        = "string"
}


variable "db_instance_identifier" {
  description = "The name of the RDS instance"
  type        = "string"
}


variable "db_instance_name" {
  description = "The name of the database to create when the DB instance is created"
  type        = "string"
}


variable "db_instance_username" {
  description = "Username for the master DB user"
  type        = "string"
}


variable "db_instance_password" {
  description = "Password for the master DB user"
  type        = "string"
}


variable "db_instance_port" {
  description = "The port on which the DB accepts connections"
  default     = "3306"
  type        = "string"
}


variable "db_instance_parameter_group_name" {
  description = "parameter_group_name"
  default     = "main.mariadb.10.3.13"
  type        = "string"
}


variable "db_instance_backup_retention_period" {
  description = "The number of days to retain backups. Must be a number between 0 and 35."
  default     = "7"
  type        = "string"
}


variable "db_instance_final_snapshot_identifier" {
  description = "The name of your final DB snapshot when this DB instance is deleted"
  default     = "mariadb-final-snapshot"
  type        = "string"
}


variable "db_instance_publicly_accessible" {
  description = "Bool to control if instance is publicly accessible"
  default     = "true"
  type        = "string"
}


variable "db_instance_vpc_security_group_ids" {
  description = "List of VPC security groups to associate"
  # type        = "string"
}


variable "db_instance_tag_name" {
  description = "Tag name"
  type        = "string"
}
