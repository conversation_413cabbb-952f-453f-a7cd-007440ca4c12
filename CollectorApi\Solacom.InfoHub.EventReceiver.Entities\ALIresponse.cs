﻿using System.Xml.Serialization;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    [XmlRoot(ElementName = "aliResponse")]
    public class AliResponse
    {
        [XmlElement(ElementName = "mediaLabel")]
        public string MediaLabel { get; set; }
        [XmlElement(ElementName = "aliLink")]
        public string AliLink { get; set; }
        [XmlElement(ElementName = "ali")]
        public string Ali { get; set; }
        [XmlElement(ElementName = "aliResponseCode")]
        public string AliResponseCode { get; set; }
    }
}
