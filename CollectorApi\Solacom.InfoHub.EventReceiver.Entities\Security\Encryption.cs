﻿using System;
using System.Security.Cryptography;
using System.IO;

namespace Solacom.InfoHub.EventReceiver.Entities.Security
{
    public class Encryption
    {
        /// <summary>
        /// Hardcoded key string for the encryption
        /// </summary>
        /// <remarks>Key length is 256</remarks>
        private static byte[] _privateKey = { 0x88, 0x34, 0x03, 0x90, 0xA5, 0x16, 0x87, 0x08, 0x9F, 0x02, 0xAF, 0xA4, 0x18, 0x10, 0xFA, 0x6C, 0x25, 0x34, 0x03, 0x90, 0xA5, 0x16, 0x87, 0x08, 0x9F, 0x02, 0xAF, 0xA4, 0x24, 0x08, 0xFA, 0x6C };

        /// <summary>
        /// Encryptes a given string
        /// </summary>
        /// <param name="message">Message to encrypt</param>
        /// <returns>Encrypted results</returns>
        public static string Encrypt(string message)
        {
            try
            {
                using var aesAlg = Aes.Create();
                aesAlg.Key = _privateKey;

                var encryptor = aesAlg.CreateEncryptor();

                using var ms = new MemoryStream();

                //Store the generated IV into the resulting encryption string.
                byte[] iv = aesAlg.IV;
                ms.Write(iv, 0, iv.Length);

                using var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write);
                using (var sw = new StreamWriter(cs))
                {
                    sw.Write(message); // Write all data to the stream.
                }
                return Convert.ToBase64String(ms.ToArray());
            }
            catch (Exception ex)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Decyrpts a given string
        /// </summary>
        /// <param name="textToDecrypt"></param>
        /// <returns>resulting string.  Returns string.empty if it fails.</returns>
        public static string Decrypt(string textToDecrypt)
        {
            try
            {
                var inputByte = new byte[textToDecrypt.Length + 1];
                inputByte = Convert.FromBase64String(textToDecrypt);
                using var ms = new MemoryStream(inputByte);

                using var aesAlg = Aes.Create();

                byte[] iv = new byte[aesAlg.IV.Length];
                ms.Read(iv, 0, iv.Length);

                var decryptor = aesAlg.CreateDecryptor(_privateKey, iv);

                using var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read);
                using var sr = new StreamReader(cs);
                return sr.ReadToEnd();
            }
            catch (Exception ex)
            {
                //return the original string, when not encrypted to allow for normal operation
                return textToDecrypt;
            }
        }
    }
}
