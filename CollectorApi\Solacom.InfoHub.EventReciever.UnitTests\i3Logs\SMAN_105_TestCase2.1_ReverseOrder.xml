<LogEvents>


<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:42:17.555Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>op4</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>CDRtype1</eventType>
        <cdrType1>
                <startTime>2023-02-06T12:31:14.226Z</startTime>
                <operatorId>0</operatorId>
                <ani>8195551002</ani>
                <presentedTime>2023-02-06T12:31:14.541Z</presentedTime>
                <answeredTime>2023-02-06T12:31:16.159Z</answeredTime>
                <jobNumber>.</jobNumber>
                <transferTime></transferTime>
                <transferAnswerTime></transferAnswerTime>
                <disassociatedTime></disassociatedTime>
                <transferTargetType>.</transferTargetType>
                <transferTargetName>.</transferTargetName>
                <transferTarget>.</transferTarget>
                <disconnectReason>.</disconnectReason>
                <ivrOutcome>.</ivrOutcome>
                <externalTransferAttempts>0</externalTransferAttempts>
                <dnis>779</dnis>
                <endTime>2023-02-06T12:42:17.555Z</endTime>
                <callbackNumber>8195551002</callbackNumber>
        </cdrType1>
</LogEvent>


<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:42:17.554Z</timestamp>
        <agencyOrElement>tng000</agencyOrElement>
        <agent>op4</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>EndMedia</eventType>
        <endMedia>
                <mediaLabel>_ML_18626BF8C88V0000240A@tng000</mediaLabel>
                <responseCode>16</responseCode>
                <disconnectReason></disconnectReason>
                <voiceQOS>
                        <mediaIpSourceAddr>.</mediaIpSourceAddr> 
                        <mediaIpDestAddr>.</mediaIpDestAddr>
                        <mediaUdpRtpSourcePort>-1</mediaUdpRtpSourcePort> 
                        <mediaUdpRtpDestPort>-1</mediaUdpRtpDestPort>
                        <mediaNumOfIpPktRxed>-1</mediaNumOfIpPktRxed> 
                        <mediaNumOfIpPktTxed>-1</mediaNumOfIpPktTxed>
                        <mediaNumOfIpErroredPktRxed>-1</mediaNumOfIpErroredPktRxed>
                        <mediaNumOfRtpPktRxed>-1</mediaNumOfRtpPktRxed>
                        <mediaNumOfRtpPktTxed>-1</mediaNumOfRtpPktTxed>
                        <mediaNumOfRtpPktLost>-1</mediaNumOfRtpPktLost>
                        <mediaNumOfRtpPktDiscarded>-1</mediaNumOfRtpPktDiscarded>
                        <mediaRtpJitter>-1</mediaRtpJitter>
                        <mediaRtpLatency>-1</mediaRtpLatency>
                        <mediaNumOfRtcpPktRxed>-1</mediaNumOfRtcpPktRxed>
                        <mediaNumOfRtcpPktTxed>-1</mediaNumOfRtcpPktTxed> 
                        <mediaFarEndPacketLostPercentage>-1</mediaFarEndPacketLostPercentage>
                        <mediaFarEndCumulativePacketLost>-1</mediaFarEndCumulativePacketLost> 
                        <mediaFarEndInterarrivalJitter>-1</mediaFarEndInterarrivalJitter> 
                </voiceQOS>
        </endMedia>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:42:17.553Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>EndCall</eventType>
        <endCall>
                <responseCode>16</responseCode>
                <callReplaced>No</callReplaced>
        </endCall>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:42:16.058Z</timestamp>
        <agencyOrElement>tng000</agencyOrElement>
        <agent>op4</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>Answer</eventType>
        <answer>
                <mediaLabel>_ML_18626BF8C88V0000240A@tng000</mediaLabel>
                <uri>tel:+7775553004</uri>
                <agentRole>Admin</agentRole>
                <tenantGroup>tng000</tenantGroup>
                <operatorId>0</operatorId>
                <workstation>PORSCHE-004</workstation>
        </answer>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:42:13.893Z</timestamp>
        <agencyOrElement>tng000</agencyOrElement>
        <agent>op4</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>Media</eventType>
        <media>
                <udp>.</udp>
                <mediaLabel>_ML_18626BF8C88V0000240A@tng000</mediaLabel>
        </media>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:42:13.890Z</timestamp>
        <agencyOrElement>tng000</agencyOrElement>
        <agent>op4</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>Route</eventType>
        <route>
                <uri>7775553004</uri> 
                <rule>rule #67</rule>
                <reason>alternate</reason>
                <mediaLabel>_ML_18626BF8C88V0000240A@tng000</mediaLabel>
                <attempt>1</attempt>
                <priority>-1</priority>
                <ani>8195551002</ani>
                <aniDomain>.</aniDomain>
                <dnis>7775553004</dnis>
                <pani>8195551002</pani>
                <esrn>0009991504</esrn>
                <callerName>Preserve Call Test</callerName>
                <aniTranslated>8195551002</aniTranslated>
                <dnisTranslated>7775553004</dnisTranslated>
                <callerNameTranslated>Preserve Call Test</callerNameTranslated>
        </route>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:42:13.835Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>QueueStateChange</eventType>
             <QueueStateChange>
                <StateChangeNotificationContents>Count</StateChangeNotificationContents>
                <queueId>9991504</queueId>
                <queueName>RG_LI</queueName>
                <direction>In</direction>
                <count>0</count>
             </QueueStateChange>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:32:33.081Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>EndMedia</eventType>
        <endMedia>
                <mediaLabel>_ML_TESTCASE_2.1_@tng000</mediaLabel>
                <responseCode>41</responseCode>
                <disconnectReason></disconnectReason>
                <voiceQOS>
                        <mediaIpSourceAddr>.</mediaIpSourceAddr> 
                        <mediaIpDestAddr>.</mediaIpDestAddr>
                        <mediaUdpRtpSourcePort>-1</mediaUdpRtpSourcePort> 
                        <mediaUdpRtpDestPort>-1</mediaUdpRtpDestPort>
                        <mediaNumOfIpPktRxed>-1</mediaNumOfIpPktRxed> 
                        <mediaNumOfIpPktTxed>-1</mediaNumOfIpPktTxed>
                        <mediaNumOfIpErroredPktRxed>-1</mediaNumOfIpErroredPktRxed>
                        <mediaNumOfRtpPktRxed>-1</mediaNumOfRtpPktRxed>
                        <mediaNumOfRtpPktTxed>-1</mediaNumOfRtpPktTxed>
                        <mediaNumOfRtpPktLost>-1</mediaNumOfRtpPktLost>
                        <mediaNumOfRtpPktDiscarded>-1</mediaNumOfRtpPktDiscarded>
                        <mediaRtpJitter>-1</mediaRtpJitter>
                        <mediaRtpLatency>-1</mediaRtpLatency>
                        <mediaNumOfRtcpPktRxed>-1</mediaNumOfRtcpPktRxed>
                        <mediaNumOfRtcpPktTxed>-1</mediaNumOfRtcpPktTxed> 
                        <mediaFarEndPacketLostPercentage>-1</mediaFarEndPacketLostPercentage>
                        <mediaFarEndCumulativePacketLost>-1</mediaFarEndCumulativePacketLost> 
                        <mediaFarEndInterarrivalJitter>-1</mediaFarEndInterarrivalJitter> 
                </voiceQOS>
        </endMedia>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:32:17.865Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>QueueStateChange</eventType>
             <QueueStateChange>
                <StateChangeNotificationContents>Count</StateChangeNotificationContents>
                <queueId>9991504</queueId>
                <queueName>RG_LI</queueName>
                <direction>In</direction>
                <count>1</count>
             </QueueStateChange>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:32:17.858Z</timestamp>
        <agencyOrElement>tng000</agencyOrElement>
        <agent>op4</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>EndMedia</eventType>
        <endMedia>
                <mediaLabel>_ML_18626B57E50V00002409@tng000</mediaLabel>
                <responseCode>41</responseCode>
                <disconnectReason></disconnectReason>
                <voiceQOS>
                        <mediaIpSourceAddr>.</mediaIpSourceAddr> 
                        <mediaIpDestAddr>.</mediaIpDestAddr>
                        <mediaUdpRtpSourcePort>-1</mediaUdpRtpSourcePort> 
                        <mediaUdpRtpDestPort>-1</mediaUdpRtpDestPort>
                        <mediaNumOfIpPktRxed>-1</mediaNumOfIpPktRxed> 
                        <mediaNumOfIpPktTxed>-1</mediaNumOfIpPktTxed>
                        <mediaNumOfIpErroredPktRxed>-1</mediaNumOfIpErroredPktRxed>
                        <mediaNumOfRtpPktRxed>-1</mediaNumOfRtpPktRxed>
                        <mediaNumOfRtpPktTxed>-1</mediaNumOfRtpPktTxed>
                        <mediaNumOfRtpPktLost>-1</mediaNumOfRtpPktLost>
                        <mediaNumOfRtpPktDiscarded>-1</mediaNumOfRtpPktDiscarded>
                        <mediaRtpJitter>-1</mediaRtpJitter>
                        <mediaRtpLatency>-1</mediaRtpLatency>
                        <mediaNumOfRtcpPktRxed>-1</mediaNumOfRtcpPktRxed>
                        <mediaNumOfRtcpPktTxed>-1</mediaNumOfRtcpPktTxed> 
                        <mediaFarEndPacketLostPercentage>-1</mediaFarEndPacketLostPercentage>
                        <mediaFarEndCumulativePacketLost>-1</mediaFarEndCumulativePacketLost> 
                        <mediaFarEndInterarrivalJitter>-1</mediaFarEndInterarrivalJitter> 
                </voiceQOS>
        </endMedia>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:31:46.323Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>HELDresponse</eventType>
        <heldResponse>
                <mediaLabel>_ML_TESTCASE_2.1_@tng000</mediaLabel>
                <heldDomain>**********</heldDomain>
                <responseCode>200</responseCode>
                <held>
    <locationResponse xmlns="urn:ietf:params:xml:ns:geopriv:held" xmlns:ca="urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr" xmlns:gml="http://www.opengis.net/gml" xmlns:ns4="urn:ietf:params:xml:ns:pidf" xmlns:ns5="urn:ietf:params:xml:ns:pidf:data-model" xmlns:ns6="urn:ietf:params:xml:ns:pidf:geopriv10" xmlns:ns7="urn:ietf:params:xml:ns:pidf:geopriv10:basicPolicy" xmlns:ns8="http://www.opengis.net/pidflo/1.0" xmlns:ns9="urn:ietf:params:xml:ns:geopriv:held:id">
        <locationUriSet expires="2023-02-06T07:56:13" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <locationUri>https://ls.example.com:9768/357yc6s64ceyoiuy5ax3o</locationUri>
            <locationUri>sip:<EMAIL>:</locationUri>
        </locationUriSet>
        <presence entity="pres:<EMAIL>" xmlns="urn:ietf:params:xml:ns:pidf" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <tuple id="lisLocation">
                <status>
                    <geopriv xmlns="urn:ietf:params:xml:ns:pidf:geopriv10">
                        <location-info>
                           <ns8:Sphere srsName="urn:ogc:def:crs:EPSG::4979">
                            <gml:pos>42.5463 -73.2512 26.3</gml:pos>
                            <ns8:radius uom="urn:ogc:def:uom:EPSG::9001">
                              850.24
                            </ns8:radius>
                          </ns8:Sphere>
                        </location-info>
                        <location-info>
                                <gml:Polygon srsName="urn:ogc:def:crs:EPSG::4326">
                                      <gml:exterior>
                                        <gml:LinearRing>
                                          <gml:pos>43.311 -73.422</gml:pos> 
                                          <gml:pos>43.111 -73.322</gml:pos> 
                                          <gml:pos>43.111 -73.222</gml:pos> 
                                          <gml:pos>43.311 -73.122</gml:pos> 
                                          <gml:pos>43.411 -73.222</gml:pos> 
                                          <gml:pos>43.411 -73.322</gml:pos> 
                                          <gml:pos>43.311 -73.422</gml:pos> 
                                        </gml:LinearRing>
                                      </gml:exterior>
                                    </gml:Polygon>
                        </location-info>
                        <usage-rules>
                            <retransmission-allowed>true</retransmission-allowed>
                            <retention-expiry>2023-02-06T07:56:13</retention-expiry>
                        </usage-rules>
                        <method>Wiremap</method>
                    </geopriv>
                </status>
                <timestamp>2023-02-06T07:56:15</timestamp>
            </tuple>
        </presence>
    </locationResponse>
</held>
        </heldResponse>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:31:46.298Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>HELDquery</eventType>
        <heldQuery>
                <mediaLabel>_ML_TESTCASE_2.1_@tng000</mediaLabel>
                <heldDomain>**********</heldDomain>
                <heldPurpose>AutoRebid</heldPurpose>
                <held-uri>tel:8195551002</held-uri>
        </heldQuery>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:31:46.123Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>HELDresponse</eventType>
        <heldResponse>
                <mediaLabel>_ML_TESTCASE_2.1_@tng000</mediaLabel>
                <heldDomain>**********</heldDomain>
                <responseCode>200</responseCode>
                <held>
    <locationResponse xmlns="urn:ietf:params:xml:ns:geopriv:held" xmlns:ca="urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr" xmlns:gml="http://www.opengis.net/gml" xmlns:ns4="urn:ietf:params:xml:ns:pidf" xmlns:ns5="urn:ietf:params:xml:ns:pidf:data-model" xmlns:ns6="urn:ietf:params:xml:ns:pidf:geopriv10" xmlns:ns7="urn:ietf:params:xml:ns:pidf:geopriv10:basicPolicy" xmlns:ns8="http://www.opengis.net/pidflo/1.0" xmlns:ns9="urn:ietf:params:xml:ns:geopriv:held:id">
        <locationUriSet expires="2023-02-06T07:56:13" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <locationUri>https://ls.example.com:9768/357yc6s64ceyoiuy5ax3o</locationUri>
            <locationUri>sip:<EMAIL>:</locationUri>
        </locationUriSet>
        <presence entity="pres:<EMAIL>" xmlns="urn:ietf:params:xml:ns:pidf" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <tuple id="lisLocation">
                <status>
                    <geopriv xmlns="urn:ietf:params:xml:ns:pidf:geopriv10">
                        <location-info>
                            <ns8:Ellipse srsName="urn:ogc:def:crs:EPSG::4326">
                              <gml:pos>42.5463 -73.2512</gml:pos>
                              <ns8:semiMajorAxis uom="urn:ogc:def:uom:EPSG::9001">
                                1275
                              </ns8:semiMajorAxis>
                              <ns8:semiMinorAxis uom="urn:ogc:def:uom:EPSG::9001">
                                670
                              </ns8:semiMinorAxis>
                              <ns8:orientation uom="urn:ogc:def:uom:EPSG::9102">
                                43.2
                              </ns8:orientation>
                            </ns8:Ellipse> 
                        </location-info>
                        <usage-rules>
                            <retransmission-allowed>true</retransmission-allowed>
                            <retention-expiry>2023-02-06T07:56:13</retention-expiry>
                        </usage-rules>
                        <method>Wiremap</method>
                    </geopriv>
                </status>
                <timestamp>2023-02-06T07:56:13</timestamp>
            </tuple>
        </presence>
    </locationResponse>
</held>
        </heldResponse>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:31:46.098Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>HELDquery</eventType>
        <heldQuery>
                <mediaLabel>_ML_TESTCASE_2.1_@tng000</mediaLabel>
                <heldDomain>**********</heldDomain>
                <heldPurpose>AutoRebid</heldPurpose>
                <held-uri>tel:8195551002</held-uri>
        </heldQuery>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:31:36.122Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>HELDresponse</eventType>
        <heldResponse>
                <mediaLabel>_ML_TESTCASE_2.1_@tng000</mediaLabel>
                <heldDomain>**********</heldDomain>
                <responseCode>200</responseCode>
                <held>
                        <locationResponse xmlns="urn:ietf:params:xml:ns:geopriv:held" xmlns:ca="urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr" xmlns:gml="http://www.opengis.net/gml" xmlns:ns4="urn:ietf:params:xml:ns:pidf" xmlns:ns5="urn:ietf:params:xml:ns:pidf:data-model" xmlns:ns6="urn:ietf:params:xml:ns:pidf:geopriv10" xmlns:ns7="urn:ietf:params:xml:ns:pidf:geopriv10:basicPolicy" xmlns:ns8="http://www.opengis.net/pidflo/1.0" xmlns:ns9="urn:ietf:params:xml:ns:geopriv:held:id">
                            <locationUriSet expires="2023-02-08T09:49:53" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                <locationUri>https://ls.example.com:9768/357yc6s64ceyoiuy5ax3o</locationUri>
                                <locationUri>sip:<EMAIL>:</locationUri>
                            </locationUriSet>
                            <presence entity="pres:<EMAIL>" xmlns="urn:ietf:params:xml:ns:pidf" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                <tuple id="lisLocation">
                                    <status>
                                        <geopriv xmlns="urn:ietf:params:xml:ns:pidf:geopriv10">
                                            <location-info>
                                                <gml:Point srsName="urn:ogc:def:crs:EPSG::4979">
                                                    <gml:pos>+044 -06 55</gml:pos>
                                                </gml:Point>
                                            </location-info>
                                            <location-info>
                                                <ca:civicAddress xml:lang="en-us" xmlns:ca="urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr">
                                                    <ca:country>AU</ca:country>
                                                    <ca:A1>NSW1</ca:A1>
                                                    <ca:A2>556</ca:A2>
                                                    <ca:A3>Wollongon</ca:A3>
                                                    <ca:A4>Gwynneville</ca:A4>
                                                    <ca:A5/>
                                                    <ca:STS>SouthField Road</ca:STS>
                                                    <ca:LMK>CEGEP Wollongong</ca:LMK>
                                                    <ca:NAM>wredan corporation</ca:NAM>
                                                    <ca:PC>43456</ca:PC>
                                                    <ca:BLD>45</ca:BLD>
                                                    <ca:FLR>5</ca:FLR>
                                                </ca:civicAddress>
                                            </location-info>
                                            <usage-rules>
                                                <retransmission-allowed>true</retransmission-allowed>
                                                <retention-expiry>2023-02-08T09:49:53</retention-expiry>
                                            </usage-rules>
                                            <provided-by>
                                                <EmergencyCallDataReference purpose="EmergencyCallData.ProviderInfo" ref="https://api.ok.nga911.com/api/v1/adr/provider-info/by-rfa/39" xmlns="urn:ietf:params:xml:ns:pidf"/>
                                                <EmergencyCallDataReference purpose="EmergencyCallData.DeviceInfo" ref="https://api.ok.nga911.com/api/v1/adr/device-info/by-rfa/39" xmlns="urn:ietf:params:xml:ns:pidf"/>
                                                <EmergencyCallDataReference purpose="EmergencyCallData.ServiceInfo" ref="https://api.ok.nga911.com/api/v1/adr/service-info/by-rfa/39" xmlns="urn:ietf:params:xml:ns:pidf"/>
                                                <EmergencyCallDataReference purpose="EmergencyCallData.SubscriberInfo" ref="https://api.ok.nga911.com/api/v1/adr/subscriber-info/by-rfa/39" xmlns="urn:ietf:params:xml:ns:pidf"/>
                                                <EmergencyCallDataReference purpose="EmergencyCallData.Comment" ref="https://api.ok.nga911.com/api/v1/adr/comment/by-rfa/39" xmlns="urn:ietf:params:xml:ns:pidf"/>
                                                <EmergencyCallDataValue xmlns="urn:ietf:params:xml:ns:EmergencyCallData">
                                                    <EmergencyCallData.DeviceInfo xmlns="urn:ietf:params:xml:ns:EmergencyCallData:DeviceInfo">
                                                        <DataProviderReference>urn:nena:NGA</DataProviderReference>
                                                        <DeviceClassification>NA</DeviceClassification>
                                                        <DeviceMfgr>NA</DeviceMfgr>
                                                        <DeviceModelNr>NA</DeviceModelNr>
                                                        <UniqueDeviceID TypeOfDeviceID="NA">NA</UniqueDeviceID>
                                                    </EmergencyCallData.DeviceInfo>
                                                    <EmergencyCallData.Comment xmlns="urn:ietf:params:xml:ns:EmergencyCallData:Comment">
                                                        <DataProviderReference>urn:nena:NGA</DataProviderReference>
                                                        <Comment xml:lang="en">Telephone number reaches our 24x7 Network Operations Center.</Comment>
                                                    </EmergencyCallData.Comment>
                                                    <EmergencyCallData.ServiceInfo xmlns="urn:ietf:params:xml:ns:EmergencyCallData:ServiceInfo" xmlns:default="en-US">
                                                        <DataProviderReference/>
                                                        <ServiceEnvironment>Unknown</ServiceEnvironment>
                                                        <ServiceType>wireless</ServiceType>
                                                        <ServiceMobility>Mobile</ServiceMobility>
                                                        <default:legacy_class_of_service xmlns="en-US">8</default:legacy_class_of_service>
                                                    </EmergencyCallData.ServiceInfo>
                                                    <EmergencyCallData.ProviderInfo xmlns="urn:ietf:params:xml:ns:EmergencyCallData:ProviderInfo">
                                                        <DataProviderReference>urn:nena:NGA</DataProviderReference>
                                                        <DataProviderString>NGA</DataProviderString>
                                                        <ProviderID>urn:nena:companyid:NGA</ProviderID>
                                                        <ProviderIDSeries>NENA</ProviderIDSeries>
                                                        <TypeOfProvider>Emergency Service Provider</TypeOfProvider>
                                                        <ContactURI>tel:</ContactURI>
                                                        <Language>en</Language>
                                                    </EmergencyCallData.ProviderInfo>
                                                    <EmergencyCallData.SubscriberInfo privacyRequested="false" xmlns="urn:ietf:params:xml:ns:EmergencyCallData:SubscriberInfo" xmlns:vc="urn:ietf:params:xml:ns:vcard-4.0">
                                                        <DataProviderReference>NGA</DataProviderReference>
                                                        <SubscriberData>
                                                            <vc:vcard xmlns="urn:ietf:params:xml:ns:vcard-4.0">
                                                                <vc:fn>
                                                                    <vc:text>Solacome Test</vc:text>
                                                                </vc:fn>
                                                                <vc:n>
                                                                    <vc:surname>Solacome Test</vc:surname>
                                                                    <vc:given>Solacome Test</vc:given>
                                                                    <vc:additional/>
                                                                    <vc:prefix/>
                                                                    <vc:suffix/>
                                                                </vc:n>
                                                                <vc:lang>
                                                                    <vc:parameters>
                                                                        <vc:pref>
                                                                            <vc:integer>1</vc:integer>
                                                                        </vc:pref>
                                                                    </vc:parameters>
                                                                    <vc:language-tag>fr</vc:language-tag>
                                                                </vc:lang>
                                                                <vc:lang>
                                                                    <vc:parameters>
                                                                        <vc:pref>
                                                                            <vc:integer>2</vc:integer>
                                                                        </vc:pref>
                                                                    </vc:parameters>
                                                                    <vc:language-tag>en</vc:language-tag>
                                                                </vc:lang>
                                                                <vc:adr>
                                                                    <vc:parameters>
                                                                        <vc:type>
                                                                            <vc:text>work</vc:text>
                                                                        </vc:type>
                                                                        <vc:label>
                                                                            <vc:text>0Remington Pl Oklahoma CityOklahoma, , 73111US</vc:text>
                                                                        </vc:label>
                                                                    </vc:parameters>
                                                                    <vc:pobox/>
                                                                    <vc:ext/>
                                                                    <vc:street/>
                                                                    <vc:locality/>
                                                                    <vc:region/>
                                                                    <vc:code/>
                                                                    <vc:country>US</vc:country>
                                                                </vc:adr>
                                                                <vc:tel>
                                                                    <vc:parameters>
                                                                        <vc:type>
                                                                            <vc:text>work</vc:text>
                                                                            <vc:text>voice</vc:text>
                                                                            <vc:text>video</vc:text>
                                                                        </vc:type>
                                                                    </vc:parameters>
                                                                    <vc:uri>tel:5129889495</vc:uri>
                                                                </vc:tel>
                                                                <vc:email>
                                                                    <vc:parameters>
                                                                        <vc:type>
                                                                            <vc:text>work</vc:text>
                                                                        </vc:type>
                                                                    </vc:parameters>
                                                                    <vc:text>5129889495</vc:text>
                                                                </vc:email>
                                                                <vc:key>
                                                                    <vc:parameters>
                                                                        <vc:type>
                                                                            <vc:text>work</vc:text>
                                                                        </vc:type>
                                                                    </vc:parameters>
                                                                    <vc:uri>nga911.com</vc:uri>
                                                                </vc:key>
                                                                <vc:tz>
                                                                    <vc:text>America</vc:text>
                                                                </vc:tz>
                                                            </vc:vcard>
                                                        </SubscriberData>
                                                    </EmergencyCallData.SubscriberInfo>
                                                </EmergencyCallDataValue>
                                            </provided-by>
                                            <method>Wiremap</method>
                                        </geopriv>
                                    </status>
                                    <timestamp>2023-02-08T09:49:53</timestamp>
                                </tuple>
                            </presence>
                        </locationResponse>
                    </held>
        </heldResponse>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:31:36.098Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>HELDquery</eventType>
        <heldQuery>
                <mediaLabel>_ML_TESTCASE_2.1_@tng000</mediaLabel>
                <heldDomain>**********</heldDomain>
                <heldPurpose>AutoRebid</heldPurpose>
                <held-uri>tel:8195551002</held-uri>
        </heldQuery>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:31:26.122Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>HELDresponse</eventType>
        <heldResponse>
                <mediaLabel>_ML_TESTCASE_2.1_@tng000</mediaLabel>
                <heldDomain>**********</heldDomain>
                <responseCode>200</responseCode>
                <held>
                        <locationResponse xmlns="urn:ietf:params:xml:ns:geopriv:held" xmlns:ca="urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr" xmlns:gml="http://www.opengis.net/gml" xmlns:ns4="urn:ietf:params:xml:ns:pidf" xmlns:ns5="urn:ietf:params:xml:ns:pidf:data-model" xmlns:ns6="urn:ietf:params:xml:ns:pidf:geopriv10" xmlns:ns7="urn:ietf:params:xml:ns:pidf:geopriv10:basicPolicy" xmlns:ns8="http://www.opengis.net/pidflo/1.0" xmlns:ns9="urn:ietf:params:xml:ns:geopriv:held:id">
                            <locationUriSet expires="2022-09-27T15:05:21" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                <locationUri>https://ls.example.com:9768/357yc6s64ceyoiuy5ax3o</locationUri>
                                <locationUri>sip:<EMAIL>:</locationUri>
                            </locationUriSet>
                            <presence entity="pres:<EMAIL>" xmlns="urn:ietf:params:xml:ns:pidf" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                <tuple id="lisLocation">
                                    <status>
                                        <geopriv xmlns="urn:ietf:params:xml:ns:pidf:geopriv10">
                                            <location-info>
                                                 <ns8:Circle srsName="urn:ogc:def:crs:EPSG::4326">
                                                      <gml:pos>-34.410649 150.87651</gml:pos>
                                                      <ns8:radius uom="urn:ogc:def:uom:EPSG::9001">
                                                        30
                                                      </ns8:radius>
                                                    </ns8:Circle>
                                            </location-info>
                                            <usage-rules>
                                                <retransmission-allowed>true</retransmission-allowed>
                                                <retention-expiry>2022-09-27T15:05:21</retention-expiry>
                                            </usage-rules>
                                            <method>Cell123</method>
                                        </geopriv>
                                    </status>
                                    <timestamp>2022-09-27T15:05:21</timestamp>
                                </tuple>
                            </presence>
                        </locationResponse>
                    </held>
        </heldResponse>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:31:26.097Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>HELDquery</eventType>
        <heldQuery>
                <mediaLabel>_ML_TESTCASE_2.1_@tng000</mediaLabel>
                <heldDomain>**********</heldDomain>
                <heldPurpose>AutoRebid</heldPurpose>
                <held-uri>tel:8195551002</held-uri>
        </heldQuery>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:31:16.158Z</timestamp>
        <agencyOrElement>tng000</agencyOrElement>
        <agent>op4</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>Answer</eventType>
        <answer>
                <mediaLabel>_ML_18626B57E50V00002409@tng000</mediaLabel>
                <uri>tel:+7775553004</uri>
                <agentRole>Admin</agentRole>
                <tenantGroup>tng000</tenantGroup>
                <operatorId>0</operatorId>
                <workstation>PORSCHE-004</workstation>
        </answer>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:31:16.121Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>HELDresponse</eventType>
        <heldResponse>
                <mediaLabel>_ML_TESTCASE_2.1_@tng000</mediaLabel>
                <heldDomain>**********</heldDomain>
                <responseCode>200</responseCode>
                <held>
                        <locationResponse xmlns="urn:ietf:params:xml:ns:geopriv:held" xmlns:ca="urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr" xmlns:gml="http://www.opengis.net/gml" xmlns:ns4="urn:ietf:params:xml:ns:pidf" xmlns:ns5="urn:ietf:params:xml:ns:pidf:data-model" xmlns:ns6="urn:ietf:params:xml:ns:pidf:geopriv10" xmlns:ns7="urn:ietf:params:xml:ns:pidf:geopriv10:basicPolicy" xmlns:ns8="http://www.opengis.net/pidflo/1.0" xmlns:ns9="urn:ietf:params:xml:ns:geopriv:held:id">
                            <locationUriSet expires="2022-09-27T15:05:21" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                <locationUri>https://ls.example.com:9768/357yc6s64ceyoiuy5ax3o</locationUri>
                                <locationUri>sip:<EMAIL>:</locationUri>
                            </locationUriSet>
                            <presence entity="pres:<EMAIL>" xmlns="urn:ietf:params:xml:ns:pidf" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                <tuple id="lisLocation">
                                    <status>
                                        <geopriv xmlns="urn:ietf:params:xml:ns:pidf:geopriv10">
                                            <location-info>
                                                <gml:Point srsName="urn:ogc:def:crs:EPSG::4326">
                                                    <gml:pos>28.113055 -81.64747</gml:pos>
                                                </gml:Point>
                                                <ca:civicAddress xml:lang="en-us" xmlns:ca="urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr">
                                                    <ca:country>AU</ca:country>
                                                    <ca:A1>NSW</ca:A1>
                                                    <ca:A2>354</ca:A2>
                                                    <ca:A3>Wollongon</ca:A3>
                                                    <ca:A4>Gwynneville</ca:A4>
                                                    <ca:STS>Northfield Avenue</ca:STS>
                                                    <ca:LMK>University of Wollongong</ca:LMK>
                                                    <ca:NAM>Andrew Corporation sant..</ca:NAM>
                                                    <ca:PC>23445</ca:PC>
                                                    <ca:BLD>12</ca:BLD>
                                                    <ca:FLR>2</ca:FLR>
                                                    <ca:POBOX>U40</ca:POBOX>
                                                    <ca:SEAT>WS-183</ca:SEAT>
                                                    <ca:RD>Raccoon Valley</ca:RD>
                                                </ca:civicAddress>
                                                <confidence xmlns="urn:ietf:params:xml:ns:geopriv:conf">18</confidence>
                                            </location-info>
                                            <usage-rules>
                                                <retransmission-allowed>true</retransmission-allowed>
                                                <retention-expiry>2022-09-27T15:05:21</retention-expiry>
                                            </usage-rules>
                                            <method>Cell123</method>
                                        </geopriv>
                                    </status>
                                    <timestamp>2022-09-27T15:05:21</timestamp>
                                    <contact>5552223333</contact>
                                </tuple>
                            </presence>
                        </locationResponse>
                    </held>
        </heldResponse>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:31:16.096Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>HELDquery</eventType>
        <heldQuery>
                <mediaLabel>_ML_TESTCASE_2.1_@tng000</mediaLabel>
                <heldDomain>**********</heldDomain>
                <heldPurpose>AutoRebid</heldPurpose>
                <held-uri>tel:8195551002</held-uri>
        </heldQuery>
</LogEvent>


<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:31:14.551Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>Answer</eventType>
        <answer>
                <mediaLabel>_ML_TESTCASE_2.1_@tng000</mediaLabel>
                <uri>tel:+8195551002</uri>
                <agentRole>.</agentRole>
                <tenantGroup>.</tenantGroup>
                <operatorId>-1</operatorId>
                <workstation>.</workstation>
        </answer>
</LogEvent>


<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:31:14.543Z</timestamp>
        <agencyOrElement>tng000</agencyOrElement>
        <agent>op4</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>Media</eventType>
        <media>
                <udp>.</udp>
                <mediaLabel>_ML_18626B57E50V00002409@tng000</mediaLabel>
        </media>
</LogEvent>


<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:31:14.539Z</timestamp>
        <agencyOrElement>tng000</agencyOrElement>
        <agent>op4</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>Route</eventType>
        <route>
                <uri>7775553004</uri> 
                <rule>rule #67</rule>
                <reason>normal</reason>
                <mediaLabel>_ML_18626B57E50V00002409@tng000</mediaLabel>
                <attempt>1</attempt>
                <priority>-1</priority>
                <ani>8195551002</ani>
                <aniDomain>*********</aniDomain>
                <dnis>7775553004</dnis>
                <pani>8195551002</pani>
                <esrn>0009991504</esrn>
                <callerName>Preserve Call Test</callerName>
                <aniTranslated>8195551002</aniTranslated>
                <dnisTranslated>7775553004</dnisTranslated>
                <callerNameTranslated>Preserve Call Test</callerNameTranslated>
        </route>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:31:14.479Z</timestamp>
        <agencyOrElement>tng000</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>Route</eventType>
        <route>
                <uri>9991504</uri> 
                <rule>rule #157</rule>
                <reason>normal</reason>
                <mediaLabel>.</mediaLabel>
                <attempt>1</attempt>
                <priority>-1</priority>
                <ani>8195551002</ani>
                <aniDomain>*********</aniDomain>
                <dnis>0009991504</dnis>
                <pani>8195551002</pani>
                <esrn>0009991504</esrn>
                <callerName>Preserve Call Test</callerName>
                <aniTranslated>8195551002</aniTranslated>
                <dnisTranslated>9991504</dnisTranslated>
                <callerNameTranslated>Preserve Call Test</callerNameTranslated>
        </route>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:31:14.376Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>HELDresponse</eventType>
        <heldResponse>
                <mediaLabel>_ML_TESTCASE_2.1_@tng000</mediaLabel>
                <heldDomain>**********</heldDomain>
                <responseCode>200</responseCode>
                <held>
    <locationResponse xmlns="urn:ietf:params:xml:ns:geopriv:held" xmlns:ca="urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr" xmlns:gml="http://www.opengis.net/gml" xmlns:ns4="urn:ietf:params:xml:ns:pidf" xmlns:ns5="urn:ietf:params:xml:ns:pidf:data-model" xmlns:ns6="urn:ietf:params:xml:ns:pidf:geopriv10" xmlns:ns7="urn:ietf:params:xml:ns:pidf:geopriv10:basicPolicy" xmlns:ns8="http://www.opengis.net/pidflo/1.0" xmlns:ns9="urn:ietf:params:xml:ns:geopriv:held:id">
        <locationUriSet expires="2023-02-06T07:55:42" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <locationUri>https://ls.example.com:9768/357yc6s64ceyoiuy5ax3o</locationUri>
            <locationUri>sip:<EMAIL>:</locationUri>
        </locationUriSet>
        <presence entity="pres:<EMAIL>" xmlns="urn:ietf:params:xml:ns:pidf" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <tuple id="lisLocation">
                <status>
                    <geopriv xmlns="urn:ietf:params:xml:ns:pidf:geopriv10">
                        <location-info>
                            <ca:civicAddress xml:lang="en-us" xmlns:ca="urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr">
                                <ca:country>TX</ca:country>
                                <ca:A1>TX</ca:A1>
                                <ca:A2>dddd</ca:A2>
                                <ca:A3>Wollongon</ca:A3>
                                <ca:A4>Gwynneville</ca:A4>
                                <ca:A5>testCounty</ca:A5>
                                <ca:STS>JeanProulx s  curit  </ca:STS>
                                <ca:LMK>University of Wollongong</ca:LMK>
                                <ca:NAM>Andrew Corporation</ca:NAM>
                                <ca:FLR>2</ca:FLR>
                                <ca:POBOX>U40</ca:POBOX>
                                <ca:SEAT>WS-183</ca:SEAT>
                            </ca:civicAddress>
                        </location-info>
                        <usage-rules>
                            <retransmission-allowed>true</retransmission-allowed>
                            <retention-expiry>2023-02-06T07:55:42</retention-expiry>
                        </usage-rules>
                        <method>Wiremap</method>
                    </geopriv>
                </status>
                <timestamp>2023-02-06T07:55:42</timestamp>
            </tuple>
        </presence>
    </locationResponse>
</held>
        </heldResponse>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:31:14.235Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>HELDquery</eventType>
        <heldQuery>
                <mediaLabel>_ML_TESTCASE_2.1_@tng000</mediaLabel>
                <heldDomain>**********</heldDomain>
                <heldPurpose>Dereferencing</heldPurpose>
                <held-uri>tel:8195551002</held-uri>
        </heldQuery>
</LogEvent>


<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:31:14.228Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>Media</eventType>
        <media>
                <udp>v=0  o=user1 53655765 2353687637 IN IP 4 *********  s=Sip Call  c=IN IP4 *********  t=0 0  m=audio 6000 RTP/AVP 0 101  a=rtpmap:0 PCMU/8000  a=rtpmap:101 telephone-event/8000  a=sendrecv  </udp>
                <mediaLabel>_ML_TESTCASE_2.1_@tng000</mediaLabel>
        </media>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:31:14.224Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_2.1_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_2.1_@tng000</incidentIdentifier>
        <eventType>StartCall</eventType>
        <startCall>
                <header><![CDATA[INVITE sip:779@*********** SIP/2.0  Via: SIP/2.0/UDP ***********:5060;branch=z9hG4bK0242.bcec298.0  To: sip:779@***********  From: "Preserve Call Test" <sip:8195551002@*********:5060>;tag=9b082611d8edbb45dc1367c468a3b51a-3d14  CSeq: 2 INVITE  Call-ID: B2B.8.110070.1675686673  Max-Forwards: 70  Content-Length: 188  User-Agent: OpenSIPS (2.4.6 (x86_64/linux))  Content-Type: application/sdp  Initial-CallID: 1-526916@*********  Contact: <sip:***********:5060>  Geolocation: <http://**********/I3Services/i3service.asmx/held>]]></header>
                <location>.</location>
                <mediaLabel>_ML_TESTCASE_2.1_@tng000</mediaLabel>
                <incomingCallPolicy>167_ESRP_REGIONAL_TEST</incomingCallPolicy>
                <callType>E911</callType>
                <signallingType>VOIP</signallingType>
                <circuit>30/02/00/0028</circuit>
                <circuitId>126091292</circuitId>
                <trunkGroupId>300</trunkGroupId>
                <ani>8195551002</ani>
                <aniDomain>*********</aniDomain>
                <dnis>779</dnis>
                <dnisDomain>***********</dnisDomain>
                <pani>8195551002</pani>
                <esrn>.</esrn>
                <callerName>Preserve Call Test</callerName>
        </startCall>
</LogEvent>

</LogEvents>
