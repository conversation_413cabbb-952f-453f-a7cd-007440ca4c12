{"info": {"_postman_id": "8312788f-8ab1-4f2d-a160-19ce0309c4d7", "name": "Insights ELM Adapter (Ottawa)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "StartCall", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"response is 200.\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["//Global related variables that are used for the given iteration group.\r", "pm.collectionVariables.set(\"IterationCounter\", pm.info.iteration);\r", "\r", "const moment = require('moment');\r", "var id = moment().format(\"YYYYMMDDHHmmssSSS\") + \"_\" + pm.collectionVariables.get(\"PSAPName\") +\"_\" + pm.collectionVariables.get(\"IterationCounter\");\r", "//setting callid for the iteration loop\r", "pm.collectionVariables.set(\"CallID\", \"_CI_dev_\" + id);\r", "pm.collectionVariables.set(\"IncidentID\", \"_II_dev_\" + id);\r", "\r", "\r", "//Local variable set for each event.\r", "var now = new Date();\r", "pm.variables.set(\"timestamp\", now.toISOString());\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<LogEvent xmlns=\"http://solacom.com/Logging\">\r\n        <timestamp>{{timestamp}}</timestamp>\r\n        <agencyOrElement>{{PSAPName}}-A_A</agencyOrElement>\r\n        <agent>operator</agent>\r\n        <callIdentifier>{{CallID}}</callIdentifier>\r\n        <incidentIdentifier>{{IncidentID}}</incidentIdentifier>\r\n        <eventType>StartCall</eventType>\r\n         <startCall>\r\n\t\t <header><![CDATA[INVITE sip:9991@************ SIP/2.0  Via: SIP/2.0/UDP ************:5060;branch=z9hG4bK-d8754z-2d4e8660672ae54f-1---d8754z-;rport  Max-Forwards: 70  Contact: <sip:8197720005@************:5060>  To: <sip:9991@************>  From: <sip:8197720005@***********>;tag=e7625571  Call-ID: YmFlYjQyMjkyOWM3NjJlMmZjYjNjNzA3ODViZmFkMTA.  CSeq: 1 INVITE  Allow: INVITE, ACK, CANCEL, OPTIONS, BYE, REFER, INFO, NOTIFY  Content-Type: application/sdp  Supported: replaces  User-Agent: ipTTY  Content-Length: 330]]></header>\r\n\t\t <location>.</location>\r\n\t\t <mediaLabel>_ML_dev_{{IterationCounter}}</mediaLabel>\r\n\t\t <incomingCallPolicy>.</incomingCallPolicy>\r\n\t\t <callType>.</callType>\r\n\t\t <signallingType>VOIP</signallingType>\r\n\t\t <circuit>31/01/00/0000</circuit>\r\n\t\t <circuitId>130154496</circuitId>\r\n\t\t <trunkGroupId>300</trunkGroupId>\r\n\t\t <ani>8197720005</ani>\r\n\t\t <aniDomain>************</aniDomain>\r\n\t\t <dnis>9991</dnis>\r\n\t\t <dnisDomain>************</dnisDomain>\r\n\t\t <pani>8197720005</pani>\r\n\t\t <esrn>.</esrn>\r\n\t\t <callerName>n/a</callerName>\r\n\t\t <concurrentCalls>-1</concurrentCalls>\r\n         </startCall>\r\n</LogEvent>"}, "url": {"raw": "http://{{ELMAdapter_URL}}/api/event", "protocol": "http", "host": ["{{ELMAdapter_URL}}"], "path": ["api", "event"]}}, "response": []}, {"name": "Media", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"response is 200.\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var now = new Date();\r", "pm.variables.set(\"timestamp\", now.toISOString());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<LogEvent xmlns=\"http://solacom.com/Logging\">\r\n        <timestamp>{{timestamp}}</timestamp>\r\n        <agencyOrElement>{{PSAPName}}-A_A</agencyOrElement>\r\n        <agent>.</agent>\r\n        <callIdentifier>{{CallID}}</callIdentifier>\r\n        <incidentIdentifier>{{IncidentID}}</incidentIdentifier>\r\n        <eventType>Media</eventType>\r\n        <media>\r\n                <udp>v=0  o=user1 53655765 2353687637 IN IP 4 *********  s=Sip Call  c=IN IP4 *********  t=0 0  m=audio 6000 RTP/AVP 0 101  a=rtpmap:0 PCMU/8000  a=rtpmap:101 telephone-event/8000  a=sendrecv  </udp>\r\n                <mediaLabel>_ML_dev_{{IterationCounter}}</mediaLabel>\r\n        </media>\r\n</LogEvent>"}, "url": {"raw": "http://{{ELMAdapter_URL}}/api/event", "protocol": "http", "host": ["{{ELMAdapter_URL}}"], "path": ["api", "event"]}}, "response": []}, {"name": "HELDquery", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"response is 200.\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var now = new Date();\r", "pm.variables.set(\"timestamp\", now.toISOString());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<LogEvent xmlns=\"http://solacom.com/Logging\">\r\n        <timestamp>{{timestamp}}</timestamp>\r\n        <agencyOrElement>{{PSAPName}}-A_A</agencyOrElement>\r\n        <agent>.</agent>\r\n        <callIdentifier>{{CallID}}</callIdentifier>\r\n        <incidentIdentifier>{{IncidentID}}</incidentIdentifier>\r\n        <eventType>HELDquery</eventType>\r\n        <heldQuery>\r\n                <mediaLabel>_ML_dev_{{IterationCounter}}</mediaLabel>\r\n                <heldDomain>**********</heldDomain>\r\n                <heldPurpose>Dereferencing</heldPurpose>\r\n                <held-uri>tel:8195551002</held-uri>\r\n        </heldQuery>\r\n</LogEvent>"}, "url": {"raw": "http://{{ELMAdapter_URL}}/api/event", "protocol": "http", "host": ["{{ELMAdapter_URL}}"], "path": ["api", "event"]}}, "response": []}, {"name": "HELDresponse", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"response is 200.\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var now = new Date();\r", "pm.variables.set(\"timestamp\", now.toISOString());\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<LogEvent xmlns=\"http://solacom.com/Logging\">\r\n        <timestamp>{{timestamp}}</timestamp>\r\n        <agencyOrElement>{{PSAPName}}-A_A</agencyOrElement>\r\n        <agent>.</agent>\r\n        <callIdentifier>{{CallID}}</callIdentifier>\r\n        <incidentIdentifier>{{IncidentID}}</incidentIdentifier>\r\n        <eventType>HELDresponse</eventType>\r\n        <heldResponse>\r\n                <mediaLabel>_ML_dev_{{IterationCounter}}</mediaLabel>\r\n                <heldDomain>**********</heldDomain>\r\n                <responseCode>200</responseCode>\r\n                <held><locationResponse xmlns=\"urn:ietf:params:xml:ns:geopriv:held\" xmlns:gml=\"http://www.opengis.net/gml\" xmlns:ns4=\"urn:ietf:params:xml:ns:pidf\" xmlns:ns5=\"urn:ietf:params:xml:ns:pidf:data-model\" xmlns:ns6=\"urn:ietf:params:xml:ns:pidf:geopriv10\" xmlns:ca=\"urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr\" xmlns:ns7=\"urn:ietf:params:xml:ns:pidf:geopriv10:basicPolicy\" xmlns:ns8=\"http://www.opengis.net/pidflo/1.0\" xmlns:ns9=\"urn:ietf:params:xml:ns:geopriv:held:id\"><locationUriSet expires=\"2023-02-06T07:55:42\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><locationUri>https://ls.example.com:9768/357yc6s64ceyoiuy5ax3o</locationUri><locationUri>sip:<EMAIL>:</locationUri></locationUriSet><presence entity=\"pres:<EMAIL>\" xmlns=\"urn:ietf:params:xml:ns:pidf\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"><tuple id=\"lisLocation\"><status><geopriv xmlns=\"urn:ietf:params:xml:ns:pidf:geopriv10\"><location-info><ca:civicAddress xmlns:ca=\"urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr\" xml:lang=\"en-us\"><ca:country>TX</ca:country><ca:A1>TX</ca:A1><ca:A2>dddd</ca:A2><ca:A3>Wollongon</ca:A3><ca:A4>Gwynneville</ca:A4><ca:A5>testCounty</ca:A5><ca:STS>JeanProulx s  curit  </ca:STS><ca:LMK>University of Wollongong</ca:LMK><ca:NAM>Andrew Corporation</ca:NAM><ca:FLR>2</ca:FLR><ca:POBOX>U40</ca:POBOX><ca:SEAT>WS-183</ca:SEAT></ca:civicAddress></location-info><usage-rules><retransmission-allowed>true</retransmission-allowed><retention-expiry>2023-02-06T07:55:42</retention-expiry></usage-rules><method>Wiremap</method></geopriv></status><timestamp>2023-02-06T07:55:42</timestamp></tuple></presence></locationResponse></held>\r\n        </heldResponse>\r\n</LogEvent>\r\n"}, "url": {"raw": "http://{{ELMAdapter_URL}}/api/event", "protocol": "http", "host": ["{{ELMAdapter_URL}}"], "path": ["api", "event"]}}, "response": []}, {"name": "Route", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"response is 200.\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var now = new Date();\r", "pm.variables.set(\"timestamp\", now.toISOString());\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<LogEvent xmlns=\"http://solacom.com/Logging\">\r\n        <timestamp>{{timestamp}}</timestamp>\r\n        <agencyOrElement>{{PSAPName}}</agencyOrElement>\r\n        <agent>.</agent>\r\n        <callIdentifier>{{CallID}}</callIdentifier>\r\n        <incidentIdentifier>{{IncidentID}}</incidentIdentifier>\r\n        <eventType>Route</eventType>\r\n        <route>\r\n                <uri>9991504</uri> \r\n                <rule>rule #157</rule>\r\n                <reason>normal</reason>\r\n                <mediaLabel>.</mediaLabel>\r\n                <attempt>1</attempt>\r\n                <priority>-1</priority>\r\n                <ani>8195551002</ani>\r\n                <aniDomain>*********</aniDomain>\r\n                <dnis>0009991504</dnis>\r\n                <pani>8195551002</pani>\r\n                <esrn>0009991504</esrn>\r\n                <callerName>Preserve Call Test</callerName>\r\n                <aniTranslated>8195551002</aniTranslated>\r\n                <dnisTranslated>9991504</dnisTranslated>\r\n                <callerNameTranslated>Preserve Call Test</callerNameTranslated>\r\n        </route>\r\n</LogEvent>\r\n\r\n"}, "url": {"raw": "http://{{ELMAdapter_URL}}/api/event", "protocol": "http", "host": ["{{ELMAdapter_URL}}"], "path": ["api", "event"]}}, "response": []}, {"name": "Route (#2)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"response is 200.\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var now = new Date();\r", "pm.variables.set(\"timestamp\", now.toISOString());\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<LogEvent xmlns=\"http://solacom.com/Logging\">\r\n        <timestamp>{{timestamp}}</timestamp>\r\n        <agencyOrElement>{{PSAPName}}</agencyOrElement>\r\n        <agent>operatorAgent01</agent>\r\n        <callIdentifier>{{CallID}}</callIdentifier>\r\n        <incidentIdentifier>{{IncidentID}}</incidentIdentifier>\r\n        <eventType>Route</eventType>\r\n        <route>\r\n                <uri>7775553004</uri> \r\n                <rule>rule #67</rule>\r\n                <reason>normal</reason>\r\n                <mediaLabel>_ML_dev_2_{{IterationCounter}}</mediaLabel>\r\n                <attempt>1</attempt>\r\n                <priority>-1</priority>\r\n                <ani>8195551002</ani>\r\n                <aniDomain>*********</aniDomain>\r\n                <dnis>7775553004</dnis>\r\n                <pani>8195551002</pani>\r\n                <esrn>0009991504</esrn>\r\n                <callerName>Preserve Call Test</callerName>\r\n                <aniTranslated>8195551002</aniTranslated>\r\n                <dnisTranslated>7775553004</dnisTranslated>\r\n                <callerNameTranslated>Preserve Call Test</callerNameTranslated>\r\n        </route>\r\n</LogEvent>"}, "url": {"raw": "http://{{ELMAdapter_URL}}/api/event", "protocol": "http", "host": ["{{ELMAdapter_URL}}"], "path": ["api", "event"]}}, "response": []}, {"name": "Media (#2)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"response is 200.\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var now = new Date();\r", "pm.variables.set(\"timestamp\", now.toISOString());\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<LogEvent xmlns=\"http://solacom.com/Logging\">\r\n        <timestamp>{{timestamp}}</timestamp>\r\n        <agencyOrElement>{{PSAPName}}</agencyOrElement>\r\n        <agent>operatorAgent01</agent>\r\n        <callIdentifier>{{CallID}}</callIdentifier>\r\n        <incidentIdentifier>{{IncidentID}}</incidentIdentifier>\r\n        <eventType>Media</eventType>\r\n        <media>\r\n            <udp>.</udp>\r\n            <mediaLabel>_ML_dev_2_{{IterationCounter}}</mediaLabel>\r\n        </media>\r\n</LogEvent>"}, "url": {"raw": "http://{{ELMAdapter_URL}}/api/event", "protocol": "http", "host": ["{{ELMAdapter_URL}}"], "path": ["api", "event"]}}, "response": []}, {"name": "Answer", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"response is 200.\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var now = new Date();\r", "pm.variables.set(\"timestamp\", now.toISOString());\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<LogEvent xmlns=\"http://solacom.com/Logging\">\r\n        <timestamp>{{timestamp}}</timestamp>\r\n        <agencyOrElement>{{PSAPName}}-A_A</agencyOrElement>\r\n        <agent>.</agent>\r\n        <callIdentifier>{{CallID}}</callIdentifier>\r\n        <incidentIdentifier>{{IncidentID}}</incidentIdentifier>\r\n        <eventType>Answer</eventType>\r\n        <answer>\r\n                <mediaLabel>_ML_dev_{{IterationCounter}}</mediaLabel>\r\n                <uri>tel:+8195551002</uri>\r\n                <agentRole>.</agentRole>\r\n                <tenantGroup>.</tenantGroup>\r\n                <operatorId>-1</operatorId>\r\n                <workstation>.</workstation>\r\n        </answer>\r\n</LogEvent>"}, "url": {"raw": "http://{{ELMAdapter_URL}}/api/event", "protocol": "http", "host": ["{{ELMAdapter_URL}}"], "path": ["api", "event"]}}, "response": []}, {"name": "EndMedia (#2)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"response is 200.\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var now = new Date();\r", "pm.variables.set(\"timestamp\", now.toISOString());\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<LogEvent xmlns=\"http://solacom.com/Logging\">\r\n        <timestamp>{{timestamp}}</timestamp>\r\n        <agencyOrElement>{{PSAPName}}</agencyOrElement>\r\n        <agent>operatorAgent01</agent>\r\n        <callIdentifier>{{CallID}}</callIdentifier>\r\n        <incidentIdentifier>{{IncidentID}}</incidentIdentifier>\r\n        <eventType>EndMedia</eventType>\r\n        <endMedia>\r\n            <mediaLabel>_ML_dev_2_{{IterationCounter}}</mediaLabel>\r\n            <responseCode>41</responseCode>\r\n            <disconnectReason></disconnectReason>\r\n            <voiceQOS>\r\n                    <mediaIpSourceAddr>.</mediaIpSourceAddr> \r\n                    <mediaIpDestAddr>.</mediaIpDestAddr>\r\n                    <mediaUdpRtpSourcePort>-1</mediaUdpRtpSourcePort> \r\n                    <mediaUdpRtpDestPort>-1</mediaUdpRtpDestPort>\r\n                    <mediaNumOfIpPktRxed>-1</mediaNumOfIpPktRxed> \r\n                    <mediaNumOfIpPktTxed>-1</mediaNumOfIpPktTxed>\r\n                    <mediaNumOfIpErroredPktRxed>-1</mediaNumOfIpErroredPktRxed>\r\n                    <mediaNumOfRtpPktRxed>-1</mediaNumOfRtpPktRxed>\r\n                    <mediaNumOfRtpPktTxed>-1</mediaNumOfRtpPktTxed>\r\n                    <mediaNumOfRtpPktLost>-1</mediaNumOfRtpPktLost>\r\n                    <mediaNumOfRtpPktDiscarded>-1</mediaNumOfRtpPktDiscarded>\r\n                    <mediaRtpJitter>-1</mediaRtpJitter>\r\n                    <mediaRtpLatency>-1</mediaRtpLatency>\r\n                    <mediaNumOfRtcpPktRxed>-1</mediaNumOfRtcpPktRxed>\r\n                    <mediaNumOfRtcpPktTxed>-1</mediaNumOfRtcpPktTxed> \r\n                    <mediaFarEndPacketLostPercentage>-1</mediaFarEndPacketLostPercentage>\r\n                    <mediaFarEndCumulativePacketLost>-1</mediaFarEndCumulativePacketLost> \r\n                    <mediaFarEndInterarrivalJitter>-1</mediaFarEndInterarrivalJitter> \r\n            </voiceQOS>\r\n        </endMedia>\r\n</LogEvent>\r\n"}, "url": {"raw": "http://{{ELMAdapter_URL}}/api/event", "protocol": "http", "host": ["{{ELMAdapter_URL}}"], "path": ["api", "event"]}}, "response": []}, {"name": "EndMedia", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"response is 200.\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var now = new Date();\r", "pm.variables.set(\"timestamp\", now.toISOString());\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<LogEvent xmlns=\"http://solacom.com/Logging\">\r\n        <timestamp>{{timestamp}}</timestamp>\r\n        <agencyOrElement>{{PSAPName}}-A_A</agencyOrElement>\r\n        <agent>.</agent>\r\n        <callIdentifier>{{CallID}}</callIdentifier>\r\n        <incidentIdentifier>{{IncidentID}}</incidentIdentifier>\r\n        <eventType>EndMedia</eventType>\r\n        <endMedia>\r\n                <mediaLabel>_ML_dev_{{IterationCounter}}</mediaLabel>\r\n                <responseCode>41</responseCode>\r\n                <disconnectReason></disconnectReason>\r\n                <voiceQOS>\r\n                        <mediaIpSourceAddr>.</mediaIpSourceAddr> \r\n                        <mediaIpDestAddr>.</mediaIpDestAddr>\r\n                        <mediaUdpRtpSourcePort>-1</mediaUdpRtpSourcePort> \r\n                        <mediaUdpRtpDestPort>-1</mediaUdpRtpDestPort>\r\n                        <mediaNumOfIpPktRxed>-1</mediaNumOfIpPktRxed> \r\n                        <mediaNumOfIpPktTxed>-1</mediaNumOfIpPktTxed>\r\n                        <mediaNumOfIpErroredPktRxed>-1</mediaNumOfIpErroredPktRxed>\r\n                        <mediaNumOfRtpPktRxed>-1</mediaNumOfRtpPktRxed>\r\n                        <mediaNumOfRtpPktTxed>-1</mediaNumOfRtpPktTxed>\r\n                        <mediaNumOfRtpPktLost>-1</mediaNumOfRtpPktLost>\r\n                        <mediaNumOfRtpPktDiscarded>-1</mediaNumOfRtpPktDiscarded>\r\n                        <mediaRtpJitter>-1</mediaRtpJitter>\r\n                        <mediaRtpLatency>-1</mediaRtpLatency>\r\n                        <mediaNumOfRtcpPktRxed>-1</mediaNumOfRtcpPktRxed>\r\n                        <mediaNumOfRtcpPktTxed>-1</mediaNumOfRtcpPktTxed> \r\n                        <mediaFarEndPacketLostPercentage>-1</mediaFarEndPacketLostPercentage>\r\n                        <mediaFarEndCumulativePacketLost>-1</mediaFarEndCumulativePacketLost> \r\n                        <mediaFarEndInterarrivalJitter>-1</mediaFarEndInterarrivalJitter> \r\n                </voiceQOS>\r\n        </endMedia>\r\n</LogEvent>\r\n"}, "url": {"raw": "http://{{ELMAdapter_URL}}/api/event", "protocol": "http", "host": ["{{ELMAdapter_URL}}"], "path": ["api", "event"]}}, "response": []}, {"name": "Route (#3)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"response is 200.\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var now = new Date();\r", "pm.variables.set(\"timestamp\", now.toISOString());\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<LogEvent xmlns=\"http://solacom.com/Logging\">\r\n        <timestamp>{{timestamp}}</timestamp>\r\n        <agencyOrElement>{{PSAPName}}</agencyOrElement>\r\n        <agent>operatorAgent01</agent>\r\n        <callIdentifier>{{CallID}}</callIdentifier>\r\n        <incidentIdentifier>{{IncidentID}}</incidentIdentifier>\r\n        <eventType>Route</eventType>\r\n        <route>\r\n                <uri>7775553004</uri> \r\n                <rule>rule #67</rule>\r\n                <reason>alternate</reason>\r\n                <mediaLabel>_ML_dev_3_{{IterationCounter}}</mediaLabel>\r\n                <attempt>1</attempt>\r\n                <priority>-1</priority>\r\n                <ani>8195551002</ani>\r\n                <aniDomain>.</aniDomain>\r\n                <dnis>7775553004</dnis>\r\n                <pani>8195551002</pani>\r\n                <esrn>0009991504</esrn>\r\n                <callerName>Preserve Call Test</callerName>\r\n                <aniTranslated>8195551002</aniTranslated>\r\n                <dnisTranslated>7775553004</dnisTranslated>\r\n                <callerNameTranslated>Preserve Call Test</callerNameTranslated>\r\n        </route>\r\n</LogEvent>"}, "url": {"raw": "http://{{ELMAdapter_URL}}/api/event", "protocol": "http", "host": ["{{ELMAdapter_URL}}"], "path": ["api", "event"]}}, "response": []}, {"name": "Media (#3)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"response is 200.\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var now = new Date();\r", "pm.variables.set(\"timestamp\", now.toISOString());\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<LogEvent xmlns=\"http://solacom.com/Logging\">\r\n    <timestamp>{{timestamp}}</timestamp>\r\n    <agencyOrElement>{{PSAPName}}</agencyOrElement>\r\n    <agent>operatorAgent01</agent>\r\n    <callIdentifier>{{CallID}}</callIdentifier>\r\n    <incidentIdentifier>{{IncidentID}}</incidentIdentifier>\r\n    <eventType>Media</eventType>\r\n    <media>\r\n            <udp>.</udp>\r\n            <mediaLabel>_ML_dev_3_{{IterationCounter}}</mediaLabel>\r\n    </media>\r\n</LogEvent>\r\n"}, "url": {"raw": "http://{{ELMAdapter_URL}}/api/event", "protocol": "http", "host": ["{{ELMAdapter_URL}}"], "path": ["api", "event"]}}, "response": []}, {"name": "Answer (#2)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"response is 200.\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var now = new Date();\r", "pm.variables.set(\"timestamp\", now.toISOString());\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<LogEvent xmlns=\"http://solacom.com/Logging\">\r\n    <timestamp>{{timestamp}}</timestamp>\r\n    <agencyOrElement>{{PSAPName}}</agencyOrElement>\r\n    <agent>operatorAgent01</agent>\r\n    <callIdentifier>{{CallID}}</callIdentifier>\r\n    <incidentIdentifier>{{IncidentID}}</incidentIdentifier>\r\n    <eventType>Answer</eventType>\r\n    <answer>\r\n            <mediaLabel>_ML_dev_2_{{IterationCounter}}</mediaLabel>\r\n            <uri>tel:+7775553004</uri>\r\n            <agentRole>Admin</agentRole>\r\n            <tenantGroup>Tenant1</tenantGroup>\r\n            <operatorId>0</operatorId>\r\n            <workstation>PORSCHE-004</workstation>\r\n    </answer>\r\n</LogEvent>\r\n"}, "url": {"raw": "http://{{ELMAdapter_URL}}/api/event", "protocol": "http", "host": ["{{ELMAdapter_URL}}"], "path": ["api", "event"]}}, "response": []}, {"name": "EndCall", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"response is 200.\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var now = new Date();\r", "pm.variables.set(\"timestamp\", now.toISOString());\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<LogEvent xmlns=\"http://solacom.com/Logging\">\r\n    <timestamp>{{timestamp}}</timestamp>\r\n    <agencyOrElement>{{PSAPName}}-A_A</agencyOrElement>\r\n    <agent>.</agent>\r\n    <callIdentifier>{{CallID}}</callIdentifier>\r\n    <incidentIdentifier>{{IncidentID}}</incidentIdentifier>\r\n    <eventType>EndCall</eventType>\r\n    <endCall>\r\n            <responseCode>16</responseCode>\r\n            <callReplaced>No</callReplaced>\r\n    </endCall>\r\n</LogEvent>\r\n\r\n"}, "url": {"raw": "http://{{ELMAdapter_URL}}/api/event", "protocol": "http", "host": ["{{ELMAdapter_URL}}"], "path": ["api", "event"]}}, "response": []}, {"name": "EndMedia (#3)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"response is 200.\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var now = new Date();\r", "pm.variables.set(\"timestamp\", now.toISOString());\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<LogEvent xmlns=\"http://solacom.com/Logging\">\r\n    <timestamp>{{timestamp}}</timestamp>\r\n    <agencyOrElement>{{PSAPName}}</agencyOrElement>\r\n    <agent>operatorAgent01</agent>\r\n    <callIdentifier>{{CallID}}</callIdentifier>\r\n    <incidentIdentifier>{{IncidentID}}</incidentIdentifier>\r\n    <eventType>EndMedia</eventType>\r\n    <endMedia>\r\n        <mediaLabel>_ML_dev_3_{{IterationCounter}}</mediaLabel>\r\n        <responseCode>16</responseCode>\r\n        <disconnectReason></disconnectReason>\r\n        <voiceQOS>\r\n                <mediaIpSourceAddr>.</mediaIpSourceAddr> \r\n                <mediaIpDestAddr>.</mediaIpDestAddr>\r\n                <mediaUdpRtpSourcePort>-1</mediaUdpRtpSourcePort> \r\n                <mediaUdpRtpDestPort>-1</mediaUdpRtpDestPort>\r\n                <mediaNumOfIpPktRxed>-1</mediaNumOfIpPktRxed> \r\n                <mediaNumOfIpPktTxed>-1</mediaNumOfIpPktTxed>\r\n                <mediaNumOfIpErroredPktRxed>-1</mediaNumOfIpErroredPktRxed>\r\n                <mediaNumOfRtpPktRxed>-1</mediaNumOfRtpPktRxed>\r\n                <mediaNumOfRtpPktTxed>-1</mediaNumOfRtpPktTxed>\r\n                <mediaNumOfRtpPktLost>-1</mediaNumOfRtpPktLost>\r\n                <mediaNumOfRtpPktDiscarded>-1</mediaNumOfRtpPktDiscarded>\r\n                <mediaRtpJitter>-1</mediaRtpJitter>\r\n                <mediaRtpLatency>-1</mediaRtpLatency>\r\n                <mediaNumOfRtcpPktRxed>-1</mediaNumOfRtcpPktRxed>\r\n                <mediaNumOfRtcpPktTxed>-1</mediaNumOfRtcpPktTxed> \r\n                <mediaFarEndPacketLostPercentage>-1</mediaFarEndPacketLostPercentage>\r\n                <mediaFarEndCumulativePacketLost>-1</mediaFarEndCumulativePacketLost> \r\n                <mediaFarEndInterarrivalJitter>-1</mediaFarEndInterarrivalJitter> \r\n        </voiceQOS>\r\n    </endMedia>\r\n</LogEvent>"}, "url": {"raw": "http://{{ELMAdapter_URL}}/api/event", "protocol": "http", "host": ["{{ELMAdapter_URL}}"], "path": ["api", "event"]}}, "response": []}, {"name": "CDRtype1", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"response is 200.\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var now = new Date();\r", "pm.variables.set(\"timestamp\", now.toISOString());\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<LogEvent xmlns=\"http://solacom.com/Logging\">\r\n    <timestamp>{{timestamp}}</timestamp>\r\n    <agencyOrElement>{{PSAPName}}-A_A</agencyOrElement>\r\n    <agent>operatorAgent01</agent>\r\n    <callIdentifier>{{CallID}}</callIdentifier>\r\n    <incidentIdentifier>{{IncidentID}}</incidentIdentifier>\r\n    <eventType>CDRtype1</eventType>\r\n    <cdrType1>\r\n            <startTime>2023-02-06T12:31:14.226Z</startTime>\r\n            <operatorId>0</operatorId>\r\n            <ani>8195551002</ani>\r\n            <presentedTime>2023-02-06T12:31:14.541Z</presentedTime>\r\n            <answeredTime>2023-02-06T12:31:16.159Z</answeredTime>\r\n            <jobNumber>.</jobNumber>\r\n            <transferTime></transferTime>\r\n            <transferAnswerTime></transferAnswerTime>\r\n            <disassociatedTime></disassociatedTime>\r\n            <transferTargetType>.</transferTargetType>\r\n            <transferTargetName>.</transferTargetName>\r\n            <transferTarget>.</transferTarget>\r\n            <disconnectReason>.</disconnectReason>\r\n            <ivrOutcome>.</ivrOutcome>\r\n            <externalTransferAttempts>0</externalTransferAttempts>\r\n            <dnis>779</dnis>\r\n            <endTime>2023-02-06T12:42:17.555Z</endTime>\r\n    </cdrType1>\r\n</LogEvent>"}, "url": {"raw": "http://{{ELMAdapter_URL}}/api/event", "protocol": "http", "host": ["{{ELMAdapter_URL}}"], "path": ["api", "event"]}}, "response": []}], "variable": [{"key": "ELMAdapter_URL", "value": "localhost:55247/"}, {"key": "IterationCounter", "value": "0"}, {"key": "PSAPName", "value": "Ottawa"}, {"key": "CallID", "value": ""}, {"key": "IncidentID", "value": ""}], "protocolProfileBehavior": {"disableBodyPruning": true}}