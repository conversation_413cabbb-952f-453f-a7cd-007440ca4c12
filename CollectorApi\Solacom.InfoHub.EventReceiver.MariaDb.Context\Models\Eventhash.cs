﻿using System;

namespace Solacom.InfoHub.EventReceiver.MariaDb.Context.Models
{
    public partial class Eventhash
    {
        public int Id { get; set; }
        public string Content { get; set; }
        public string Hashedcontent { get; set; }
        public DateTime Eventreceived { get; set; }
        public string Clientcode { get; set; }
        public string Message { get; set; }
        public bool? Islastsavesuccessful { get; set; }
        public string Callid { get; set; }
        public string Medialabel { get; set; }
        public DateTime? Eventtimestamp { get; set; }
        public string EventType { get; set; }
    }
}
