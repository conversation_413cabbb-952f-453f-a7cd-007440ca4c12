﻿using Solacom.InfoHub.EventReceiver.Entities.Security;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    /// <summary>
    /// User Key Settings
    /// </summary>
    public class UserKeyData
    {
        private string _UserId = string.Empty;
        /// <summary>
        /// UserId
        /// </summary>
        public string UserId
        {
            get
            {
                return _UserId;
            }
            set
            {
                _UserId = Encryption.Decrypt(value);
            }
        }

        private string _RawApiKey = string.Empty;
        /// <summary>
        /// RawApiKey
        /// </summary>
        public string RawApiKey
        {
            get
            {
                return _RawApiKey;
            }
            set
            {
                _RawApiKey = Encryption.Decrypt(value);
            }
        }

        private string _HashedApiKey = string.Empty;
        /// <summary>
        /// RawApiKey
        /// </summary>
        public string HashedApiKey
        {
            get
            {
                return _HashedApiKey;
            }
            set
            {
                _HashedApiKey = Encryption.Decrypt(value);
            }
        }

        /// <summary>
        /// Unique friendly customer name / long code, as based on the settings mapping
        /// </summary>
        /// <remarks>also known as customer Name</remarks>
        public string ClientCode { get; set; }

        /// <summary>
        /// Unique short code for the client
        /// </summary>
        /// <remarks>introduced more for tracking usage / clarity</remarks>
        public string ClientShortCode { get; set; }

        /// <summary>
        /// Stores if the given client is EsiNet supported - default is false/null.
        /// </summary>
        public bool ClientEsiNETSupported { get; set; }

        /// <summary>
        /// Stores the available Tenants for a given Client.
        /// </summary>
        public System.Collections.Generic.Dictionary<string, string> TenantLookup { get; set; }

        /// <summary>
        /// Stores the clients timezone as configured
        /// </summary>
        public NodaTime.DateTimeZone ClientTimezone { get; set; }

    }

    public class ScheduledServicesFrequenciesInMinutes
    {
        /// <summary>
        /// How often the Expired Root events check is processed, in minutes.
        /// </summary>
        public int ExpiredEventService { get; set; }

        /// <summary>
        /// How often the DB clean up service runs, in minutes
        /// </summary>
        public int DBCleanupService { get; set; }

        /// <summary>
        /// How often the Process Queue is checked for available awaiting Calls for Processing
        /// </summary>
        public int ProcessQueueService { get; set; }

    }
}