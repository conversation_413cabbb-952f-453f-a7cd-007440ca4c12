﻿{
    "Logging": {
        "LogLevel": {
            "Default": "Debug",
            "System": "Warning",
            "Microsoft": "Warning"
        }
    },
    "Serilog": {
        "MinimumLevel": {
            "Default": "Debug",
            "Override": {
                "Microsoft": "Warning",
                "System": "Warning"
            }
        },
        "Properties": { "ApplicationName": "CollectorAPI" }
    },
    "elasticsearchSettings": {
        "url": "https://us-guardian-insights-production-2e3347.es.us-west-2.aws.found.io:9243",
        "userName": "elastic",
        "password": "zeZyJm9XuwXmgQQnO6H6JzBg",
        "listLimitOfQuery": 1000
    },
    "client": "thurston-wa",
    "tenantCode": "thwa",
    "sourceIndex_override": "callsummary_thurston-wa_historicaldata_20201212_20220124",
    "targetIndex_suffix": "_backfilldata_20201212_20220124",
    "filter": {
        "dateStart": "2020-11-10T00:00:00",
        "dateEnd": "2022-01-28T00:00:00"
    },
    "loop_interval": {
        "period": 1,
        "periodType": "week",
        "dateStart": "2020-11-10T00:00:00",
        "dateEnd": "2022-01-28T00:00:00"
    },
    "version": "1.0.0.0"
}
