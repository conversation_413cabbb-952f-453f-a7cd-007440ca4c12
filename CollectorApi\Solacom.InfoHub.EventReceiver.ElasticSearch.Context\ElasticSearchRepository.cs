﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nest;
using Solacom.InfoHub.EventReceiver.ElasticSearch.Entities;
using Solacom.InfoHub.EventReceiver.Entities;

namespace Solacom.InfoHub.EventReceiver.ElasticSearch.Context
{
    public class ElasticSearchRepository<T> : IGenericElasticSearchRepository<T> where T : class
    {
        private readonly IElasticClient _client;
        private readonly ElasticsearchSettings _settings;
        private readonly ILogger<ElasticSearchRepository<T>> _logger;
        public ElasticSearchRepository(IElasticClient client, IOptions<ElasticsearchSettings> settings, ILogger<ElasticSearchRepository<T>> logger)
        {
            _client = client;
            _logger = logger;
            _settings = settings.Value;
        }

        /// <summary>
        /// Main function to define the INDEX names based on the Type passed in.  
        /// </summary>
        /// <param name="t">Type of object</param>
        /// <param name="indexPostfix">The appended postfix - i.e. butler-oh</param>
        /// <returns></returns>
        private string GetIndexName(Type t, string indexPostfix)
        {
            if (t == typeof(EventData))
            {
                return $"{_settings.EventsIndex}_{indexPostfix}";
            }
            else if (t == typeof(CallSummary))
            {
                return $"{_settings.CallSummaryIndex}_{indexPostfix}";
            }
            else if (t == typeof(AgentAudit))
            {
                return $"{_settings.AgentAuditIndex}_{indexPostfix}";
            }
            else if (t == typeof(AliData))
            {
                return $"{_settings.aliEventIndex}_{indexPostfix}";
            }

            return null;
        }

        public async Task Insert(T document, string indexPrefix)
        {
            var start = DateTime.Now;
            var indexName = GetIndexName(typeof(T), indexPrefix);

            try
            {
                if (document is EventReceiver.Entities.EventLog)
                {
                    await _client.IndexAsync(document, ind => ind.Index(indexName));
                }
                else
                {
                    var response = await _client.IndexAsync(document, ind => ind.Index(indexName));
                    if (response.ServerError != null)
                    {
                        _logger.LogWarning($"ElasticSearchRepository : Insert Warning {response.ServerError}");
                    }
                }
            }

            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"ElasticSearchRepository : Insert in {indexName} took {duration.TotalMilliseconds} ms");
            }
        }

        public async Task InsertMany(List<T> documents, string indexPrefix)
        {
            var start = DateTime.Now;
            var indexName = GetIndexName(typeof(T), indexPrefix);
            try
            {
                if (documents is List<EventReceiver.Entities.EventLog>)
                {
                    
                    await _client.IndexManyAsync(documents, indexName);
                }
                else
                {
                    var response = await _client.IndexManyAsync(documents, indexName);
                    if (response.ServerError != null)
                    {
                        _logger.LogWarning($"ElasticSearchRepository : Insert Many Warning {response.ServerError}");
                    }
                }
            }

            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"ElasticSearchRepository : Insert Many in {indexName} took {duration.TotalMilliseconds} ms");
            }
        }

        public async Task DeleteByCallId(string callId, string indexPrefix)
        {
            var start = DateTime.Now;
            var indexName = GetIndexName(typeof(T), indexPrefix);

            try
            {
                var response = await _client.DeleteByQueryAsync<CallSummary>(c =>
                    c.Index(indexName).Query(q => q.MatchPhrase(bff => bff.Field(x => x.Callid).Query(callId))));   
               if (response.ServerError != null)
               {
                   _logger.LogWarning($"ElasticSearchRepository : DeleteByCallId Warning {response.ServerError}");
               }
            }

            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"ElasticSearchRepository : DeleteByCallId in {indexName} took {duration.TotalMilliseconds} ms");
            }
        }
        public async Task Delete(Guid id, string indexPrefix)
        {
            var start = DateTime.Now;
            var indexName = GetIndexName(typeof(T), indexPrefix);

            try
            {
               var response = await _client.DeleteAsync<T>(id, ind => ind.Index(indexName));
               if (response.ServerError != null)
               {
                   _logger.LogWarning($"ElasticSearchRepository : Delete Warning {response.ServerError}");
               }
            }

            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"ElasticSearchRepository : Delete in {indexName} took {duration.TotalMilliseconds} ms");
            }
        }

        /// <summary>
        /// Updates / Upsert (insert if not exists) a given document.
        /// </summary>
        /// <param name="document">Document to write</param>
        /// <param name="id">The unique GUID of the document</param>
        /// <param name="indexPrefix"></param>
        /// <param name="original_index"></param>
        /// <exception cref="Exception"></exception>
        public async Task Update(T document, Guid id, string indexPrefix, string original_index)
        {
            var start = DateTime.Now;

            //leverage the original index if provided, else, generate the index based on the prefix
            var indexName = string.Empty;
            if (!string.IsNullOrEmpty(original_index))
            {
                indexName = original_index;
            }
            else
            {
                indexName = GetIndexName(typeof(T), indexPrefix);
            }

            _logger.LogInformation($"Adding {id} to index {indexName}");

            try
            {
                var response = await _client.UpdateAsync<T>(id, r => r.Doc(document).Index(indexName).DocAsUpsert(true));
                if (response.ServerError != null)
                {
                    _logger.LogWarning($"ElasticSearchRepository : Update Warning {response.ServerError}");
                }
            }

            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"ElasticSearchRepository : Update in {indexName} took {duration.TotalMilliseconds} ms");
            }
        }
        /// <summary>
        /// Updates / Upsert (insert if not exists) a given document.
        /// </summary>
        /// <param name="document">Document to write</param>
        /// <param name="id">The unique identifier of the document</param>
        /// <param name="indexPrefix">The index prefix (i.e. Client)</param>
        /// <param name="original_index">Optional, the index of the document if available</param>
        /// <exception cref="Exception"></exception>
        public async Task Update(T document, string id, string indexPrefix, string original_index)
        {
            var start = DateTime.Now;

            //leverage the original index if provided, else, generate the index based on the prefix
            var indexName = string.Empty;
            if (!string.IsNullOrEmpty(original_index))
            {
                indexName = original_index;
            }
            else
            {
                indexName = GetIndexName(typeof(T), indexPrefix);
            }

            _logger.LogInformation($"Adding {id} to index {indexName}");

            try
            {
                var response = await _client.UpdateAsync<T>(id, r => r.Doc(document).Index(indexName).DocAsUpsert(true));
                if (response.ServerError != null)
                {
                    _logger.LogWarning($"ElasticSearchRepository : Update Warning {response.ServerError}");
                }
            }

            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"ElasticSearchRepository : Update in {indexName} took {duration.TotalMilliseconds} ms");
            }
        }

        /// <summary>
        /// !! ONLY USED FOR TESTING CURRENTLY - Please update this comment if leveraged in future
        /// </summary>
        public async Task<T> GetById(Guid id, string indexPrefix)
        {
            var start = DateTime.Now;
            var indexName = GetIndexName(typeof(T), indexPrefix);

            try
            {
                var response = await _client.GetAsync<T>(id, s => s.Index(indexName));
                if (response.Source == null)
                {
                    _logger.LogWarning($"ElasticSearchRepository GetById found null for id: {id}, index: {indexName}");
                }
                if (response.ServerError != null)
                {
                    _logger.LogWarning($"ElasticSearchRepository GetById Warning {response.ServerError}");
                }
                
                return response.Source;
            }

            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"ElasticSearchRepository : GetById in {indexName} took {duration.TotalMilliseconds} ms");
            }
        }

        /// <summary>
        /// Retrieves the Call Summary document based on the _id.  
        /// </summary>
        /// <param name="id">Unique identifier for the document</param>
        /// <param name="indexPrefix">The index prefix (i.e. Client)</param>
        /// <returns>Collection of Call Summaries</returns>
        /// <remarks>This data will include the Source index if avialable</remarks>
        /// <exception cref="exception"></exception>
        public async Task<List<CallSummary>> GetByCallId(string id, string indexPrefix)
        {
            var start = DateTime.Now;
            var indexName = GetIndexName(typeof(CallSummary), indexPrefix);

            try
            {
                var response = await _client.SearchAsync<CallSummary>(c =>
                    c.Index(indexName).RequestCache(false).Query(bf =>
                        bf.MatchPhrasePrefix(bff => bff.Field(x => x.Callid).Query(id))));

                //required check, original logic assumed there was always a Call summary record - this is no longer the case.
                if( response.HitsMetadata == null )
                {
                    return null;
                }

                //fetch the response metadata headers
                string[] source_index = response.HitsMetadata.Hits.Select(h => h.Index).ToArray<string>();

                List<CallSummary> callSummaryList = response.Documents.ToList();

                //iterate to add them to the loop up collection
                int idx = 0;
                foreach(CallSummary callsummary in callSummaryList)
                {
                    callsummary.ElasticIndex = source_index[idx];
                    idx++;
                }

                if (response.ServerError != null)
                {
                    _logger.LogWarning($"ElasticSearchRepository : GetByCallId Warning {response.ServerError}");
                }
                return callSummaryList;
            }

            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"ElasticSearchRepository : GetByCallId in {indexName} took {duration.TotalMilliseconds} ms");
            }
        }
    }
}