﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Solacom.InfoHub.EventReceiver.AppService.Interfaces;
using Solacom.InfoHub.EventReceiver.Entities;
using Solacom.InfoHub.EventReceiver.Web.API.Security;
using Solacom.InfoHub.EventReceiver.Web.Dtos;
using Solacom.InfoHub.EventReceiver.BusinessLogic.ALI;
using Microsoft.Extensions.Caching.Memory;
using Amazon.SecretsManager;
using Amazon.SecretsManager.Model;

namespace Solacom.InfoHub.EventReceiver.Web.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EventsController : ControllerBase
    {
        private readonly IEventsService _eventsService;
        private readonly ILogger<EventsController> _logger;
        private readonly IUserKeyManager _userKeyManager;
        private readonly UserKeyData _userOptions;
        private Dictionary<string, string> _clientCodeVsIndexprefix;
        /// <summary>
        /// Collection of Clients timezone configuration
        /// </summary>
        private Dictionary<string, NodaTime.DateTimeZone> _clientTimeZoneMapping;
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _cache;
        /// <summary>
        /// Stores the threshold where the XML parsing throws an exception if the error threshold is hit during main object parsing
        /// </summary>
        private readonly int _ErrorThreshold = 2;

        public EventsController(IEventsService eventsService, ILogger<EventsController> logger, IUserKeyManager userKeyManager, IOptions<UserKeyData> userOptions, IConfiguration configuration, IMemoryCache cache)
        {
            _eventsService = eventsService;
            _logger = logger;
            _userKeyManager = userKeyManager;
            _userOptions = userOptions.Value;
            _cache = cache;
            _clientCodeVsIndexprefix = new Dictionary<string, string>();
            var section = configuration.GetSection("clientSettings:clientcodeIndexPrefix");
            foreach (var kv in section.GetChildren())
            {
                _clientCodeVsIndexprefix.Add(kv.Key, kv.Value);
            }

            //retrieving the new Timzone configuration settings - if the setting isn't valid, the client information will not be available, and will not process any events from the given Client.
            _clientTimeZoneMapping = new Dictionary<string, NodaTime.DateTimeZone>();
            section = configuration.GetSection("clientSettings:clientTimezoneMapping");
            foreach (var kv in section.GetChildren())
            {
                if (NodaTime.DateTimeZoneProviders.Tzdb.Ids.Contains(kv.Value))
                {
                    _clientTimeZoneMapping.Add(kv.Key, NodaTime.DateTimeZoneProviders.Tzdb[kv.Value]);
                }
                else
                {
                    _logger.LogError($"EventsController Init: Client {kv.Key} timezone of {kv.Value} is not valid.  Please cross check https://nodatime.org/TimeZones for proper syntax.  Not adding it as a configured client.");
                }
            }
            _configuration = configuration;
        }

        private Dictionary<string, string> GetTenantClientMapping(string clientCode, UserKeyData userData)
        {
            Dictionary<string, string> rtnTenantLookup = new Dictionary<string, string>();

            var section = _configuration.GetSection("clientSettings:clientTenantMapping:" + clientCode);

            if (section == null)
            {
                return rtnTenantLookup;
            }

            foreach (var kv in section.GetChildren())
            {
                //ensuring lowercase for lookup
                rtnTenantLookup.Add(kv.Key.ToLower(), kv.Value.ToLower());
            }

            return rtnTenantLookup;
        }


        /// <summary>
        /// API configuration validation end point for Tenant Mappings
        /// </summary>
        /// <param name="clientcode">Client short code - required.</param>
        /// <returns>JSON output of the tenant Mappings, empty if not found.</returns>
        /// <remarks>Set PRIVATE to not be accessible during regular release of Insights - introduced for on premise debugging and may be valuable in future
        /// Can secure it with Auth requirement / UserData post or just simple hashed unique token as required in future.
        /// </remarks>
        [HttpGet]
        [Route("/api/tenant/mappings/{clientcode}")]
        public IActionResult DisplayTenantMapping(string clientcode, [FromQuery] UserKeyData userData)
        {
            //First check is access, do not proceed with any data actions of event logic until User is validated.
            if (userData == null)
            {
                return Forbid();
            }
            var userKeyData = new UserKeyData { UserId = userData.UserId, RawApiKey = userData.RawApiKey, HashedApiKey = _userOptions.HashedApiKey };
            var isValidUser = _userKeyManager.Match(userKeyData);
            if (!isValidUser)
            {
                return Forbid();
            }

            Dictionary<string, string> tenantMapping = GetTenantClientMapping(clientcode, null);

            return Ok($"{System.Text.Json.JsonSerializer.Serialize(tenantMapping)}");
        }


        /// <summary>
        /// Main Save entry event.  
        /// </summary>
        /// <param name="clientcode"></param>
        /// <param name="userData"></param>
        /// <returns>Does inline XML parsing to allow for detailed log capturing of failed EventLog payloads</returns>
        [HttpPost]
        [Route("/api/events/{clientcode}")]
        public async Task<IActionResult> SaveEventsGeneric(string clientcode, [FromQuery] UserKeyData userData)
        {
            //First check is access, do not proceed with any data actions of event logic until User is validated.
            if (userData == null)
            {
                return Forbid();
            }
            string customerName = _clientCodeVsIndexprefix[clientcode];
            var userKeyData = new UserKeyData { UserId = userData.UserId, RawApiKey = userData.RawApiKey, HashedApiKey = _userOptions.HashedApiKey, ClientCode = customerName, ClientShortCode = clientcode, TenantLookup = userData.TenantLookup };
            //format for the disabled list is the client short code - i.e. dcwi,thwa...
            string clientEsiNetSupportedList = _configuration.GetSection("clientSettings:clientEsiNetSupportedList").Value;
            if (!string.IsNullOrEmpty(clientEsiNetSupportedList))
            {
                userKeyData.ClientEsiNETSupported = clientEsiNetSupportedList.Contains(clientcode, StringComparison.OrdinalIgnoreCase);
            }

            var isValidUser = _userKeyManager.Match(userKeyData);
            if (!isValidUser)
            {
                return Forbid();
            }

            if (_clientCodeVsIndexprefix.ContainsKey(clientcode))
            {
                if (!_clientTimeZoneMapping.ContainsKey(clientcode))
                {
                    _logger.LogError($"EventController : SaveEventsGeneric - Client Code {clientcode} does not have a configured timezone (ref: clientTimezoneMapping settings)");
                    return StatusCode(417, "Client Timezone was not configured");
                }

                userKeyData.TenantLookup = GetTenantClientMapping(clientcode, userKeyData);
                userKeyData.ClientTimezone = _clientTimeZoneMapping[clientcode];

                string logString = string.Empty;
                try
                {
                    using (var reader = new System.IO.StreamReader(Request.Body, System.Text.Encoding.UTF8))
                    {
                        logString = await reader.ReadToEndAsync();
                    }

                    BusinessLogic.XmlHelper xmlHelper = new BusinessLogic.XmlHelper();

                    EventLog log = xmlHelper.DeserializeObjectXml<EventLog>(logString);
                    log.eventReceived = DateTime.Now;

                    //display any parsing Exceptions to the main log, if the exception is over the threshold, trigger a failed parsing occurence.
                    if (xmlHelper._ExceptionList.Count > 0)
                    {
                        bool isSupportedEventType = Enum.IsDefined(typeof(BusinessLogic.Enums.EventType), log.eventType);

                        //If the EventLog type is supported, log the error case and trigger the threshold check.  If not supported, there is no need to halt the processing.
                        if (isSupportedEventType)
                        {
                            _logger.LogError($"EventController : SaveEventsGeneric - EventType: {log.eventType} (supported:{isSupportedEventType}) Event Log has the following exceptions during parsing.\r\n{string.Join("\r\n", xmlHelper._ExceptionList)}");

                            if (xmlHelper._ExceptionList.Count > this._ErrorThreshold)
                            {
                                _logger.LogError($"EventController : SaveEventsGeneric - Parsing errors hit threshold of {this._ErrorThreshold}.  See previous log entry for specific exceptions.\r\n{logString}");
                                return StatusCode(HttpStatusCode.BadRequest.GetHashCode(), $"Failed to parse passed Event Log object.");
                            }
                        }
                        else
                        {
                            _logger.LogWarning($"EventController : SaveEventsGeneric - EventType: {log.eventType} (supported:{isSupportedEventType}) " +
                                                $"Event Log has the following exceptions during parsing.\r\n{string.Join("\r\n", xmlHelper._ExceptionList)}\r\n{logString}");
                        }
                    }

                    _logger.LogDebug($"Event received. CallID: {log.callIdentifier}, Event Type: {log.eventType}, Event Timestamp: {log.timestamp.ToString()}, Index Prefix: {customerName}");
                    var data = await _eventsService.SaveEvent(logString, log, userKeyData);
                    _logger.LogDebug($"Event Saved. CallID: {log.callIdentifier} with MasterRecordId: {data.MasterRecordId} Messages: {string.Join(", ", data.Messages)}");
                    return Ok(new SaveEventResponse { MasterRecordId = data.MasterRecordId, Meta = new MetaData { ErrorMessages = data.Messages } });
                }
                catch (Exception ex)
                {
                    _logger.LogError($"EventController : SaveEventsGeneric - Failed to parse Event Log object.  Exception: {ex.Message} \r\n  {logString}");
                    return StatusCode(HttpStatusCode.BadRequest.GetHashCode(), $"Failed to parse passed Event Log object.");
                }
            }
            else
            {
                _logger.LogError("EventsController : SaveEventsGeneric - Client Code {0} not found", clientcode);
                return StatusCode(417, "Client Code not found");
            }
        }

        /// <summary>
        /// Validates a Event Log entry to the expected data structures the Collector leverages.  
        /// Returns back success or failure with additional debug information.
        /// </summary>
        /// <param name="userData">Required to help avoid DNS or abuse to this end point.</param>
        /// <returns>Success or failure with detailed failure collection.</returns>
        [HttpPost]
        [Route("/api/event/validate")]
        public async Task<IActionResult> ValidateEvent([FromQuery] UserKeyData userData)
        {
            //First check is access, do not proceed with any data actions of event logic until User is validated.
            if (userData == null)
            {
                return Forbid();
            }
            var userKeyData = new UserKeyData { UserId = userData.UserId, RawApiKey = userData.RawApiKey, HashedApiKey = _userOptions.HashedApiKey };
            var isValidUser = _userKeyManager.Match(userKeyData);
            if (!isValidUser)
            {
                return Forbid();
            }

            string logString = string.Empty;
            try
            {
                using (var reader = new System.IO.StreamReader(Request.Body, System.Text.Encoding.UTF8))
                {
                    logString = await reader.ReadToEndAsync();
                }

                BusinessLogic.XmlHelper xmlHelper = new BusinessLogic.XmlHelper();

                EventLog log = xmlHelper.DeserializeObjectXml<EventLog>(logString);

                if (xmlHelper._ExceptionList.Count > 0)
                {
                    bool isSupportedEventType = Enum.IsDefined(typeof(BusinessLogic.Enums.EventType), log.eventType);

                    _logger.LogError($"EventController : ValidateEvent - EventType: {log.eventType} (supported:{isSupportedEventType}) Found the following Exceptions with the Event Log.\r\n{string.Join("\r\n", xmlHelper._ExceptionList)}\r\n{logString}");

                    return StatusCode(HttpStatusCode.BadRequest.GetHashCode(), $"Event Type of '{log.eventType}'{(isSupportedEventType ? " supported" : " not supported")}\r\n{string.Join("\r\n", xmlHelper._ExceptionList)}");
                }

                return Ok("EventLog was successfully parsed.");
            }
            catch (Exception ex)
            {
                _logger.LogError($"EventController : ValidateEvent - Failed to parse Event Log object.  Exception: {ex.Message} \r\n  {logString}");
                return StatusCode(HttpStatusCode.BadRequest.GetHashCode(), $"Failed to parse passed Event Log object.");
            }
        }

        /// <summary>
        /// Validates a Event of type ALI Response by parsing out the ALI data and returning it back in JSON form
        /// </summary>
        /// <param name="clientcode"></param>
        /// <param name="userData"></param>
        /// <returns>JSON object on success</returns>
        [HttpPost]
        [Route("/api/ali/validate/{clientcode}")]
        public async Task<IActionResult> ValidateALI(string clientcode, [FromQuery] UserKeyData userData)
        {
            //First check is access, do not proceed with any data actions of event logic until User is validated.
            if (userData == null)
            {
                return Forbid();
            }
            string index = _clientCodeVsIndexprefix[clientcode];
            var userKeyData = new UserKeyData { UserId = userData.UserId, RawApiKey = userData.RawApiKey, HashedApiKey = _userOptions.HashedApiKey };
            var isValidUser = _userKeyManager.Match(userKeyData);
            if (!isValidUser)
            {
                return Forbid();
            }

            string logString = string.Empty;
            try
            {
                using (var reader = new System.IO.StreamReader(Request.Body, System.Text.Encoding.UTF8))
                {
                    logString = await reader.ReadToEndAsync();
                }

                BusinessLogic.XmlHelper xmlHelper = new BusinessLogic.XmlHelper();

                EventLog log = xmlHelper.DeserializeObjectXml<EventLog>(logString);

                if (log.aliResponse == null || string.IsNullOrEmpty(log.aliResponse.Ali))
                {
                    return StatusCode(HttpStatusCode.BadRequest.GetHashCode(), $"Payload requires Event with a ALI Response.");
                }

                AliParser aliParser = new AliParser(log.aliResponse.Ali, index, _cache);

                return Ok(aliParser.GetAlidata());
            }
            catch (Exception ex)
            {
                _logger.LogError($"EventController : ValidateALI - Failed to parse Event Log object.  Exception: {ex.Message} \r\n  {logString}");
                return StatusCode(HttpStatusCode.BadRequest.GetHashCode(), $"Failed to parse passed Event Log object ALI Data.");
            }
        }

        [HttpPost]
        [Route("/api/event/cleanhash/{clientcode}")]
        public async Task<IActionResult> CleanUpHashedEvent(string clientcode, [FromQuery] UserKeyData userData)
        {
            //First check is access, do not proceed with any data actions of event logic until User is validated.
            if (userData == null)
            {
                return Forbid();
            }
            string index = _clientCodeVsIndexprefix[clientcode];
            var userKeyData = new UserKeyData { UserId = userData.UserId, RawApiKey = userData.RawApiKey, HashedApiKey = _userOptions.HashedApiKey, ClientCode = index };
            var isValidUser = _userKeyManager.Match(userKeyData);
            if (!isValidUser)
            {
                return Forbid();
            }

            if (!string.IsNullOrEmpty(index))
            {
                string logString = string.Empty;
                try
                {
                    using (var reader = new System.IO.StreamReader(Request.Body, System.Text.Encoding.UTF8))
                    {
                        logString = await reader.ReadToEndAsync();
                    }

                    await _eventsService.CleanUpEventHash(logString, index);

                    _logger.LogInformation($"EventController : CleanUpHashedEvent called for Client: {index}, EventLog: {logString}");

                    return Ok();
                }
                catch (Exception ex)
                {
                    _logger.LogError($"EventController : CleanUpHashedEvent - Failed to clean up event hash.  Exception: {ex.Message} \r\n  {logString}");
                    return StatusCode(HttpStatusCode.BadRequest.GetHashCode(), $"Failed to clean up event hash.");
                }
            }
            else
            {
                _logger.LogError("EventsController : CleanUpHashedEvent - Client Code {0} not found", clientcode);
                return StatusCode(417, "Client Code not found");
            }
        }


        /// <summary>
        /// Clears the ALI XML cache for a specific Client Id.
        /// </summary>
        /// <param name="clientcode">client to clear the ALI XML definition cached</param>
        /// <param name="userData">Required to help avoid DNS or abuse of this end point.</param>
        /// <returns>Success - there is no validation of the Cache existed, just executes the removal call</returns>
        /// <remarks>Current single entity specific, can expand to clear the full cache with additional Cache tracking code - however, adds more complexity for little gain on the core functional goals.</remarks>
        [HttpGet]
        [Route("/api/ali/cache/clear/{clientcode}")]
        public IActionResult ClearALICache(string clientcode, [FromQuery] UserKeyData userData)
        {
            //First check is access, do not proceed with any data actions of event logic until User is validated.
            if (userData == null)
            {
                return Forbid();
            }
            string index = _clientCodeVsIndexprefix[clientcode];
            var userKeyData = new UserKeyData { UserId = userData.UserId, RawApiKey = userData.RawApiKey, HashedApiKey = _userOptions.HashedApiKey };
            var isValidUser = _userKeyManager.Match(userKeyData);
            if (!isValidUser)
            {
                return Forbid();
            }

            //Key is also directly used in the BusinessLogic.ALIParser.cs
            string aliCacheKey = $"ALIXmlCache_{_clientCodeVsIndexprefix[clientcode]}";
            _cache.Remove(aliCacheKey);
            return Ok($"Cache removal call completed for {clientcode}");
        }

        /// <summary>
        /// Retrieves the ALI XML cache of the specific Client Id, if not found, returns "not found".  
        /// </summary>
        /// <param name="clientcode">client to clear the ALI XML definition cached</param>
        /// <param name="userData">Required to help avoid DNS or abuse of this end point.</param>
        /// <returns>flag XML string if found</returns>
        /// <remarks>this process never sets the cache, only retrieves it based on code settings.  Please call api/ali/validate/ if the goal is to validate writting to the cache and retrieval.</remarks>
        [HttpGet]
        [Route("/api/ali/cache/{clientcode}")]
        public IActionResult GetALICache(string clientcode, [FromQuery] UserKeyData userData)
        {
            //First check is access, do not proceed with any data actions of event logic until User is validated.
            if (userData == null)
            {
                return Forbid();
            }
            string index = _clientCodeVsIndexprefix[clientcode];
            var userKeyData = new UserKeyData { UserId = userData.UserId, RawApiKey = userData.RawApiKey, HashedApiKey = _userOptions.HashedApiKey };
            var isValidUser = _userKeyManager.Match(userKeyData);
            if (!isValidUser)
            {
                return Forbid();
            }

            //Key is also directly used in the BusinessLogic.ALIParser.cs
            string aliCacheKey = $"ALIXmlCache_{_clientCodeVsIndexprefix[clientcode]}";
            ALISchema cachedAliSchema;
            _cache.TryGetValue(aliCacheKey, out cachedAliSchema);
            
            if( cachedAliSchema != null )
            {
                return Ok(cachedAliSchema);
            }
            return Ok("--");
        }

        /// <summary>
        /// Calls the core configuration and forces a reload of all configuration settings.  This includes local and secret settings as appropriate.
        /// </summary>
        /// <returns>Ok status with message</returns>
        [HttpGet]
        [Route("/config/forcerefresh")]
        public IActionResult ForceRefreshConfiguration([FromQuery] UserKeyData userData)
        {
            //First check is access, do not proceed with any data actions of event logic until User is validated.
            if (userData == null)
            {
                return Forbid();
            }
            var userKeyData = new UserKeyData { UserId = userData.UserId, RawApiKey = userData.RawApiKey, HashedApiKey = _userOptions.HashedApiKey };
            var isValidUser = _userKeyManager.Match(userKeyData);
            if (!isValidUser)
            {
                return Forbid();
            }

            IConfigurationRoot rootconfig = _configuration as IConfigurationRoot;
            if (rootconfig != null)
            {
                rootconfig.Reload();

                return Ok("configuration refreshed.");
            }

            return Ok($"No configuration found.");
        }

        /// <summary>
        /// attempts to retrieve the secrete manager configuraiton file if configured, providing detailed error messages if unsuccessful
        /// </summary>
        /// <returns>Ok status, 500 if failure</returns>
        /// <remarks>Details of the failure (or success) are pushed to the logs</remarks>
        [HttpGet]
        [Route("/config/validate")]
        public IActionResult ValidateConfiguration([FromQuery] UserKeyData userData)
        {
            //First check is access, do not proceed with any data actions of event logic until User is validated.
            if (userData == null)
            {
                return Forbid();
            }
            var userKeyData = new UserKeyData { UserId = userData.UserId, RawApiKey = userData.RawApiKey, HashedApiKey = _userOptions.HashedApiKey };
            var isValidUser = _userKeyManager.Match(userKeyData);
            if (!isValidUser)
            {
                return Forbid();
            }

            try
            {
                //first, attempt to retrieve the ALI from a secret 
                var region = Environment.GetEnvironmentVariable("aws_region");
                var awsEnv = Environment.GetEnvironmentVariable("aws_environment");
                var awsCountry = Environment.GetEnvironmentVariable("aws_country");

                if( string.IsNullOrEmpty(region))
                {
                    _logger.LogError($"API.ValidateConfiguration: region environment variable failure.");
                    return StatusCode(500, $"Could not load configuration region environment variable failure.");
                }

                var secretPath = $"{awsEnv}/{awsCountry}/collectorapi/configuration";
                IAmazonSecretsManager client = new AmazonSecretsManagerClient(Amazon.RegionEndpoint.GetBySystemName(region));
                GetSecretValueRequest request = new GetSecretValueRequest
                {
                    SecretId = secretPath,
                    VersionStage = "AWSCURRENT", // VersionStage defaults to AWSCURRENT if unspecified.
                };

                GetSecretValueResponse response;

                try
                {
                    response = client.GetSecretValueAsync(request).Result;

                    _logger.LogWarning($"API.ValidateConfiguration triggered.  Configuration found {secretPath} Version: {response.VersionId}");
                    _logger.LogInformation($"{response.SecretString}");
                    return Ok($"Configuration located successfully.");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"API.ValidateConfiguration: Could not retrieve Secret {secretPath}.");
                    return StatusCode(500, $"Could not load configuration {ex.Message}");
                }
            }
            catch(Exception ex)
            {
                _logger.LogError(ex, $"API.ValidateConfiguration: General failure.");
                return StatusCode(500, $"Could not load configuration {ex.Message}");
            }
            
        }


    }
}