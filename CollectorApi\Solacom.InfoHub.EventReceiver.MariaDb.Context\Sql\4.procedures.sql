﻿/** 
	Created: Aug 2021
	Updated: Nov 2023 - updated to new DB / cleaned up and adding in SPRI procedures
	Author: <PERSON>
	Drop / Create logic for all stored procudures
**/

DELIMITER $$

DROP PROCEDURE IF EXISTS CollectorAPI.GetMasterEvent;
$$

DROP PROCEDURE IF EXISTS CollectorAPI.GetRootEvent;
$$


DROP PROCEDURE IF EXISTS CollectorAPI.GetHashedEvent;
$$
/**
	Retrieves a Root Event record
	@p_hashed_event - hashed event data
	@p_client_id - client identifier
**/
CREATE PROCEDURE `CollectorAPI`.`GetHashedEvent`(
	in p_hashed_key varchar(1000),
	in p_client_id varchar(100)
)
begin
	/**version: 1.0**/	
	select 	id, 
			hashed_data			
	from hashedevents
	where (hashed_key = p_hashed_key)
	and (client_id = p_client_id);
	
END$$

DROP PROCEDURE IF EXISTS CollectorAPI.GetAgentSession;
$$
/**
	Retrieves a Root Event record
	@p_hashed_event - hashed event data
	@p_client_id - client identifier
**/
CREATE PROCEDURE CollectorAPI.`GetAgentSession`(
	in p_medialabel varchar(512),
	in p_client_id varchar(100)
)
begin
	/**version: 1.0**/	
	select 	id, 
			agent_data			
	from agentsession
	where (medialabel = p_medialabel)
	and (client_id = p_client_id);
	
END$$

DROP PROCEDURE IF EXISTS CollectorAPI.GetEvents;
$$

DROP PROCEDURE IF EXISTS CollectorAPI.DeleteMasterEvent;
$$

DROP PROCEDURE IF EXISTS CollectorAPI.DeleteRootEvent;
$$

DROP PROCEDURE IF EXISTS CollectorAPI.DeleteEvents;
$$

DROP PROCEDURE IF EXISTS CollectorAPI.DeleteHashedEvent;
$$
/**
	Deletes a hashed event Record
	@p_hashed_event - hashed event data
	@p_client_id - client identifier
**/
CREATE PROCEDURE `CollectorAPI`.`DeleteHashedEvent`(
	in p_hashed_key varchar(1000),
	in p_client_id varchar(100)
)
begin
	/**version: 1.0**/		
	DELETE FROM hashedevents
	where (client_id = p_client_id)
	and (hashed_key = p_hashed_key);
	
END$$


DROP PROCEDURE IF EXISTS CollectorAPI.SetRootEvent;
$$

DROP PROCEDURE IF EXISTS CollectorAPI.SetMasterEvent;
$$ 

DROP PROCEDURE IF EXISTS CollectorAPI.AddEvent;
$$
/**
	Add events
**/
CREATE PROCEDURE CollectorAPI.`AddEvent`(
	in p_call_identifier varchar(256),
	in p_client_id varchar(100),
	in p_eventType varchar(128),
	in p_event_data json,
	in p_state_id int
)
begin
	/**version: 1.0**/		
	INSERT INTO 
	events (Client_Id, Call_Identifier, EventType, Event_Data, Date_Created, State_Id) 
		VALUES(p_client_id, p_call_identifier, p_eventType, p_event_data, now(), p_state_id);
	
	SELECT LAST_INSERT_ID() AS eventId;
END$$

DROP PROCEDURE IF EXISTS CollectorAPI.AddHashEvent;
$$
/**
	Add Hash events
**/
CREATE PROCEDURE `CollectorAPI`.`AddHashEvent`(
	in p_client_id varchar(100),
	in p_hashed_key varchar(1000),
	in p_hashed_data json
)
begin
	/**version: 1.0**/		
	INSERT IGNORE INTO 
	hashedevents (Client_Id, Hashed_Key, Hashed_Data, Date_Created) 
		VALUES(p_client_id, p_hashed_key, p_hashed_data, now());
END$$

DROP PROCEDURE IF EXISTS CollectorAPI.AddAgentSession;
$$
/**
	Add Agent Session
**/
CREATE PROCEDURE `CollectorAPI`.`AddAgentSession`(
	in p_client_id varchar(100),
	in p_medialabel varchar(512),
	in p_agent_data json
)
begin
	/**version: 1.0**/		
	INSERT INTO 
	agentsession (Client_Id, MediaLabel, Agent_Data, Date_Created, Date_Updated, State_Id) 
		VALUES(p_client_id, p_medialabel, p_agent_data, now(), now(), 1);
END$$

DROP PROCEDURE IF EXISTS CollectorAPI.AddProcessError;
$$
/**
	Add events
**/
CREATE PROCEDURE CollectorAPI.`AddProcessError`(
	in p_call_identifier varchar(256),
	in p_client_id varchar(100),
	in p_error_message varchar(100),
	in p_state_id int
)
begin
	/**version: 1.0**/		
	INSERT INTO 
	processerror (Client_Id, Call_Identifier, Error_Message, Date_Created, State_Id) 
		VALUES(p_client_id, p_call_identifier, p_error_message, now(), p_state_id);
END$$

DROP PROCEDURE IF EXISTS CollectorAPI.GetProcessErrorList;
$$
/**
	Get All Process Errors
**/
CREATE PROCEDURE CollectorAPI.GetProcessErrorList()
begin
	/**version: 1.0**/	
	SELECT Error_Message FROM processerror;
END$$

DROP PROCEDURE IF EXISTS CollectorAPI.DeleteProcessError;
$$
/**
	Delete's a specific Process Errors
**/
CREATE PROCEDURE CollectorAPI.DeleteProcessError(
	in p_error_message varchar(100)
		/**version: 1.0**/	
)
begin
	DELETE FROM processerror WHERE Error_Message = p_error_message;
END$$

DROP PROCEDURE IF EXISTS CollectorAPI.GetExpiredRootEvents;
$$


DROP PROCEDURE IF EXISTS CollectorAPI.SetEventErrorState;
$$

DROP PROCEDURE IF EXISTS CollectorAPI.CleanUpTables_HashedEvents;
$$

/**
	Deletes any data based on the age of the data/parameters, excluding Error State records  (StateId = 4)
**/
CREATE PROCEDURE CollectorAPI.CleanUpTables_HashedEvents(
	in p_olderthan_hours int
)
begin
	
	delete from hashedevents where 
		Date_Created < (DATE_SUB(NOW(), INTERVAL p_olderthan_hours HOUR));
		
END$$

DROP PROCEDURE IF EXISTS CollectorAPI.CleanUpTables_Events;
$$

/**
	Deletes any data based on the age of the data/parameters, excluding Error State records  (StateId = 4)
**/
CREATE PROCEDURE CollectorAPI.CleanUpTables_Events(
	in p_olderthan_hours int
)
begin
	
	delete from events where state_id < 4 AND  
		Date_Created < (DATE_SUB(NOW(), INTERVAL p_olderthan_hours HOUR));
		
END$$

DROP PROCEDURE IF EXISTS CollectorAPI.CleanUpTables_RootEvent;
$$

DROP PROCEDURE IF EXISTS CollectorAPI.CleanUpTables_MasterEvents;
$$

DROP PROCEDURE IF EXISTS CollectorAPI.CleanUpTables_AgentSession;
$$
/**
	Deletes any data based on the age of the data/parameters, excluding Error State records  (StateId = 4)
**/
CREATE PROCEDURE CollectorAPI.CleanUpTables_AgentSession(
	in p_olderthan_hours int
)
begin
	
	delete from agentsession where state_id != 4 AND 
		Date_Created < (DATE_SUB(NOW(), INTERVAL p_olderthan_hours HOUR));
		
END$$

DROP PROCEDURE IF EXISTS InsightsData.SetCallSummary
$$

/**
	Stores the final Call Summary recorrds
**/
CREATE PROCEDURE InsightsData.SetCallSummary(
  in p_customerName nvarchar(100),
  in p_tenantPsapName nvarchar(100),
  in p_isAbandonedState smallint,
  in p_address nvarchar(256),
  in p_isAdminCall smallint,
  in p_isAdminEmergencyCall smallint,
  in p_agentAnsweredWithin10s smallint,
  in p_agentAnsweredWithin15s smallint,
  in p_agentAnsweredWithin20s smallint,
  in p_agentAnsweredWithin40s smallint,
  in p_agentAnsweredMoreThan10s smallint,
  in p_agentAnsweredMoreThan20s smallint,
  in p_agentAnsweredMoreThan40s smallint,
  in p_agentCallbacknumber nvarchar(50),
  in p_agentName nvarchar(100),
  in p_agentTimeToAnswerInSeconds double,
  in p_answeredBySystem smallint,
  in p_systemAnsweredWithin10s smallint,
  in p_systemAnsweredWithin15s smallint,
  in p_systemAnsweredWithin20s smallint,
  in p_systemAnsweredWithin40s smallint,
  in p_systemAnsweredMoreThan10s smallint,
  in p_systemAnsweredMoreThan20s smallint,
  in p_systemAnsweredMoreThan40s smallint,
  in p_nonEmergencyAnsweredWithin10s smallint,
  in p_nonEmergencyAnsweredWithin15s smallint,
  in p_nonEmergencyAnsweredWithin20s smallint,
  in p_nonEmergencyAnsweredWithin40s smallint,
  in p_nonEmergencyAnsweredMoreThan10s smallint,
  in p_nonEmergencyAnsweredMoreThan20s smallint,
  in p_nonEmergencyAnsweredMoreThan40s smallint,
  in p_callAnswered datetime(4),
  in p_callAnsweredToLocal datetime(4),
  in p_callArrivedSystem datetime(4),
  in p_callArrivedSystemToLocal datetime(4),
  in p_callDetailsIndex int,
  in p_callPresented datetime(4),
  in p_callPresentedToLocal datetime(4),  
  in p_callReleased datetime(4),
  in p_callReleasedToLocal datetime(4),
  in p_callTransferred datetime(4),
  in p_callTransferredToLocal datetime(4),
  in p_callBackNumber nvarchar(50),
  in p_callIdentifier nvarchar(100),
  in p_callMobilityType nvarchar(50),
  in p_callState nvarchar(50),
  in p_callType nvarchar(50),
  in p_carrier nvarchar(50),
  in p_isCompleted smallint,
  in p_confidence double,
  in p_isEmergencyCall smallint,
  in p_endCallTime datetime(4),
  in p_endCallTimeToLocal datetime(4),  
  in p_esn double,
  in p_finalCos nvarchar(50),
  in p_holdTimeInSeconds double,
  in p_isInProgress smallint,
  in p_incidentIdentifier nvarchar(100),
  in p_isAbandoned smallint,
  in p_isAbandonedCallback smallint,
  in p_isAdmin smallint,
  in p_isAdminEmergency smallint,
  in p_isAlternativeRoute smallint,
  in p_isCallback smallint,
  in p_isEmergency smallint,
  in p_isInternalTransferCall smallint,
  in p_isOutbound smallint,
  in p_isTandem smallint,
  in p_isTransferred smallint,
  in p_isUnknownType smallint,
  in p_isLandlineType smallint,
  in p_latitude double,
  in p_longitude double,
  in p_mediaLabel nvarchar(100),
  in p_nonEmergencyHoldTimeInSeconds double,
  in p_nonEmergencyPsapTimeToAnswerInSeconds double,
  in p_isNotFoundType smallint,
  in p_originalCos nvarchar(50),
  in p_processedTime datetime(4),
  in p_psapName nvarchar(100),
  in p_psapTimeToAnswerInSeconds double,
  in p_isRTTType smallint,
  in p_isSMSType smallint,
  in p_startCallTime datetime(4),
  in p_startCallTimeToLocal datetime(4),  
  in p_isTDDChallenge smallint,
  in p_isTDDType smallint,
  in p_talkTimeInSeconds double,
  in p_nonEmergencyTalkTimeInSeconds double,
  in p_isTandemCall smallint,
  in p_timeStamp datetime(4),
  in p_timeStampToLocal datetime(4),
  in p_timeToAnswerInSeconds double,
  in p_nonEmergencyTimeToAnswerInSeconds double,
  in p_timeToTransferInSeconds double,
  in p_totalCallTimeInSeconds double,
  in p_nonEmergencyTotalCallTimeInSeconds double,
  in p_transferFrom nvarchar(100),
  in p_transferTo nvarchar(100),
  in p_uncertainty double,
  in p_isUnknownCall smallint,
  in p_isVoipType smallint,
  in p_isWirelessType smallint,
  IN p_locationDataList json,
  in p_zipcode nvarchar(50)
)
BEGIN
	/**version: 1.0**/		
	INSERT INTO 
	callsummary (customerName, tenantPsapName, isAbandonedState, address, isAdminCall, isAdminEmergencyCall, agentAnsweredWithin10s, agentAnsweredWithin15s, agentAnsweredWithin20s, agentAnsweredWithin40s, agentAnsweredMoreThan10s, agentAnsweredMoreThan20s, agentAnsweredMoreThan40s, agentCallbacknumber, agentName, agentTimeToAnswerInSeconds, answeredBySystem, systemAnsweredWithin10s, systemAnsweredWithin15s, systemAnsweredWithin20s, systemAnsweredWithin40s, systemAnsweredMoreThan10s, systemAnsweredMoreThan20s, systemAnsweredMoreThan40s, 
	nonEmergencyAnsweredWithin10s, nonEmergencyAnsweredWithin15s, nonEmergencyAnsweredWithin20s, nonEmergencyAnsweredWithin40s, nonEmergencyAnsweredMoreThan10s, nonEmergencyAnsweredMoreThan20s, nonEmergencyAnsweredMoreThan40s, callAnswered, callAnsweredToLocal, callArrivedSystem, callArrivedSystemToLocal, callDetailsIndex, 
	callPresented, callPresentedToLocal, callReleased, callReleasedToLocal, callTransferred, callTransferredToLocal, 
	callBackNumber, callIdentifier, callMobilityType, callState, callType, carrier, isCompleted, confidence, isEmergencyCall, endCallTime, endCallTimeToLocal, esn, finalCos, holdTimeInSeconds, 
	isInProgress, incidentIdentifier, isAbandoned, isAbandonedCallback, isAdmin, isAdminEmergency, isAlternativeRoute, isCallback, isEmergency, isInternalTransferCall, isOutbound, isTandem, isTransferred, isLandlineType, latitude, longitude, mediaLabel, nonEmergencyHoldTimeInSeconds, nonEmergencyPsapTimeToAnswerInSeconds, 
	isNotFoundType, originalCos, processedTime, psapName, psapTimeToAnswerInSeconds, isRTTType, isSMSType, startCallTime, startCallTimeToLocal, isTDDChallenge, isTDDType, talkTimeInSeconds, nonEmergencyTalkTimeInSeconds, isTandemCall, timeStamp, timeStampToLocal, timeToAnswerInSeconds, nonEmergencyTimeToAnswerInSeconds, timeToTransferInSeconds, 
	totalCallTimeInSeconds, nonEmergencyTotalCallTimeInSeconds, transferFrom, transferTo, uncertainty, isUnknownType, isUnknownCall, isVoipType, isWirelessType, zipcode, locationDataList, dateCreated)
		VALUES(p_customerName, p_tenantPsapName, p_isAbandonedState, p_address, p_isAdminCall, p_isAdminEmergencyCall, p_agentAnsweredWithin10s, p_agentAnsweredWithin15s, p_agentAnsweredWithin20s, p_agentAnsweredWithin40s, p_agentAnsweredMoreThan10s, p_agentAnsweredMoreThan20s, p_agentAnsweredMoreThan40s, p_agentCallbacknumber, p_agentName, p_agentTimeToAnswerInSeconds, p_answeredBySystem, p_systemAnsweredWithin10s, p_systemAnsweredWithin15s, p_systemAnsweredWithin20s, p_systemAnsweredWithin40s, p_systemAnsweredMoreThan10s, p_systemAnsweredMoreThan20s, p_systemAnsweredMoreThan40s, p_nonEmergencyAnsweredWithin10s, p_nonEmergencyAnsweredWithin15s, p_nonEmergencyAnsweredWithin20s, p_nonEmergencyAnsweredWithin40s, p_nonEmergencyAnsweredMoreThan10s, p_nonEmergencyAnsweredMoreThan20s, p_nonEmergencyAnsweredMoreThan40s, p_callAnswered, p_callAnsweredToLocal, p_callArrivedSystem,  p_callArrivedSystemToLocal, p_callDetailsIndex, 
		p_callPresented, p_callPresentedToLocal, p_callReleased, p_callReleasedToLocal, p_callTransferred, p_callTransferredToLocal, 
		p_callBackNumber,	p_callIdentifier, p_callMobilityType, p_callState, p_callType, p_carrier, p_isCompleted, p_confidence, p_isEmergencyCall, p_endCallTime, p_endCallTimeToLocal, p_esn, p_finalCos, p_holdTimeInSeconds,  
	p_isInProgress, p_incidentIdentifier, p_isAbandoned, p_isAbandonedCallback, p_isAdmin, p_isAdminEmergency, p_isAlternativeRoute, p_isCallback, p_isEmergency, p_isInternalTransferCall, p_isOutbound, p_isTandem, p_isTransferred, p_isLandlineType, p_latitude, p_longitude, p_mediaLabel,  p_nonEmergencyHoldTimeInSeconds, p_nonEmergencyPsapTimeToAnswerInSeconds, 
	p_isNotFoundType, p_originalCos, p_processedTime, p_psapName, p_psapTimeToAnswerInSeconds, p_isRTTType, p_isSMSType, p_startCallTime, p_startCallTimeToLocal, p_isTDDChallenge, p_isTDDType, 
	p_talkTimeInSeconds, p_nonEmergencyTalkTimeInSeconds, p_isTandemCall, p_timeStamp, p_timeStampToLocal, p_timeToAnswerInSeconds, p_nonEmergencyTimeToAnswerInSeconds, p_timeToTransferInSeconds, p_totalCallTimeInSeconds, p_nonEmergencyTotalCallTimeInSeconds, p_transferFrom, p_transferTo, p_uncertainty, 
	p_isUnknownType, p_isUnknownCall, p_isVoipType, p_isWirelessType, p_zipcode, p_locationDataList, now());
END$$


DROP PROCEDURE IF EXISTS InsightsData.SetCallEvent;
$$
/**
	Add events
**/
CREATE PROCEDURE InsightsData.`SetCallEvent`(
	in p_customerName varchar(100),
	in p_tenantPsapName varchar(100),
	in p_callIdentifier varchar(256),
	in p_eventType varchar(50), 
	in p_eventDatetime datetime(4), 
	in p_eventDatetimeToLocal datetime(4),	
	in p_eventReceived datetime(4),
	in p_eventData json
)
BEGIN
	/**version: 1.0**/		
	INSERT INTO 
	callevent (customerName, tenantPsapName, callIdentifier, eventType, eventDatetime, eventDatetimeToLocal, eventData, eventReceived, dateCreated) 
		VALUES(p_customerName, p_tenantPsapName, p_callIdentifier, p_eventType, p_eventDatetime, p_eventDatetimeToLocal, p_eventData, p_eventReceived, now());
END$$

DROP PROCEDURE IF EXISTS InsightsData.CallSummaryExists;
$$
/**

**/
CREATE PROCEDURE InsightsData.`CallSummaryExists`(
	in p_customerName varchar(100),
	in p_callIdentifier varchar(100)
)
BEGIN
	/**version: 1.0**/		
	SELECT dateCreated, COUNT(id) as CallSummaryCount FROM callsummary 
	WHERE customerName = p_customerName
	AND callIdentifier = p_callIdentifier
	GROUP BY callIdentifier;
END$$


DROP PROCEDURE IF EXISTS InsightsData.DeleteCallData;
$$

CREATE PROCEDURE InsightsData.`DeleteCallData`(
	in p_customerName varchar(100),
	in p_callIdentifier varchar(100)
)
BEGIN
	
  START TRANSACTION;	
		/**version: 1.0**/		
		DELETE FROM callsummary 
			WHERE 	customerName = p_customerName AND
					callIdentifier = p_callIdentifier;

		SET @CallSummaryCount = (SELECT ROW_COUNT());
		
		DELETE FROM callevent 
			WHERE 	customerName = p_customerName AND
					callIdentifier = p_callIdentifier;
			
		SET @CallEventCount = (SELECT ROW_COUNT());

		select @CallSummaryCount AS CallSummaryCount, @CallEventCount AS CallEventCount;
	COMMIT;

END$$

DROP PROCEDURE IF EXISTS CollectorAPI.SetProcessQueue;
$$

CREATE PROCEDURE CollectorAPI.SetProcessQueue(
	in p_callIdentifier varchar(256),
	IN p_clientId varchar(100)
)
BEGIN
	
	/**version: 1.0**/		
	INSERT INTO 
	processqueue (clientId, callIdentifier, dateCreated, dateUpdated) 
		VALUES(p_clientId, p_callIdentifier, now(), now()) ON DUPLICATE KEY UPDATE    
				dateUpdated = now();	
END$$

DROP PROCEDURE IF EXISTS CollectorAPI.GetProcessQueueOlderThan;
$$

CREATE PROCEDURE CollectorAPI.GetProcessQueueOlderThan(
	in p_olderthan_minutes int
)
BEGIN
	
	/**version: 1.0**/		
	SELECT callIdentifier, clientId, dateCreated, dateUpdated
	FROM processqueue WHERE dateUpdated <= (DATE_SUB(NOW(), INTERVAL p_olderthan_minutes MINUTE));
	
END$$

DROP PROCEDURE IF EXISTS CollectorAPI.DeleteProcessQueue;
$$

CREATE PROCEDURE CollectorAPI.DeleteProcessQueue(
	IN p_callIdentifier varchar(256),
	IN p_clientId varchar(100)
)
BEGIN
	
	/**version: 1.0**/		
	DELETE FROM processqueue 
		WHERE callIdentifier = p_callIdentifier and clientId = p_clientId;
	
END$$

DROP PROCEDURE IF EXISTS CollectorAPI.GetEventTypeCount;
$$

CREATE PROCEDURE CollectorAPI.GetEventTypeCount(
	in p_callIdentifier varchar(256),
	IN p_clientId varchar(100),
	IN p_maxEventId int
)
BEGIN
	
	/**version: 1.0**/		
	SELECT 	EventType, 
			COUNT(id) as EventCount, 
			MAX(State_Id) as MaxStateId, 
			MAX(id) as MaxEventId
	FROM events 
	WHERE Call_Identifier = p_callIdentifier
	AND Client_Id = p_clientId 
	AND (p_maxEventId = -1 OR id <= p_maxEventId)
	GROUP BY EventType;
	
END$$

DROP PROCEDURE IF EXISTS CollectorAPI.GetEventsForProcessing;
$$

CREATE PROCEDURE CollectorAPI.GetEventsForProcessing(
	in p_callIdentifier varchar(256),
	IN p_clientId varchar(100),
	in p_maxEventId int
)
BEGIN
	/**version: 1.0**/		
	START TRANSACTION;
		
		SELECT 	Event_Data,
				State_id
		FROM events 
		WHERE Call_Identifier = p_callIdentifier
			AND Client_Id = p_clientId 
			AND (p_maxEventId = -1 OR id <= p_maxEventId);
		
		UPDATE events 
			set State_Id = 2
		WHERE Call_Identifier = p_callIdentifier
			AND Client_Id = p_clientId 
			AND (p_maxEventId = -1 OR id <= p_maxEventId);

	COMMIT;
	
END$$

DROP PROCEDURE IF EXISTS CollectorAPI.SetEventState;
$$

/**
	Sets the Event table state field
**/
CREATE PROCEDURE CollectorAPI.`SetEventState`(
	in p_callIdentifier varchar(256),
	in p_clientId varchar(100),
	IN p_maxEventId int,
	IN p_stateId int
)
begin

	/**version: 1.0**/
	UPDATE events 
			set State_Id = p_stateId
		WHERE Call_Identifier = p_callIdentifier
			AND Client_Id = p_clientId 
			AND (p_maxEventId = -1 OR id <= p_maxEventId);
	
	SELECT ROW_COUNT() AS EventsUpdatedCount;
	
END$$

DROP PROCEDURE IF EXISTS CollectorAPI.GetExpiredEventsCallId;
$$

/**
 	Retrieves Call Identifiers associated to any Events that are in NEW state, older than the number of hours passed.
 */
CREATE PROCEDURE CollectorAPI.GetExpiredEventsCallId(
	IN p_olderthan_hours INT
)
BEGIN
	/**version: 1.0**/		
	SELECT 	Call_Identifier,
			Client_Id, 
			MaxDateCreated
	FROM 
	(
		SELECT 	Call_Identifier,
			 	Client_Id,
			   	MAX(Date_Created) as MaxDateCreated
			FROM events 
			WHERE State_Id = 1
			GROUP BY Call_Identifier 
	) AS callByTime 
	WHERE callByTime.MaxDateCreated < (DATE_SUB(NOW(), INTERVAL p_olderthan_hours hour));	
	
END$$
