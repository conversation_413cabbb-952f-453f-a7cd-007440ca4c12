﻿namespace Solacom.InfoHub.EventReceiver.ElasticSearch.Entities
{
    public class StartCall : EventLog
    {
        public string Header { get; set; }
        public string Location { get; set; }
        public string MediaLabel { get; set; }
        public string IncomingCallPolicy { get; set; }
        public string CallType { get; set; }
        public string SignallingType { get; set; }
        public string Circuit { get; set; }
        public string CircuitId { get; set; }
        public string TrunkGroupId { get; set; }
        public string Ani { get; set; }
        public string AniDomain { get; set; }
        public string Dnis { get; set; }
        public string DnisDomain { get; set; }
        public string Pani { get; set; }
        public string Esrn { get; set; }
        public string CallerName { get; set; }
    }
}