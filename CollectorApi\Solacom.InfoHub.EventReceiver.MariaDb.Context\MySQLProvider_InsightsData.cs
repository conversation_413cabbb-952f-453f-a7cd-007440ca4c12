﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;
using Solacom.InfoHub.EventReceiver.Entities;
using Solacom.InfoHub.EventReceiver.MariaDb.Context.Interfaces;
using Solacom.InfoHub.EventReceiver.MariaDb.Context.Models;

namespace Solacom.InfoHub.EventReceiver.MariaDb.Context
{
    public class MySQLProvider_InsightsData : IDataProvider_InsightsData
    {
        //public string ClientId { get; set; }
        public string ConnectionString { get; set; }

        public MySQLProvider_InsightsData(string connectionString)
        {
            ConnectionString = connectionString;
        }


        /// <summary>
        /// Sets the Call Summary record 
        /// </summary>
        /// <param name="customerName">Customer Name (aka. ClientId)</param>
        /// <param name="tenantPsapName">Psap name</param>
        /// <param name="callsummary">Object containing Call Summary data</param>
        public async Task SetCallSummary(string customerName, string tenantPsapName, EventReceiver.ElasticSearch.Entities.CallSummary callsummary)
        {
            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                dal.SetCallSummary(customerName, tenantPsapName, callsummary);
            }
        }

        /// <summary>
        /// Sets list of events
        /// </summary>
        /// <param name="customerName">Customer Name (aka. Clientid)</param>
        /// <param name="tenantPsapName">Customer specific Tenant</param>
        /// <param name="eventLogList">List of Event Logs</param>
        public async Task SetCallEventByList(string customerName, string tenantPsapName, List<EventLog> eventLogList)
        {
            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                int logNumber = 0;

                foreach(EventLog eventLog in eventLogList)
                {
                    eventLog.logNumber = logNumber++;
                    dal.SetCallEvent(customerName, tenantPsapName, eventLog);
                }
            }
        }

        /// <summary>
        /// Checks to see if the given Call identifier was previously processed
        /// </summary>
        /// <param name="customerName">Unique Customer Name</param>
        /// <param name="callIdentifier">Unique call identifier</param>
        /// <returns>if previously processed, with when and how many records found</returns>
        public async Task<(bool, DateTime, long)> CallSummaryExists(string customerName, string callIdentifier)
        { 
            using(DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                return dal.CallSummaryExists(customerName, callIdentifier);
            }
        }

        /// <summary>
        /// Deletes all associated data to a given Call identifier from the final data
        /// </summary>
        /// <param name="customerName">Unique Customer Name</param>
        /// <param name="callIdentifier">Unique call identifier</param>
        /// <returns>number of Call Summary records and Event records deleted.</returns>
        public async Task<(long, long)> DeleteCallData(string customerName, string callIdentifier)
        {
            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                return dal.DeleteCallData(customerName, callIdentifier);
            }

        }

    }
}