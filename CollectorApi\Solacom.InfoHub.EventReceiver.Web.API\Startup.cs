﻿using System;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Nest;
using Serilog;
using Serilog.Enrichers.AspnetcoreHttpcontext;
using Solacom.InfoHub.EventReceiver.AppService;
using Solacom.InfoHub.EventReceiver.AppService.Interfaces;
using Solacom.InfoHub.EventReceiver.BusinessLogic;
using Solacom.InfoHub.EventReceiver.BusinessLogic.Interfaces;
using Solacom.InfoHub.EventReceiver.Entities;
using Solacom.InfoHub.EventReceiver.MariaDb.Context;
using Solacom.InfoHub.EventReceiver.MariaDb.Context.Interfaces;
using Solacom.InfoHub.EventReceiver.Web.API.Middlewares;
using Solacom.InfoHub.EventReceiver.ElasticSearch.Entities;
using Solacom.InfoHub.EventReceiver.Web.API.ScheduledTasks;
using Solacom.InfoHub.EventReceiver.Web.API.Security;
using IHostingEnvironment = Microsoft.AspNetCore.Hosting.IHostingEnvironment;
using Solacom.InfoHub.EventReceiver.Entities.Security;

namespace Solacom.InfoHub.EventReceiver.Web.API
{
    public class Startup
    {
        /// <summary>
        /// Enables a relative log of any SeriLog usage errors - introduced for debugging purposes when required.
        /// </summary>
        protected bool _DEBUG_SYSLOG = false;

        public Startup(IConfiguration configuration, IServiceProvider provider)
        {
            var logConfiguration = new LoggerConfiguration()
               .Enrich.WithAspnetcoreHttpcontext(provider)
               .ReadFrom.Configuration(configuration);


            #warning UPDATE SeriLog once Typename bug is fixed.  - going back to appsetting full definition.
            #region Force setting the Elastic Search serilog definition - TO BE UPDATED once seriLog fix is provided.
            //Actions to do once serilog is updated: ref: https://solacomtech.atlassian.net/browse/INFO-1461 / https://github.com/serilog-contrib/serilog-sinks-elasticsearch/pull/420
            //Move this location into the _DEBUG_SYSLOG once we have the proper fix
            //remove the ELSE condition / setting the log configuration for ES
            Uri elastic_URI = new Uri(Encryption.Decrypt(configuration["Logging:elasticsearchSettings:url"]));
            //note: built based on the URI to inject the username/password - port is optional and will still work if not available.
            string elasticLog_url = $"{elastic_URI.Scheme}://{Encryption.Decrypt(configuration["Logging:elasticsearchSettings:userName"])}:{Encryption.Decrypt(configuration["Logging:elasticsearchSettings:password"])}@{elastic_URI.Host}:{elastic_URI.Port}";
            Serilog.Sinks.Elasticsearch.AutoRegisterTemplateVersion templateVersion = Serilog.Sinks.Elasticsearch.AutoRegisterTemplateVersion.ESv7;
            Enum.TryParse(configuration["Logging:elasticsearchSettings:serilog:autoRegisterTemplateVersion"], out templateVersion);
            if (_DEBUG_SYSLOG)
            {
                logConfiguration.WriteTo.Elasticsearch(new Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions(new Uri(elasticLog_url))
                {
                    FailureCallback = e => Console.WriteLine($"Unable to submit log event: {e.MessageTemplate}"),
                    EmitEventFailure = Serilog.Sinks.Elasticsearch.EmitEventFailureHandling.WriteToSelfLog |
                                       Serilog.Sinks.Elasticsearch.EmitEventFailureHandling.WriteToFailureSink |
                                       Serilog.Sinks.Elasticsearch.EmitEventFailureHandling.RaiseCallback,
                    AutoRegisterTemplate = (configuration["Logging:elasticsearchSettings:serilog:autoRegisterTemplate"] == "true"),
                    AutoRegisterTemplateVersion = templateVersion,
                    IndexFormat = configuration["Logging:elasticsearchSettings:serilog:indexFormat"],
                    MinimumLogEventLevel = Serilog.Events.LogEventLevel.Verbose,    //always set to the lowest level to enable full debugging information
                    TypeName = null
                });
            }
            else
            {
                if (configuration["Logging:elasticsearchSettings:serilog:enableSSL"] != "false")
                {
                    Serilog.Events.LogEventLevel loglevel;
                    Enum.TryParse(configuration["Logging:elasticsearchSettings:serilog:restrictedToMinimumLevel"], out loglevel);
                    logConfiguration.WriteTo.Elasticsearch(new Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions(new Uri(elasticLog_url))
                    {
                        AutoRegisterTemplate = (configuration["Logging:elasticsearchSettings:serilog:autoRegisterTemplate"] == "true"),
                        AutoRegisterTemplateVersion = templateVersion,
                        IndexFormat = configuration["Logging:elasticsearchSettings:serilog:indexFormat"],
                        MinimumLogEventLevel = loglevel,
                        TypeName = null
                    });
                }
                else
                {
                    Serilog.Events.LogEventLevel loglevel;
                    Enum.TryParse(configuration["Logging:elasticsearchSettings:serilog:restrictedToMinimumLevel"], out loglevel);
                    logConfiguration.WriteTo.Elasticsearch(new Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions(new Uri(elasticLog_url))
                    {
                        AutoRegisterTemplate = (configuration["Logging:elasticsearchSettings:serilog:autoRegisterTemplate"] == "true"),
                        AutoRegisterTemplateVersion = templateVersion,
                        IndexFormat = configuration["Logging:elasticsearchSettings:serilog:indexFormat"],
                        MinimumLogEventLevel = loglevel,
                        ModifyConnectionSettings = configuration => configuration.ServerCertificateValidationCallback((o, certificate, arg3, arg4) => { return true; }),
                        TypeName = null
                    });
                }
            }
            #endregion

            Log.Logger = logConfiguration.CreateLogger();
            Configuration = configuration;

            if(_DEBUG_SYSLOG)
            {
                //section of code sets the log file for the Sys log errors
                var file = System.IO.File.CreateText("./Logs/SysLog.TextLog.txt");
                Serilog.Debugging.SelfLog.Enable(System.IO.TextWriter.Synchronized(file));
            }

            AboutApiData about = new AboutApiData
            {
                Environment = String.Empty,
                Name = "Collector API",
                Version = Configuration.GetSection("version").Value,
                Build = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString()
            };

            Log.Logger.Warning($"Collector starting up, running {about.VersionOrBuild}");

            if( _DEBUG_SYSLOG)
            {
                //line to set a initial write line to the log file
                Serilog.Debugging.SelfLog.WriteLine($"Serlog Debugging enabled. Running Version {about.VersionOrBuild} to URL target: {elasticLog_url}");
            }
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddHostedService<DBCleanupService>();
            services.AddHostedService<ExpiredEventService>();
            services.AddHostedService<ProcessQueueService>();
            services.AddTransient<IEventsService, EventsService>();
            services.AddTransient<IEventsManager, EventsManager>();
            services.AddTransient<IUserKeyManager, UserKeyManager>();
            services.AddScoped<IUnitOfWork, UnitOfWork>();
            services.AddScoped<IUnitOfWork_InsightsData, UnitOfWork_InsightsData>();
            
            services.AddMemoryCache();
            services.AddDistributedMemoryCache();

            var section = Configuration.GetSection("classofservice");
            services.Configure<Classofservice>(section);
            section = Configuration.GetSection("userKeyData");
            services.Configure<UserKeyData>(section);
            section = Configuration.GetSection("scheduledServicesFrequenciesInMinutes");
            services.Configure<ScheduledServicesFrequenciesInMinutes>(section);
            services.AddMvc().AddXmlSerializerFormatters().SetCompatibilityVersion(CompatibilityVersion.Version_3_0);
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IHostingEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.UseDefaultFiles();
            app.UseStaticFiles();

            app.UseMiddleware(typeof(ErrorHandlingMiddleware));

            app.UseRouting();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute(
                    name: "default",
                    pattern: "{controller=Home}/{action=Index}/{id?}");
            });
        }
    }
}
