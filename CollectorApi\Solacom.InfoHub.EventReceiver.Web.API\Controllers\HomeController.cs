﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Solacom.InfoHub.EventReceiver.Entities;
using Solacom.InfoHub.EventReceiver.Entities.Security;
using System;
using System.Threading.Tasks;

// For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace Solacom.InfoHub.EventReceiver.Web.API.Controllers
{
    public class HomeController : Controller
    {
        private readonly IConfiguration _config;
        private readonly ILogger<HomeController> _logger;
        private readonly IWebHostEnvironment _env;

        public HomeController(IConfiguration configuration, ILogger<HomeController> logger, IWebHostEnvironment env)
        {
            _config = configuration;
            _env = env;
            _logger = logger;
        }
        // GET: /<controller>/
        public IActionResult Index()
        {
            var data = new AboutApiData {Environment = _env.EnvironmentName, Name = "Collector API", 
                                            Version = _config.GetSection("version").Value, 
                                            Build = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString() 
                                        };
            return View(data);
        }

        /// <summary>
        /// Encrypt payload is possible
        /// </summary>
        [HttpPost]
        [Route("encrypt")]
        [AllowAnonymous]
        public async Task<string> GetEncrypt()
        {
            string payload = string.Empty;

            using (var reader = new System.IO.StreamReader(Request.Body, System.Text.Encoding.UTF8))
                {
                    payload = await reader.ReadToEndAsync();
                }

            string rtnEncryption = Encryption.Encrypt(payload);
            _logger.LogInformation($"Encryption Event called. Result: [{rtnEncryption}]");
            return rtnEncryption;
        }
    }
}
