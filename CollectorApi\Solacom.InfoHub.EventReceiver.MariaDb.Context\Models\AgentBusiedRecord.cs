﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Solacom.InfoHub.EventReceiver.MariaDb.Context.Models
{
    public class AgentBusiedRecord
    {
        public int Id { get; set; }
        public DateTime? StartBusyInterval { get; set; }
        public DateTime? EndBusyInterval { get; set; }
        public float? IntervalTimeInMinutes { get; set; }
        public string BusiedOutAction { get; set; }
        public int AgentSessionRecordId { get; set; }
    }
}
