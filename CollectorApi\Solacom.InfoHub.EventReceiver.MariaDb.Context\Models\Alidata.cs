﻿
namespace Solacom.InfoHub.EventReceiver.MariaDb.Context.Models
{
    public partial class Alidata
    {
        /// <summary>
        /// The defined City 
        /// </summary>
        /// <remarks>Currently not being used</remarks>
        public string City { get; set; }
        /// <summary>
        /// State / Providence
        /// </summary>
        /// <remarks>Currently not being used</remarks>
        public string State { get; set; }
        /// <summary>
        /// Country
        /// </summary>
        /// <remarks>Currently not being used</remarks>
        public string Country { get; set; }
        /// <summary>
        /// Zip / postal code
        /// </summary>
        /// <remarks>Currently not being used</remarks>
        public string Zipcode { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        public double? StreetNumber { get; set; }
        public double? Esn { get; set; }
        public double? Confidence { get; set; }
        public double? Uncertainty { get; set; }
        public string Classofservice { get; set; }
        /// <summary>
        /// Time of the ALI Response - based on the ALI response and not the ALI data
        /// </summary>
        public string Date { get; set; }
        /// <summary>
        /// Time of the ALI Response - based on the ALI response and not the ALI data
        /// </summary>
        public string Time { get; set; }
        public string Callbacknumber { get; set; }
        public string Address { get; set; }
        /// <summary>
        /// REMOVED - not being used actively in code, keeping it as PRIVATE for awareness for now.
        /// This is NOT populated by the Canadian ALI data right now.
        /// </summary>
        private string Positionid { get; set; }
        /// <summary>
        /// Used to determine if the ALI data COS was found or not.  Defined as "9" when not found, else, it is set to any other numeric.
        /// </summary>
        /// <remarks>Currently set to 0 when found in the CANADA XML logic</remarks>
        public string Type { get; set; }
        public string Carrier { get; set; }
    }
}
