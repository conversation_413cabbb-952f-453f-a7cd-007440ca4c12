﻿using System.Xml.Serialization;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    [XmlRoot(ElementName = "voiceQOS")]
    public class VoiceQOS
    {
        [XmlElement(ElementName = "mediaIpSourceAddr")]
        public string MediaIpSourceAddr { get; set; }
        [XmlElement(ElementName = "mediaIpDestAddr")]
        public string MediaIpDestAddr { get; set; }
        [XmlElement(ElementName = "mediaUdpRtpSourcePort")]
        public string MediaUdpRtpSourcePort { get; set; }
        [XmlElement(ElementName = "mediaUdpRtpDestPort")]
        public string MediaUdpRtpDestPort { get; set; }
        [XmlElement(ElementName = "mediaNumOfIpPktRxed")]
        public string MediaNumOfIpPktRxed { get; set; }
        [XmlElement(ElementName = "mediaNumOfIpPktTxed")]
        public string MediaNumOfIpPktTxed { get; set; }
        [XmlElement(ElementName = "mediaNumOfIpErroredPktRxed")]
        public string MediaNumOfIpErroredPktRxed { get; set; }
        [XmlElement(ElementName = "mediaNumOfRtpPktRxed")]
        public string MediaNumOfRtpPktRxed { get; set; }
        [XmlElement(ElementName = "mediaNumOfRtpPktTxed")]
        public string MediaNumOfRtpPktTxed { get; set; }
        [XmlElement(ElementName = "mediaNumOfRtpPktLost")]
        public string MediaNumOfRtpPktLost { get; set; }
        [XmlElement(ElementName = "mediaNumOfRtpPktDiscarded")]
        public string MediaNumOfRtpPktDiscarded { get; set; }
        [XmlElement(ElementName = "mediaRtpJitter")]
        public string MediaRtpJitter { get; set; }
        [XmlElement(ElementName = "mediaRtpLatency")]
        public string MediaRtpLatency { get; set; }
        [XmlElement(ElementName = "mediaNumOfRtcpPktRxed")]
        public string MediaNumOfRtcpPktRxed { get; set; }
        [XmlElement(ElementName = "mediaNumOfRtcpPktTxed")]
        public string MediaNumOfRtcpPktTxed { get; set; }
        [XmlElement(ElementName = "mediaFarEndPacketLostPercentage")]
        public string MediaFarEndPacketLostPercentage { get; set; }
        [XmlElement(ElementName = "mediaFarEndCumulativePacketLost")]
        public string MediaFarEndCumulativePacketLost { get; set; }
        [XmlElement(ElementName = "mediaFarEndInterarrivalJitter")]
        public string MediaFarEndInterarrivalJitter { get; set; }
    }
}
