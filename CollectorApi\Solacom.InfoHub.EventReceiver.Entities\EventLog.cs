﻿using System;
using System.Xml.Serialization;
using Newtonsoft.Json;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    [XmlRoot(ElementName = "LogEvent", Namespace = "http://solacom.com/Logging")]
    public class EventLog
    {
        public long logNumber { get; set; }
        private DateTime dateTime;
        public DateTime timestamp { get; set; }
        /// <summary>
        /// Timestamp as presented to the Customers configured Timezone.
        /// </summary>
        [XmlIgnore]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DateTime timestampToLocal { get; set; }
        /// <summary>
        /// Timestamp of when the event was received / added to the system
        /// </summary>
        public DateTime eventReceived { get; set; }
        public string agencyOrElement { get; set; }
        public string agent { get; set; }
        public string callIdentifier { get; set; }
        public string incidentIdentifier { get; set; }
        public string eventType { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public EcrfQuery ecrfQuery { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public HeldQuery heldQuery { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public HeldResponse heldResponse { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Media media { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public StartCall startCall { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public EcrfResponse ecrfResponse { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public VpcQuery vpcQuery { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public VpcResponse vpcResponse { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public SrdbQuery srdbQuery { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public SrdbResponse srdbResponse { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public AliQuery aliQuery { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public AliResponse aliResponse { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Route route { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Answer answer { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public TransferCall transferCall { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Hold hold { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public HoldRetrieved holdRetrieved { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public MuteOn muteOn { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public MuteOff muteOff { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public PrivacyOn privacyOn { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public PrivacyOff privacyOff { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public MergeCall mergeCall { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public OutboundCall outboundCall { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public EndMedia endMedia { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public EndCall endCall { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Login login { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Logout logout { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public BusiedOut busiedOut { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public AgentAvailable agentAvailable { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public AcdLogin acdLogin { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public AcdLogout acdLogout { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Message message { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public EIDD EIDD { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public ElementStateChange elementStateChange { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public CdrType1 cdrType1 { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public QueueStateChange QueueStateChange { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Error error { get; set; }
        /// <summary>
        /// No-Support event Type
        /// </summary>
        /// <remarks>set to string to indicate non-parsing event type.</remarks>
        [XmlElement(ElementName = "EIDO")]
        [XmlIgnore]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string EIDO { get; set; }
        /// <summary>
        /// No-Support event Type
        /// </summary>
        /// <remarks>set to string to indicate non-parsing event type.</remarks>
        [XmlElement(ElementName = "EIDORequest")]
        [XmlIgnore]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string EIDORequest { get; set; }

        /// <summary>
        /// Non-support EventType.
        /// </summary>
        /// <remarks>set to string to indicate non-parsing event type.</remarks>
        [XmlElement(ElementName = "additionalDataQuery")]
        [XmlIgnore]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string AdditionalDataQuery { get; set; }

        /// <summary>
        /// Non-support EventType.
        /// </summary>
        /// <remarks>set to string to indicate non-parsing event type.</remarks>
        [XmlElement(ElementName = "removeParty")]
        [XmlIgnore]
        public string RemoveParty { get; set; }

        /// <summary>
        /// Stores the Database process state of the given Event record.
        /// Ignored from any parsing in/out occurence, driven only from the database retrieval logic
        /// </summary>
        [XmlIgnore]
        [JsonIgnore]
        public int DatabaseStateId { get; set; }

    }
}