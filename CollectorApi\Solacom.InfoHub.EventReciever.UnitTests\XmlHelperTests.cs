using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Solacom.InfoHub.EventReceiver.BusinessLogic;
using Solacom.InfoHub.EventReceiver.Exceptions;
using EventLog = Solacom.InfoHub.EventReceiver.Entities.EventLog;
using ALIRawData = Solacom.InfoHub.EventReceiver.BusinessLogic.ALI.ALIRawData;
using ALIHelper = Solacom.InfoHub.EventReceiver.BusinessLogic.ALI.ALIHelper;
using Entities = Solacom.InfoHub.EventReceiver.ElasticSearch.Entities;
using System.Collections.Generic;

namespace Solacom.InfoHub.EventReciever.UnitTests
{
    [TestClass]
    public class XmlHelperTests
    {
        [TestMethod]
        [ExpectedException(typeof(InvalidParemeterException))]
        public void SaveEvent_Throws_Exception_When_Xml_Is_Null()
        {
            XmlHelper.DeserializeXml<EventLog>(null);
        }

        [TestMethod]
        [ExpectedException(typeof(InvalidParemeterException))]
        public void SaveEvent_Throws_Exception_When_Xml_Is_Empty()
        {
            XmlHelper.DeserializeXml<EventLog>("");
        }


        [TestMethod]
        public void SaveEvent_Deserializes_When_Xml_Is_Valid()
        {
          var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType> ECRFquery </eventType>
             <ecrfQuery>
             <mediaLabel> <EMAIL> </mediaLabel>
                <ecrfDomain> URIForECRF </ecrfDomain>
                <service-urn> nena:service: sos </service-urn>
                      <ecrfPurpose> routing </ecrfPurpose>
                      <location> PIDF_LO as per RFC 4119 and RFC 5139 but starting at location-info tag as per NENA ICE8 xsd </location>
                         </ecrfQuery>
                         </LogEvent>
                         ");

          Assert.IsNotNull(data);
        }

        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void SaveEvent_Throws_Exception_When_Xml_Is_Valid_But_Missing_Namespace()
        {
          var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent>
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType> ECRFquery </eventType>
             <ecrfQuery>
             <mediaLabel> <EMAIL> </mediaLabel>
                <ecrfDomain> URIForECRF </ecrfDomain>
                <service-urn> nena:service: sos </service-urn>
                      <ecrfPurpose> routing </ecrfPurpose>
                      <location> PIDF_LO as per RFC 4119 and RFC 5139 but starting at location-info tag as per NENA ICE8 xsd </location>
                         </ecrfQuery>
                         </LogEvent>
                         ");

          Assert.IsNotNull(data);
        }

        [TestMethod]
        public void ECRFQuery_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType> ECRFquery </eventType>
             <ecrfQuery>
             <mediaLabel> <EMAIL> </mediaLabel>
                <ecrfDomain> URIForECRF </ecrfDomain>
                <service-urn> nena:service: sos </service-urn>
                      <ecrfPurpose> routing </ecrfPurpose>
                      <location> PIDF_LO as per RFC 4119 and RFC 5139 but starting at location-info tag as per NENA ICE8 xsd </location>
                         </ecrfQuery>
                         </LogEvent>
                         ");
            Assert.AreEqual(" ECRFquery ", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual(" URIForECRF ", data.ecrfQuery.EcrfDomain);
            Assert.AreEqual(" routing ", data.ecrfQuery.EcrfPurpose);
        }

        [TestMethod]
        public void StartCall_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType> StartCall </eventType>
             <startCall>
            <header>SIP Headers of the INVITE are placed here</header>
            <location>XML document as per RFC3863, RFC4119 and RFC5139</location> <mediaLabel><EMAIL></mediaLabel>
            <incomingCallPolicy>IndianaBell911</incomingCallPolicy>
            <callType>NG911</callType> 
            <signallingType>VOIP</signallingType>
            <circuit>20/03/00/0089</circuit>
            <circuitId>********</circuitId>
            <trunkGroupId>101</trunkGroupId>
            <ani>**********</ani>
            <aniDomain><EMAIL></aniDomain>
            <dnis>911</dnis>
            <dnisDomain><EMAIL></dnisDomain>
            <pani>**********</pani>
            <esrn>8197781234</esrn>
            <callerName>Joe Smith</callerName>
            </startCall>
                         </LogEvent>
                         ");

            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("**********", data.startCall.Ani);
            Assert.AreEqual("20/03/00/0089", data.startCall.Circuit);
            Assert.AreEqual("Joe Smith", data.startCall.CallerName);
        }

        [TestMethod]
        public void StartCall_CDATA_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType> StartCall </eventType>
             <startCall>
            <header><![CDATA[INVITE sip:840@*********** SIP/2.0  Via: SIP/2.0/UDP ***********;branch=z9hG4bK3b5f.f63ccf52.0  To: sip:840@***********  From: sipp <sip:**********@***********:5067>;tag=77b135c98f2b6f4b1a4400c5af4383b7-8fa7  CSeq: 2 INVITE  Call-ID: B2B.253.6454199  Max-Forwards: 70  Content-Length: 2070  User-Agent: OpenSIPS (1.8.1-notls (x86_64/linux))  Content-Type: multipart/mixed;boundary=++  Supported: geolocation  Priority: emergency  Geolocation: <cid:<EMAIL>:5060;user=phone>;inserted-by="" < sip:A911 @a911net.net > "";used-for-routing  Geolocation-Routing: yes  Initial-CallID: 2-9648@***********  Contact: <sip:***********:5060>  Call-Info: <<EMAIL>> ;purpose= nena-IncidentId  Call-Info: <urn:nena:uid:callid:E30444850D0D5B024444850D0D5B02461814044444850D0D5B02461814E444850D0D5B024618804D5B044444850D0D5B02461814E4448D5B044444850D0D5B02461814E444850D0D5B024618804D5B044444850D0D5B0246181435252352353252523qE444850D0D5B02461814E61814E9:ng-x-lsrgapp1.xypoint.com> ;purpose= nena-CallId]]></header>
            <location>XML document as per RFC3863, RFC4119 and RFC5139</location> 
            <mediaLabel><EMAIL></mediaLabel>
            <incomingCallPolicy>IndianaBell911</incomingCallPolicy>
            <callType>NG911</callType> 
            <signallingType>VOIP</signallingType>
            <circuit>20/03/00/0089</circuit>
            <circuitId>********</circuitId>
            <trunkGroupId>101</trunkGroupId>
            <ani>**********</ani>
            <aniDomain><EMAIL></aniDomain>
            <dnis>911</dnis>
            <dnisDomain><EMAIL></dnisDomain>
            <pani>**********</pani>
            <esrn>8197781234</esrn>
            <callerName>Joe Smith</callerName>
            </startCall>
                         </LogEvent>
                         ");

            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("**********", data.startCall.Ani);
            Assert.AreEqual("20/03/00/0089", data.startCall.Circuit);
            Assert.AreEqual("Joe Smith", data.startCall.CallerName);
        }

        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void StartCall_No_CDATA_Deserialized_When_Header_has_XML()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType> StartCall </eventType>
             <startCall>
            <header><[INVITE sip:840@*********** SIP/2.0  Via: SIP/2.0/UDP ***********;branch=z9hG4bK3b5f.f63ccf52.0  To: sip:840@***********  From: sipp <sip:**********@***********:5067>;tag=77b135c98f2b6f4b1a4400c5af4383b7-8fa7  CSeq: 2 INVITE  Call-ID: B2B.253.6454199  Max-Forwards: 70  Content-Length: 2070  User-Agent: OpenSIPS (1.8.1-notls (x86_64/linux))  Content-Type: multipart/mixed;boundary=++  Supported: geolocation  Priority: emergency  Geolocation: <cid:<EMAIL>:5060;user=phone>;inserted-by="" < sip:A911 @a911net.net > "";used-for-routing  Geolocation-Routing: yes  Initial-CallID: 2-9648@***********  Contact: <sip:***********:5060>  Call-Info: <<EMAIL>> ;purpose= nena-IncidentId  Call-Info: <urn:nena:uid:callid:E30444850D0D5B024444850D0D5B02461814044444850D0D5B02461814E444850D0D5B024618804D5B044444850D0D5B02461814E4448D5B044444850D0D5B02461814E444850D0D5B024618804D5B044444850D0D5B0246181435252352353252523qE444850D0D5B02461814E61814E9:ng-x-lsrgapp1.xypoint.com> ;purpose= nena-CallId]></header>
            <location>XML document as per RFC3863, RFC4119 and RFC5139</location> 
            <mediaLabel><EMAIL></mediaLabel>
            <incomingCallPolicy>IndianaBell911</incomingCallPolicy>
            <callType>NG911</callType> 
            <signallingType>VOIP</signallingType>
            <circuit>20/03/00/0089</circuit>
            <circuitId>********</circuitId>
            <trunkGroupId>101</trunkGroupId>
            <ani>**********</ani>
            <aniDomain><EMAIL></aniDomain>
            <dnis>911</dnis>
            <dnisDomain><EMAIL></dnisDomain>
            <pani>**********</pani>
            <esrn>8197781234</esrn>
            <callerName>Joe Smith</callerName>
            </startCall>
                         </LogEvent>
                         ");

            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("**********", data.startCall.Ani);
            Assert.AreEqual("20/03/00/0089", data.startCall.Circuit);
            Assert.AreEqual("Joe Smith", data.startCall.CallerName);
        }

        [TestMethod]
        public void HELDQuery_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType>HELDQuery</eventType>
             <heldQuery>
            <mediaLabel><EMAIL></mediaLabel>
            <heldDomain>URIForHELDServer</heldDomain>
            <heldPurpose>InitialLocation</heldPurpose>
            <held-uri>tel:+**********</held-uri>
            </heldQuery>
                         </LogEvent>
                         ");
            Assert.AreEqual("HELDQuery", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("URIForHELDServer", data.heldQuery.HeldDomain);
            Assert.AreEqual("tel:+**********", data.heldQuery.Helduri);
            Assert.AreEqual("InitialLocation", data.heldQuery.HeldPurpose);
        }

        [TestMethod]
        public void HELDResponse_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType>HELDresponse</eventType>
             <heldResponse>
        <mediaLabel><EMAIL></mediaLabel>
        <heldDomain>URIForHELDServer</heldDomain>
        <responseCode>200</responseCode>
        <held>XML document as per RFC5985</held>
        </heldResponse>
                         </LogEvent>
                         ");
            Assert.AreEqual("HELDresponse", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("200", data.heldResponse.ResponseCode);
            Assert.AreEqual("URIForHELDServer", data.heldResponse.HeldDomain);
            Assert.AreEqual("<EMAIL>", data.heldResponse.MediaLabel);
        }

        [TestMethod]
        public void Media_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType>Media</eventType>
             <media>
            <udp>SDP is placed here</udp> 
            <mediaLabel><EMAIL></mediaLabel>
            <mediaType>Voice</mediaType>
            </media>
                         </LogEvent>
                         ");
            Assert.AreEqual("Media", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("SDP is placed here", data.media.Udp);
            Assert.AreEqual("<EMAIL>", data.media.MediaLabel);
            Assert.AreEqual("Voice", data.media.MediaType);
        }

        [TestMethod]
        public void EcrfResponse_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType>ECRFResponse</eventType>
             <ecrfResponse>
            <mediaLabel><EMAIL></mediaLabel>
            <ecrfDomain>URI For ECRF</ecrfDomain>
            <responseCode>200</responseCode>
            <lost>XML body of LoST Response as per RFC 5222</lost>
            </ecrfResponse>
                         </LogEvent>
                         ");
            Assert.AreEqual("ECRFResponse", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("200", data.ecrfResponse.ResponseCode);
            Assert.AreEqual("URI For ECRF", data.ecrfResponse.EcrfDomain);
            Assert.AreEqual("<EMAIL>", data.ecrfResponse.MediaLabel);
        }

        [TestMethod]
        public void VpcQuery_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType>VPCQuery</eventType>
             <vpcQuery>
            <mediaLabel><EMAIL></mediaLabel>
            <vpcDomain>URIForVPC</vpcDomain>
            <ani>8195551234</ani>
            <dnis>911</dnis>
            </vpcQuery>
                         </LogEvent>
                         ");
            Assert.AreEqual("VPCQuery", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("8195551234", data.vpcQuery.Ani);
            Assert.AreEqual("911", data.vpcQuery.Dnis);
            Assert.AreEqual("<EMAIL>", data.vpcQuery.MediaLabel);
        }

        [TestMethod]
        public void VpcResponse_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType>VPCResponse</eventType>
             <vpcResponse>
            <mediaLabel><EMAIL></mediaLabel>
            <vpcDomain>URIForECRF</vpcDomain>
            <responseCode>200</responseCode>
            <esrn>8197781234</esrn>
            <esqk>8195551010</esqk>
            <esn>4500</esn>
            </vpcResponse>
                         </LogEvent>
                         ");
            Assert.AreEqual("VPCResponse", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("200", data.vpcResponse.ResponseCode);
            Assert.AreEqual("8195551010", data.vpcResponse.Esqk);
            Assert.AreEqual("URIForECRF", data.vpcResponse.VpcDomain);
        }

        [TestMethod]
        public void SrdbQuery_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType>SRDBQuery</eventType>
             <srdbQuery>
            <mediaLabel><EMAIL></mediaLabel>
            <ani>8197781234</ani>
            </srdbQuery>
                         </LogEvent>
                         ");
            Assert.AreEqual("SRDBQuery", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("8197781234", data.srdbQuery.Ani);
            Assert.AreEqual("<EMAIL>", data.srdbQuery.MediaLabel);
        }

        [TestMethod]
        public void SrdbResponse_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType>SRDBResponse</eventType>
             <srdbResponse>
            <mediaLabel><EMAIL></mediaLabel>
            <responseCode>Success</responseCode>
            <esn>4123</esn>
            </srdbResponse>
                         </LogEvent>
                         ");
            Assert.AreEqual("SRDBResponse", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("4123", data.srdbResponse.Esn);
            Assert.AreEqual("Success", data.srdbResponse.ResponseCode);
            Assert.AreEqual("<EMAIL>", data.srdbResponse.MediaLabel);
        }

        [TestMethod]
        public void AliQuery_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType>ALIQuery</eventType>
             <aliQuery>
            <mediaLabel><EMAIL></mediaLabel>
            <aliLink>ATT link 1</aliLink>
            <uri>tel:+8197781234</uri>
            <serviceArea>991</serviceArea>
            <aliQueryType>AutomaticInitial</aliQueryType>
            </aliQuery>
                         </LogEvent>
                         ");
            Assert.AreEqual("ALIQuery", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("991", data.aliQuery.ServiceArea);
            Assert.AreEqual("AutomaticInitial", data.aliQuery.AliQueryType);
            Assert.AreEqual("<EMAIL>", data.aliQuery.MediaLabel);
        }

        [TestMethod]
        public void AliResponse_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType>ALIResponse</eventType>
             <aliResponse>
            <mediaLabel><EMAIL></mediaLabel>
            <aliLink>ATT link 1</aliLink>
            <ali>Raw ALI text</ali>
            <aliResponseCode> Data retrieved, both paths operational </aliResponseCode>
            </aliResponse>
                         </LogEvent>
                         ");
            Assert.AreEqual("ALIResponse", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("Raw ALI text", data.aliResponse.Ali);
            Assert.AreEqual("ATT link 1", data.aliResponse.AliLink);
            Assert.AreEqual(" Data retrieved, both paths operational ", data.aliResponse.AliResponseCode);
        }

        [TestMethod]
        public void Route_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType>Route</eventType>
             <route>
            <uri>tel:+6432341234</uri>
            <rule>CountyXPsapRoute2</rule>
            <reason>normal</reason>
            <mediaLabel><EMAIL></mediaLabel>
            <attempt>1</attempt>
            <priority>1</priority>
            <ani>9200000003</ani>
            <aniDomain><EMAIL></aniDomain>
            <dnis>6432341234</dnis>
            <pani>8191230987</pani>
            <esrn>8197781234</esrn>
            <callerName>Joe Smith</callerName>
            <aniTranslated>9200000003</aniTranslated>
            <dnisTranslated>6432341234</dnisTranslated>
            <callerNameTranslated>8191230987</callerNameTranslated>
            </route>
                         </LogEvent>
                         ");
            Assert.AreEqual("Route", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("8197781234", data.route.Esrn);
            Assert.AreEqual("8191230987", data.route.CallerNameTranslated);
            Assert.AreEqual("<EMAIL>", data.route.AniDomain);
        }

        [TestMethod]
        public void Answer_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType>Answer</eventType>
             <answer>
            <mediaLabel><EMAIL></mediaLabel>
            <uri>tel:+6432341234</uri>
            <agentRole>operator</agentRole>
            <tenantGroup>chicago.psap.il.us</tenantGroup>
            <operatorId>001</operatorId>
            <workstation>PC Host name</workstation>
            </answer>
                         </LogEvent>
                         ");
            Assert.AreEqual("Answer", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("chicago.psap.il.us", data.answer.TenantGroup);
            Assert.AreEqual("tel:+6432341234", data.answer.Uri);
            Assert.AreEqual("<EMAIL>", data.answer.MediaLabel);
        }

        [TestMethod]
        public void TransferCall_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
            <eventType>TransferCall</eventType>
            <transferCall>
            <transferTarget>tel:+ 6432341234</transferTarget>
            <mediaLabel><EMAIL></mediaLabel>
            <originatorMediaLabel><EMAIL></originatorMediaLabel >
            <rule>CountyXPsapRoute2</rule>
            <reason>normal</reason>
            <attempt>1</attempt>
            <priority>1</priority>
            <ani>9200000003</ani>
            <aniDomain><EMAIL></aniDomain>
            <dnis>6432341234</dnis>
            <pani>8191230987</pani>
            <callerName>Joe Smith</callerName>
            <aniTranslated>9200000003</aniTranslated>
            <dnisTranslated>6432341234</dnisTranslated>
            <callerNameTranslated>Joe Smith</callerNameTranslated>
            <method>Direct Access Button</method>
            <targetType>Ring Group</targetType>
            <targetName>State Patrol</targetName>
            </transferCall>
                         </LogEvent>
                         ");
            Assert.AreEqual("TransferCall", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("6432341234", data.transferCall.DnisTranslated);
            Assert.AreEqual("Ring Group", data.transferCall.TargetType);
            Assert.AreEqual("Joe Smith", data.transferCall.CallerNameTranslated);
        }

        [TestMethod]
        public void Hold_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType>Hold</eventType>
             <hold>
            <mediaLabel><EMAIL></mediaLabel>
            <holdType>Normal</holdType>
            </hold>
                         </LogEvent>
                         ");
            Assert.AreEqual("Hold", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("Normal", data.hold.HoldType);
            Assert.AreEqual("<EMAIL>", data.hold.MediaLabel);
        }

        [TestMethod]
        public void HoldRetrieved_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType>HoldRetrieved</eventType>
             <holdRetrieved>

            <mediaLabel><EMAIL></mediaLabel>

            </holdRetrieved>
                         </LogEvent>
                         ");
            Assert.AreEqual("HoldRetrieved", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("<EMAIL>", data.holdRetrieved.MediaLabel);
        }

        [TestMethod]
        public void MuteOn_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType>MuteOn</eventType>
             <muteOn>
            <mediaLabel><EMAIL></mediaLabel>
            </muteOn>
                         </LogEvent>
                         ");
            Assert.AreEqual("MuteOn", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("<EMAIL>", data.muteOn.MediaLabel);
        }

        [TestMethod]
        public void MuteOff_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType>MuteOff</eventType>
             <muteOff>
            <mediaLabel><EMAIL></mediaLabel>
            </muteOff>
                         </LogEvent>
                         ");
            Assert.AreEqual("MuteOff", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("<EMAIL>", data.muteOff.MediaLabel);
        }

        [TestMethod]
        public void PrivacyOn_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType>PrivacyOn</eventType>
             <privacyOn>
            <mediaLabel><EMAIL></mediaLabel>
            </privacyOn>
                         </LogEvent>
                         ");
            Assert.AreEqual("PrivacyOn", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("<EMAIL>", data.privacyOn.MediaLabel);
        }

        [TestMethod]
        public void PrivacyOff_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
             <eventType>PrivacyOff</eventType>
             <privacyOff>
            <mediaLabel><EMAIL></mediaLabel>
            </privacyOff>
                         </LogEvent>
                         ");
            Assert.AreEqual("PrivacyOff", data.eventType);
            Assert.AreEqual(" esrp.state.pa.us ", data.agencyOrElement);
            Assert.AreEqual("<EMAIL>", data.privacyOff.MediaLabel);
        }

        [TestMethod]
        public void MergeCall_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
            <eventType>MergeCall</eventType>
            <mergeCall>
            <callIdentifier2><EMAIL></callIdentifier2>
            <incidentIdentifier2><EMAIL></incidentIdentifier2>
            </mergeCall>
                         </LogEvent>
                         ");
            Assert.AreEqual("MergeCall", data.eventType);
            Assert.AreEqual("<EMAIL>", data.mergeCall.CallIdentifier2);
            Assert.AreEqual("<EMAIL>", data.mergeCall.IncidentIdentifier2);
        }

        [TestMethod]
        public void OutboundCall_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
            <eventType>OutboundCall</eventType>
            <outboundCall>
            <outboundTarget>tel:+6432341234</outboundTarget>
            <rule>CountyXPsapRoute2</rule>
            <reason>normal</reason>
            <mediaLabel><EMAIL></mediaLabel>
            <attempt>1</attempt>
            <priority>1</priority>
            <ani>9200000003</ani>
            <aniDomain><EMAIL></aniDomain>
            <dnis>6432341234</dnis>
            <pani>8191230987</pani>
            <callerName>Joe Smith</callerName>
            <aniTranslated>9200000003</aniTranslated>
            <dnisTranslated>6432341234</dnisTranslated>
            <callerNameTranslated>8191230987</callerNameTranslated>
            <method>Direct Access Button</method>
            <targetType>Ring Group</targetType>
            <targetName>IL State PD</targetName>
            </outboundCall>
                         </LogEvent>
                         ");
            Assert.AreEqual("OutboundCall", data.eventType);
            Assert.AreEqual("tel:+6432341234", data.outboundCall.OutboundTarget);
            Assert.AreEqual("CountyXPsapRoute2", data.outboundCall.Rule);
            Assert.AreEqual("normal", data.outboundCall.Reason);
            Assert.AreEqual("<EMAIL>", data.outboundCall.MediaLabel);
            Assert.AreEqual("1", data.outboundCall.Attempt);
            Assert.AreEqual("1", data.outboundCall.Priority);
            Assert.AreEqual("9200000003", data.outboundCall.Ani);
            Assert.AreEqual("<EMAIL>", data.outboundCall.AniDomain);
            Assert.AreEqual("6432341234", data.outboundCall.Dnis);
            Assert.AreEqual("8191230987", data.outboundCall.Pani);
            Assert.AreEqual("Joe Smith", data.outboundCall.CallerName);
            Assert.AreEqual("9200000003", data.outboundCall.AniTranslated);
            Assert.AreEqual("6432341234", data.outboundCall.DnisTranslated);
            Assert.AreEqual("8191230987", data.outboundCall.CallerNameTranslated);
            Assert.AreEqual("Direct Access Button", data.outboundCall.Method);
            Assert.AreEqual("Ring Group", data.outboundCall.TargetType);
            Assert.AreEqual("IL State PD", data.outboundCall.TargetName);
        }

        [TestMethod]
        public void EndMedia_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
            <eventType>EndMedia</eventType>
            <endMedia>
            <mediaLabel><EMAIL></mediaLabel>
            <responseCode>16</responseCode>
            <disconnectReason>FireEmergency</disconnectReason>
            <voiceQOS>
            <mediaIpSourceAddr>***********</mediaIpSourceAddr>
            <mediaIpDestAddr>***********</mediaIpDestAddr>
            <mediaUdpRtpSourcePort>5000</mediaUdpRtpSourcePort>
            <mediaUdpRtpDestPort>5000</mediaUdpRtpDestPort>
            <mediaNumOfIpPktRxed>1</mediaNumOfIpPktRxed>
            <mediaNumOfIpPktTxed>1</mediaNumOfIpPktTxed>
            <mediaNumOfIpErroredPktRxed>1</mediaNumOfIpErroredPktRxed>
            <mediaNumOfRtpPktRxed>1</mediaNumOfRtpPktRxed>
            <mediaNumOfRtpPktTxed>1</mediaNumOfRtpPktTxed>
            <mediaNumOfRtpPktLost>1</mediaNumOfRtpPktLost>
            <mediaNumOfRtpPktDiscarded>1</mediaNumOfRtpPktDiscarded>
            <mediaRtpJitter>1</mediaRtpJitter>
            <mediaRtpLatency>1</mediaRtpLatency>
            <mediaNumOfRtcpPktRxed>1</mediaNumOfRtcpPktRxed>
            <mediaNumOfRtcpPktTxed>1</mediaNumOfRtcpPktTxed>
            <mediaFarEndPacketLostPercentage>1</mediaFarEndPacketLostPercentage>
            <mediaFarEndCumulativePacketLost>1</mediaFarEndCumulativePacketLost>
            <mediaFarEndInterarrivalJitter>1</mediaFarEndInterarrivalJitter>
            </voiceQOS>
            </endMedia>
                         </LogEvent>
                         ");
            Assert.AreEqual("EndMedia", data.eventType);
            Assert.AreEqual("<EMAIL>", data.endMedia.MediaLabel);
            Assert.AreEqual("16", data.endMedia.ResponseCode);
            Assert.AreEqual("FireEmergency", data.endMedia.DisconnectReason);
            Assert.AreEqual("***********", data.endMedia.VoiceQOS.MediaIpSourceAddr);
            Assert.AreEqual("***********", data.endMedia.VoiceQOS.MediaIpDestAddr);
            Assert.AreEqual("5000", data.endMedia.VoiceQOS.MediaUdpRtpSourcePort);
            Assert.AreEqual("5000", data.endMedia.VoiceQOS.MediaUdpRtpDestPort);
            Assert.AreEqual("1", data.endMedia.VoiceQOS.MediaNumOfIpPktRxed);
            Assert.AreEqual("1", data.endMedia.VoiceQOS.MediaNumOfIpPktTxed);
            Assert.AreEqual("1", data.endMedia.VoiceQOS.MediaNumOfIpErroredPktRxed);
            Assert.AreEqual("1", data.endMedia.VoiceQOS.MediaNumOfRtcpPktRxed);
            Assert.AreEqual("1", data.endMedia.VoiceQOS.MediaNumOfRtcpPktTxed);
            Assert.AreEqual("1", data.endMedia.VoiceQOS.MediaNumOfRtpPktLost);
            Assert.AreEqual("1", data.endMedia.VoiceQOS.MediaNumOfRtpPktDiscarded);
            Assert.AreEqual("1", data.endMedia.VoiceQOS.MediaRtpJitter);
            Assert.AreEqual("1", data.endMedia.VoiceQOS.MediaRtpLatency);
            Assert.AreEqual("1", data.endMedia.VoiceQOS.MediaNumOfRtpPktRxed);
            Assert.AreEqual("1", data.endMedia.VoiceQOS.MediaNumOfRtpPktTxed);
            Assert.AreEqual("1", data.endMedia.VoiceQOS.MediaFarEndPacketLostPercentage);
            Assert.AreEqual("1", data.endMedia.VoiceQOS.MediaFarEndCumulativePacketLost);
            Assert.AreEqual("1", data.endMedia.VoiceQOS.MediaFarEndInterarrivalJitter);
        }

        [TestMethod]
        public void EndCall_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
            <eventType>EndCall</eventType>
            <endCall>
            <responseCode>16</responseCode>
            <callReplaced>0</callReplaced>
            </endCall>
                         </LogEvent>
                         ");
            Assert.AreEqual("EndCall", data.eventType);
            Assert.AreEqual(16, data.endCall.ResponseCode);
            Assert.AreEqual("0", data.endCall.CallReplaced);
        }

        [TestMethod]
        public void Login_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
            <eventType>Login</eventType>
            <login>
            <mediaLabel><EMAIL></mediaLabel>
            <uri>tel:+6432341234</uri>
            <agentRole>operator</agentRole>
            <tenantGroup>chicago.psap.il.us</tenantGroup>
            <operatorId>001</operatorId>
            <workstation>PC Host name</workstation>
            <deviceName>Headset</deviceName>
            <reason>normal</reason>
            </login>
                         </LogEvent>
                         ");
            Assert.AreEqual("Login", data.eventType);
            Assert.AreEqual("<EMAIL>", data.login.MediaLabel);
            Assert.AreEqual("tel:+6432341234", data.login.Uri);
            Assert.AreEqual("operator", data.login.AgentRole);
            Assert.AreEqual("chicago.psap.il.us", data.login.TenantGroup);
            Assert.AreEqual("001", data.login.OperatorId);
            Assert.AreEqual("PC Host name", data.login.Workstation);
            Assert.AreEqual("Headset", data.login.DeviceName);
            Assert.AreEqual("normal", data.login.Reason);
        }

        [TestMethod]
        public void Logout_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
            <eventType>Logout</eventType>
            <logout>
            <mediaLabel><EMAIL></mediaLabel>
            <uri>tel:+6432341234</uri>
            <agentRole>operator</agentRole>
            <tenantGroup>chicago.psap.il.us</tenantGroup>
            <operatorId>001</operatorId>
            <workstation>PC Host name</workstation>
            <deviceName>Headset</deviceName>
            <reason>Normal</reason>
            <responseCode>16</responseCode>
            <voiceQOS>
            <mediaIpSourceAddr>***********</mediaIpSourceAddr>
            <mediaIpDestAddr>***********</mediaIpDestAddr>
            <mediaUdpRtpSourcePort>5000</mediaUdpRtpSourcePort>
            <mediaUdpRtpDestPort>5000</mediaUdpRtpDestPort>
            <mediaNumOfIpPktRxed>1</mediaNumOfIpPktRxed>
            <mediaNumOfIpPktTxed>1</mediaNumOfIpPktTxed>
            <mediaNumOfIpErroredPktRxed>1</mediaNumOfIpErroredPktRxed>
            <mediaNumOfRtpPktRxed>1</mediaNumOfRtpPktRxed>
            <mediaNumOfRtpPktTxed>1</mediaNumOfRtpPktTxed>
            <mediaNumOfRtpPktLost>1</mediaNumOfRtpPktLost>
            <mediaNumOfRtpPktDiscarded>1</mediaNumOfRtpPktDiscarded>
            <mediaRtpJitter>1</mediaRtpJitter>
            <mediaRtpLatency>1</mediaRtpLatency>
            <mediaNumOfRtcpPktRxed>1</mediaNumOfRtcpPktRxed>
            <mediaNumOfRtcpPktTxed>1</mediaNumOfRtcpPktTxed>
            <mediaFarEndPacketLostPercentage>1</mediaFarEndPacketLostPercentage>
            <mediaFarEndCumulativePacketLost>1</mediaFarEndCumulativePacketLost>
            <mediaFarEndInterarrivalJitter>1</mediaFarEndInterarrivalJitter>
            </voiceQOS>
            </logout>
                         </LogEvent>
                         ");
            Assert.AreEqual("Logout", data.eventType);
            Assert.AreEqual("<EMAIL>", data.logout.MediaLabel);
            Assert.AreEqual("tel:+6432341234", data.logout.Uri);
            Assert.AreEqual("operator", data.logout.AgentRole);
            Assert.AreEqual("chicago.psap.il.us", data.logout.TenantGroup);
            Assert.AreEqual("001", data.logout.OperatorId);
            Assert.AreEqual("PC Host name", data.logout.Workstation);
            Assert.AreEqual("Headset", data.logout.DeviceName);
            Assert.AreEqual("Normal", data.logout.Reason);
            Assert.AreEqual("16", data.logout.ResponseCode);
            Assert.AreEqual("***********", data.logout.VoiceQOS.MediaIpSourceAddr);
            Assert.AreEqual("***********", data.logout.VoiceQOS.MediaIpDestAddr);
            Assert.AreEqual("5000", data.logout.VoiceQOS.MediaUdpRtpSourcePort);
            Assert.AreEqual("5000", data.logout.VoiceQOS.MediaUdpRtpDestPort);
            Assert.AreEqual("1", data.logout.VoiceQOS.MediaNumOfIpPktRxed);
            Assert.AreEqual("1", data.logout.VoiceQOS.MediaNumOfIpPktTxed);
            Assert.AreEqual("1", data.logout.VoiceQOS.MediaNumOfIpErroredPktRxed);
            Assert.AreEqual("1", data.logout.VoiceQOS.MediaNumOfRtcpPktRxed);
            Assert.AreEqual("1", data.logout.VoiceQOS.MediaNumOfRtcpPktTxed);
            Assert.AreEqual("1", data.logout.VoiceQOS.MediaNumOfRtpPktLost);
            Assert.AreEqual("1", data.logout.VoiceQOS.MediaNumOfRtpPktDiscarded);
            Assert.AreEqual("1", data.logout.VoiceQOS.MediaRtpJitter);
            Assert.AreEqual("1", data.logout.VoiceQOS.MediaRtpLatency);
            Assert.AreEqual("1", data.logout.VoiceQOS.MediaNumOfRtpPktRxed);
            Assert.AreEqual("1", data.logout.VoiceQOS.MediaNumOfRtpPktTxed);
            Assert.AreEqual("1", data.logout.VoiceQOS.MediaFarEndPacketLostPercentage);
            Assert.AreEqual("1", data.logout.VoiceQOS.MediaFarEndCumulativePacketLost);
            Assert.AreEqual("1", data.logout.VoiceQOS.MediaFarEndInterarrivalJitter);
        }

        [TestMethod]
        public void BusiedOut_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
            <eventType>AgentBusiedOut</eventType>
            <busiedOut>
            <mediaLabel><EMAIL></mediaLabel>
            <uri>tel:+6432341234</uri>
            <agentRole>operator</agentRole>
            <tenantGroup>chicago.psap.il.us</tenantGroup>
            <operatorId>001</operatorId>
            <workstation>PC Host name</workstation>
            <busiedOutAction>Manual</busiedOutAction>
            </busiedOut>
                         </LogEvent>
                         ");
            Assert.AreEqual("AgentBusiedOut", data.eventType);
            Assert.AreEqual("<EMAIL>", data.busiedOut.MediaLabel);
            Assert.AreEqual("tel:+6432341234", data.busiedOut.Uri);
            Assert.AreEqual("operator", data.busiedOut.AgentRole);
            Assert.AreEqual("chicago.psap.il.us", data.busiedOut.TenantGroup);
            Assert.AreEqual("001", data.busiedOut.OperatorId);
            Assert.AreEqual("PC Host name", data.busiedOut.Workstation);
            Assert.AreEqual("Manual", data.busiedOut.BusiedOutAction);
        }

        [TestMethod]
        public void AgentAvailable_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
            <eventType>AgentAvailable</eventType>
            <agentAvailable>
            <mediaLabel><EMAIL></mediaLabel>
            <uri>tel:+6432341234</uri>
            <agentRole>operator</agentRole>
            <tenantGroup>chicago.psap.il.us</tenantGroup>
            <operatorId>001</operatorId>
            <workstation>PC Host name</workstation>
            <busiedOutAction>Manual</busiedOutAction>
            </agentAvailable>
                         </LogEvent>
                         ");
            Assert.AreEqual("AgentAvailable", data.eventType);
            Assert.AreEqual("<EMAIL>", data.agentAvailable.MediaLabel);
            Assert.AreEqual("tel:+6432341234", data.agentAvailable.Uri);
            Assert.AreEqual("operator", data.agentAvailable.AgentRole);
            Assert.AreEqual("chicago.psap.il.us", data.agentAvailable.TenantGroup);
            Assert.AreEqual("001", data.agentAvailable.OperatorId);
            Assert.AreEqual("PC Host name", data.agentAvailable.Workstation);
            Assert.AreEqual("Manual", data.agentAvailable.BusiedOutAction);
        }

        [TestMethod]
        public void ACDLogin_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
            <eventType>ACDLogin</eventType>
            <acdLogin>
            <mediaLabel><EMAIL></mediaLabel>
            <agentUri>tel:+6432341234</agentUri>
            <agentRole>operator</agentRole>
            <tenantGroup>chicago.psap.il.us</tenantGroup>
            <operatorId>001</operatorId>
            <workstation>PC Host name</workstation>
            <deviceName>Headset</deviceName>
            <ringGroupName>911 Queue</ringGroupName>
            <ringGroupUri>tel:+6432341234</ringGroupUri>
            </acdLogin>
                         </LogEvent>
                         ");
            Assert.AreEqual("ACDLogin", data.eventType);
            Assert.AreEqual("<EMAIL>", data.acdLogin.MediaLabel);
            Assert.AreEqual("tel:+6432341234", data.acdLogin.AgentUri);
            Assert.AreEqual("operator", data.acdLogin.AgentRole);
            Assert.AreEqual("chicago.psap.il.us", data.acdLogin.TenantGroup);
            Assert.AreEqual("001", data.acdLogin.OperatorId);
            Assert.AreEqual("PC Host name", data.acdLogin.Workstation);
            Assert.AreEqual("Headset", data.acdLogin.DeviceName);
            Assert.AreEqual("911 Queue", data.acdLogin.RingGroupName);
            Assert.AreEqual("tel:+6432341234", data.acdLogin.RingGroupUri);
        }

        [TestMethod]
        public void ACDLogout_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
            <eventType>ACDLogout</eventType>
            <acdLogout>
            <mediaLabel><EMAIL></mediaLabel>
            <agentUri>tel:+6432341234</agentUri>
            <agentRole>operator</agentRole>
            <tenantGroup>chicago.psap.il.us</tenantGroup>
            <operatorId>001</operatorId>
            <workstation>PC Host name</workstation>
            <deviceName>Headset</deviceName>
            <ringGroupName>911 Queue</ringGroupName>
            <ringGroupUri>tel:+6432341234</ringGroupUri>
            </acdLogout>
                         </LogEvent>
                         ");
            Assert.AreEqual("ACDLogout", data.eventType);
            Assert.AreEqual("<EMAIL>", data.acdLogout.MediaLabel);
            Assert.AreEqual("tel:+6432341234", data.acdLogout.AgentUri);
            Assert.AreEqual("operator", data.acdLogout.AgentRole);
            Assert.AreEqual("chicago.psap.il.us", data.acdLogout.TenantGroup);
            Assert.AreEqual("001", data.acdLogout.OperatorId);
            Assert.AreEqual("PC Host name", data.acdLogout.Workstation);
            Assert.AreEqual("Headset", data.acdLogout.DeviceName);
            Assert.AreEqual("911 Queue", data.acdLogout.RingGroupName);
            Assert.AreEqual("tel:+6432341234", data.acdLogout.RingGroupUri);
        }

        [TestMethod]
        public void Message_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
            <eventType>Message</eventType>
            <message>
            <mediaLabel>_ML_114F67B7A08C00000000@ chicago.psap.il.us </mediaLabel>
            <direction>Out</direction>
            <messageType>SMS</messageType>
            <text>What is your emergency?</text>
            </message>
                         </LogEvent>
                         ");
            Assert.AreEqual("Message", data.eventType);
            Assert.AreEqual("_ML_114F67B7A08C00000000@ chicago.psap.il.us ", data.message.MediaLabel);
            Assert.AreEqual("Out", data.message.Direction);
            Assert.AreEqual("SMS", data.message.MessageType);
            Assert.AreEqual("What is your emergency?", data.message.Text);
        }

        [TestMethod]
        public void EIDD_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
            <eventType>EIDD</eventType>
            <EIDD>
            <EIDDBody>EIDD xml document here</EIDDBody>
            <direction>Out</direction>
            </EIDD>
                         </LogEvent>
                         ");
            Assert.AreEqual("EIDD", data.eventType);
            Assert.AreEqual("EIDD xml document here", data.EIDD.EIDDBody);
            Assert.AreEqual("Out", data.EIDD.Direction);
        }

        [TestMethod]
        public void ElementStateChange_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
            <eventType>ElementStateChange</eventType>
            <elementStateChange>
            <StateChangeNewStateValue>Unmanned</StateChangeNewStateValue>
            </elementStateChange>
                         </LogEvent>
                         ");
            Assert.AreEqual("ElementStateChange", data.eventType);
            Assert.AreEqual("Unmanned", data.elementStateChange.StateChangeNewStateValue);
        }

        [TestMethod]
        public void CDRType1_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
            <eventType>CDRType1</eventType>
            <cdrType1>
            <startTime>2015-08-21T12:58:03.01Z </startTime>
            <operatorId>1</operatorId>
            <ani>2470001234</ani>
            <presentedTime>2015-08-21T12:58:03.03Z </presentedTime>
            <answeredTime>2015-08-21T12:58:03.10Z </answeredTime>
            <jobNumber>101001</jobNumber>
            <transferTime>2015-08-21T12:58:03.30Z </transferTime>
            <transferAnswerTime>2015-08-21T12:58:03.35Z </transferAnswerTime>
            <disassociatedTime>2015-08-21T12:58:03.40Z </disassociatedTime>
            <transferTargetType>Workstation</transferTargetType>
            <transferTargetName> PC Host name </transferTargetName>
            <transferTarget>2471230012</transferTarget>
            <disconnectReason>.</disconnectReason>
            <ivrOutcome>.</ivrOutcome>
            <externalTransferAttempts>0</externalTransferAttempts>
            <dnis>911</dnis>
            <endTime>2015-08-21T12:58:04.10Z </endTime>
            </cdrType1>
                         </LogEvent>
                         ");
            Assert.AreEqual("CDRType1", data.eventType);
            Assert.AreEqual("2015-08-21T12:58:03.01Z ", data.cdrType1.StartTime);
            Assert.AreEqual("1", data.cdrType1.OperatorId);
            Assert.AreEqual("2470001234", data.cdrType1.Ani);
            Assert.AreEqual("2015-08-21T12:58:03.03Z ", data.cdrType1.PresentedTime);
            Assert.AreEqual("2015-08-21T12:58:03.10Z ", data.cdrType1.AnsweredTime);
            Assert.AreEqual("101001", data.cdrType1.JobNumber);
            Assert.AreEqual("2015-08-21T12:58:03.30Z ", data.cdrType1.TransferTime);
            Assert.AreEqual("2015-08-21T12:58:03.35Z ", data.cdrType1.TransferAnswerTime);
            Assert.AreEqual("2015-08-21T12:58:03.40Z ", data.cdrType1.DisassociatedTime);
            Assert.AreEqual("Workstation", data.cdrType1.TransferTargetType);
            Assert.AreEqual(" PC Host name ", data.cdrType1.TransferTargetName);
            Assert.AreEqual("2471230012", data.cdrType1.TransferTarget);
            Assert.AreEqual(".", data.cdrType1.DisconnectReason);
            Assert.AreEqual(".", data.cdrType1.IvrOutcome);
            Assert.AreEqual("0", data.cdrType1.ExternalTransferAttempts);
            Assert.AreEqual("911", data.cdrType1.Dnis);
            Assert.AreEqual("2015-08-21T12:58:04.10Z ", data.cdrType1.EndTime);
        }

        [TestMethod]
        public void QueueStateChange_Deserialized_Properly()
        {
            var data = XmlHelper.DeserializeXml<EventLog>(@"<LogEvent xmlns=""http://solacom.com/Logging"">
                           <timestamp>2015-08-21T12:58:03.01Z</timestamp>
       <agencyOrElement> esrp.state.pa.us </agencyOrElement>
       <agent>.</agent>
       <callIdentifier> <EMAIL> </callIdentifier>
          <incidentIdentifier> <EMAIL> </incidentIdentifier>
            <eventType>QueueStateChange</eventType>
            <QueueStateChange>
            <StateChangeNotificationContents>Count</StateChangeNotificationContents>
            <queueId>2470001234</queueId>
            <queueName>2470001234</queueName>
            <direction></direction>
            <count>1</count>
            </QueueStateChange>
                         </LogEvent>
                         ");
            Assert.AreEqual("QueueStateChange", data.eventType);
            Assert.AreEqual("Count", data.QueueStateChange.StateChangeNotificationContents);
            Assert.AreEqual("2470001234", data.QueueStateChange.QueueId);
            Assert.AreEqual("2470001234", data.QueueStateChange.QueueName);
            Assert.AreEqual("", data.QueueStateChange.Direction);
            Assert.AreEqual("1", data.QueueStateChange.Count);
        }

        /// <summary>
        /// Sample Canadian ALI Data for TELUS
        /// </summary>
        private string ALIDataExample_TELUS = @"<tns:Response timestamp=""2022-08-10T06:27:09Z"" xsi:schemaLocation=""urn:nena-org:dtc:aqstcp AQS.TCP.xsd""
    xmlns:nali=""http://www.nena9-1-1.org/schemas/2003/ali""
    xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance""
    xmlns:aqs=""urn:nena-org:dtc:aqs""
    xmlns:tns=""urn:nena-org:dtc:aqstcp"">
    <aqs:QueryResponse version=""4.4"" ID=""ALI01008004902345"">
        <aqs:Status>
            <aqs:StatusCode>urn:nena-org:dtc:aqs:status:Ok</aqs:StatusCode>
        </aqs:Status>
        <aqs:QueryProperties>
            <aqs:TrunkID>0006</aqs:TrunkID>
            <aqs:Extension ID=""TELUSQueryPropExtV1.0"">
                <!--TELUS Query Property Extension-->
                <tqpe:TELUSQueryPropertyExt xsi:schemaLocation=""TELUS_QP_Extn TELUS_Query_Property_Ext.xsd""
                    xmlns:tqpe=""TELUS_QP_Extn""
                    xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"">
                    <tqpe:CadPacketId>C</tqpe:CadPacketId>
                    <tqpe:AgentPosition>00001</tqpe:AgentPosition>
                    <tqpe:CallDateTime>2022-08-10T06:27:09Z</tqpe:CallDateTime>
                </tqpe:TELUSQueryPropertyExt>
            </aqs:Extension>
        </aqs:QueryProperties>
        <aqs:QueryResultData>
            <nali:ALIBody schemaVersion=""4.4"">
                <nali:CallInfo>
                    <nali:CallingPartyNum>7782112286</nali:CallingPartyNum>
                    <nali:MainTelNum>2507069244</nali:MainTelNum>
                </nali:CallInfo>
                <nali:LocationInfo>
                    <nali:StreetAddress>
                        <nali:HouseNum>3761</nali:HouseNum>
                        <nali:StreetName>WRIGHT STATION</nali:StreetName>
                        <nali:MSAGCommunity>LAC LA HACHE BC</nali:MSAGCommunity>
                        <nali:StateProvince>BC</nali:StateProvince>
                        <nali:Country>CA</nali:Country>
                    </nali:StreetAddress>
                    <nali:GeoLocation>
                        <nali:Latitude>51.857308</nali:Latitude>
                        <nali:Longitude>-121.637889</nali:Longitude>
                        <nali:Uncertainty>28</nali:Uncertainty>
                        <nali:Confidence>90</nali:Confidence>
                    </nali:GeoLocation>
                </nali:LocationInfo>
                <nali:Agencies>
                    <nali:Police>
                        <nali:Name>NORTH DISTRICT BC RCMP</nali:Name>
                        <nali:TN>2506124860</nali:TN>
                    </nali:Police>
                    <nali:Fire>
                        <nali:Name>PRINCE GEORGE FIRE</nali:Name>
                        <nali:TN>**********</nali:TN>
                    </nali:Fire>
                    <nali:EMS>
                        <nali:Name>KAMLOOPS AMBULANCE</nali:Name>
                        <nali:TN>**********</nali:TN>
                    </nali:EMS>
                    <nali:ESN>02075</nali:ESN>
                </nali:Agencies>
                <nali:SourceInfo>
                    <nali:AccessProvider>
                        <nali:AccessProviderID>TELMU</nali:AccessProviderID>
                        <nali:TN>**********</nali:TN>
                        <nali:Name>TELUS</nali:Name>
                    </nali:AccessProvider>
                </nali:SourceInfo>
                <nali:Extension name=""TELUSAliBodyExtV1.0"" source=""TELUS"">
                    <!--TELUS ALI Body Extension-->
                    <tabx:TELUSAliBodyExt xsi:schemaLocation=""TELUS_AB_Extn TELUS_ALI_Body_Ext.xsd""
                        xmlns:tabx=""TELUS_AB_Extn""
                        xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"">
                        <tabx:LocationInfo>
                            <tabx:GeoLocation>
                                <tabx:LocationResultCode>000</tabx:LocationResultCode>
                                <tabx:LocationResultText>OK</tabx:LocationResultText>
                            </tabx:GeoLocation>
                            <tabx:StreetAddress>
                                <tabx:StreetType>RD</tabx:StreetType>
                                <tabx:Community>CARIBOO RD (SOUTH)</tabx:Community>
                            </tabx:StreetAddress>
                            <tabx:CallInfo>
                                <tabx:ClassOfService>WL2</tabx:ClassOfService>
                                <tabx:CustomerName>BC0268 (LTE LAC LA HACHE)</tabx:CustomerName>
                            </tabx:CallInfo>
                        </tabx:LocationInfo>
                        <tabx:Agencies>
                            <tabx:PSAPNumber>008</tabx:PSAPNumber>
                        </tabx:Agencies>
                    </tabx:TELUSAliBodyExt>
                </nali:Extension>
            </nali:ALIBody>
        </aqs:QueryResultData>
    </aqs:QueryResponse>
</tns:Response>";

        /// <summary>
        /// Sample Canadian ALI data from BELL (Barrie)
        /// </summary>
        private string ALIDataExample_BELL = @"<Response xmlns:bcabex=""urn:bell-canada:ali:aqs:alibody:ext"" xmlns:ali=""http://www.nena9-1-1.org/schemas/2003/ali"" xmlns=""urn:nena-org:dtc:aqstcp""
xmlns:bcadex=""urn:bell-canada:ali:aqs:advisory:ext""
xmlns:aqs=""urn:nena-org:dtc:aqs""
xmlns:bcqpex=""urn:bell-canada:ali:aqs:queryproperties:ext"" timestamp=""2022-11-23T04:24:37.785Z"" >
<aqs:QueryResponse ID=""1731"">
    <aqs:Status>
        <aqs:StatusCode>urn:nena-org:dtc:aqs:status:OkMore</aqs:StatusCode>
	</aqs:Status>
	<aqs:QueryKey>
	    <aqs:NumericKey>
            <aqs:FirstPart>7057911718</aqs:FirstPart>
            <aqs:SecondPart>2492110235</aqs:SecondPart>
        </aqs:NumericKey>
    </aqs:QueryKey>
    <aqs:QueryProperties>
        <aqs:TrunkID>0</aqs:TrunkID>
        <aqs:CallTakerPosition>0</aqs:CallTakerPosition>
        <aqs:Extension>
            <bcqpex:BellQueryPropertiesExt>
                <bcqpex:CallId>29705791171800000007</bcqpex:CallId>
            </bcqpex:BellQueryPropertiesExt>
        </aqs:Extension>
    </aqs:QueryProperties>
    <aqs:QueryResultData>
    <ali:ALIBody schemaVersion=""4.2"">
        <ali:CallInfo>
            <ali:CallbackNum>7057911718</ali:CallbackNum>
            <ali:CallingPartyNum>2492110235</ali:CallingPartyNum>
        </ali:CallInfo>
        <ali:LocationInfo>
            <ali:StreetAddress>
                <ali:HouseNum>112</ali:HouseNum>
                <ali:StreetSuffix>ST</ali:StreetSuffix>
                <ali:MSAGCommunity>BARRIE</ali:MSAGCommunity>
                <ali:StateProvince>ON</ali:StateProvince>
                <ali:PostalZipCode>NA</ali:PostalZipCode>
            </ali:StreetAddress>
            <ali:GeoLocation>
                <ali:Latitude>44.346298</ali:Latitude>
                <ali:Longitude>-79.687754</ali:Longitude>
                <ali:Uncertainty>12</ali:Uncertainty>
                <ali:Confidence>90</ali:Confidence>
                <ali:DateStamp>2022-11-23T04:24:32.000Z</ali:DateStamp>
                <ali:LocationDescription>LAT:44 20 46.671N LONG:079 41 15.914W UNC:12 CONF:90</ali:LocationDescription>
            </ali:GeoLocation>
        </ali:LocationInfo>
        <ali:Agencies>
            <ali:Police>
                <ali:Name>BARRIE_POL</ali:Name>
                <ali:TN>7057285588</ali:TN>
            </ali:Police>
            <ali:Fire>
                <ali:Name>BARIFIR</ali:Name>
                <ali:TN>7057283131</ali:TN>
            </ali:Fire>
            <ali:EMS>
                <ali:Name>GEORGIANAMB</ali:Name>
                <ali:TN>7057268103</ali:TN>
            </ali:EMS>
            <ali:OtherAgencies>
                <ali:Agency>
                    <ali:Name>N/A</ali:Name>
                    <ali:TN>**********</ali:TN>
                </ali:Agency>
                <ali:Agency>
                    <ali:Name>N/A</ali:Name>
                    <ali:TN>**********</ali:TN>
                </ali:Agency>
                <ali:Agency>
                    <ali:Name>N/A</ali:Name>
                    <ali:TN>**********</ali:TN>
                </ali:Agency>
            </ali:OtherAgencies>
            <ali:ESN>00112</ali:ESN>
        </ali:Agencies>
        <ali:SourceInfo>
            <ali:DataProvider>
                <ali:DataProviderID>ROGER</ali:DataProviderID>
                <ali:TN>**********</ali:TN>
                <ali:Name>ROGERS WIRELESS</ali:Name>
            </ali:DataProvider>
            <ali:AccessProvider>
                <ali:AccessProviderID>ROGER</ali:AccessProviderID>
                <ali:TN>**********</ali:TN>
                <ali:Name>ROGERS WIRELESS</ali:Name>
            </ali:AccessProvider>
            <ali:ALIUpdateGMT>2020-04-02T16:36:03.000Z</ali:ALIUpdateGMT>
            <ali:ALIRetrievalGMT>2022-11-23T04:24:23.107Z</ali:ALIRetrievalGMT>
        </ali:SourceInfo>
        <ali:NetworkInfo>
            <ali:PSAPID>0007</ali:PSAPID>
            <ali:CLLI>29</ali:CLLI>
        </ali:NetworkInfo>
        <ali:Extension name=""Bell ALI Body Extension"" source=""urn:bell-canada:ali:aqs:alibody:ext"" version=""1.4"">
            <bcabex:BellAliBodyExt>
                <bcabex:LocationInfo>
                    <bcabex:StreetAddress>
                        <bcabex:StreetName>CELLULAR</bcabex:StreetName>
                        <bcabex:PostalCommunity>BARRIE</bcabex:PostalCommunity>
                        <bcabex:MSAGCommunityCode>BAR</bcabex:MSAGCommunityCode>
                    </bcabex:StreetAddress>
                    <bcabex:GeoLocation>
                        <bcabex:ResultDateTime>2022-11-23T04:24:32.000Z</bcabex:ResultDateTime>
                    </bcabex:GeoLocation>
                </bcabex:LocationInfo>
                <bcabex:CallInfo>
                    <bcabex:CustomerName>(HWY 400 &amp; FAIRVIEW RD, BARRIE, ON, BARRIE) L N69JX7 ROGERS WIRELESS</bcabex:CustomerName>
                    <bcabex:ClassOfService>WL2</bcabex:ClassOfService>
                    <bcabex:PSAPAnswerDateTime>2022-11-23T04:24:37.781Z</bcabex:PSAPAnswerDateTime>
                    <bcabex:TransferringPSAPName>NRBAYOPPBUR</bcabex:TransferringPSAPName>
                    <bcabex:TransferringPSAPConferenceDuration>P0Y0M0DT0H0M14.639S</bcabex:TransferringPSAPConferenceDuration>
                </bcabex:CallInfo>
            </bcabex:BellAliBodyExt>
        </ali:Extension>
    </ali:ALIBody>
</aqs:QueryResultData>
</aqs:QueryResponse>
</Response>";

        [TestMethod]
        public void ALIDataXml_ALIXmlFetch_TELUS()
        {
            var xmlData = ALIDataExample_TELUS;

            ALIRawData aliData = ALIHelper.parseALIBody(xmlData);

            Assert.IsTrue(aliData.MainTelNum == "2507069244");
            Assert.IsTrue(aliData.TELUSExtension.ClassOfService == "WL2");
        }

        [TestMethod]
        public void ALIDataXml_ALIXmlFetch_BELL()
        {
            var xmlData = ALIDataExample_BELL;

            ALIRawData aliData = ALIHelper.parseALIBody(xmlData);

            Assert.IsTrue(aliData.CallBackNum == "7057911718");
            Assert.IsTrue(aliData.BELLExtension.ClassOfService == "WL2");
        }

        #region HELDresponse (location) tests

        /// <summary>
        /// Sample HELD Response XML
        /// </summary>
        private string EventLogExample_HELDResponse_Template = @"<LogEvent xmlns=""http://solacom.com/Logging\"">
                                                            <timestamp>2023-02-06T14:11:41.533Z</timestamp>
                                                            <agencyOrElement>PORSCHE-A_A</agencyOrElement>
                                                            <agent>.</agent>
                                                            <callIdentifier>_CI_18627105C08C000011D1 @PORSCHE-A</callIdentifier>
                                                            <incidentIdentifier>_II_18627105C08C000011D1 @PORSCHE-A</incidentIdentifier>
                                                            <eventType>HELDresponse</eventType>
                                                            <heldResponse>
                                                                    <mediaLabel>_ML_18627105C08C000011D1 @PORSCHE-A</mediaLabel>
                                                                    <heldDomain>************</heldDomain>
                                                                    <responseCode>200</responseCode>
                                                                    <held><locationResponse xmlns=""urn:ietf:params:xml:ns:geopriv:held"" xmlns:ca=""urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr"" xmlns:gml=""http://www.opengis.net/gml"" xmlns:ns4=""urn:ietf:params:xml:ns:pidf"" xmlns:ns5=""urn:ietf:params:xml:ns:pidf:data-model"" xmlns:ns6=""urn:ietf:params:xml:ns:pidf:geopriv10"" xmlns:ns7=""urn:ietf:params:xml:ns:pidf:geopriv10:basicPolicy"" xmlns:ns8=""http://www.opengis.net/pidflo/1.0"" xmlns:ns9=""urn:ietf:params:xml:ns:geopriv:held:id"" >
                                                                <locationUriSet expires=""2022-09-27T15:05:21"" xmlns:xsd=""http://www.w3.org/2001/XMLSchema"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"" >
                                                                    <locationUri>https://ls.example.com:9768/357yc6s64ceyoiuy5ax3o</locationUri>
                                                                    <locationUri>sip:9769+357yc6s64ceyoiuy5ax3o @ls.example.com:</locationUri>
                                                                </locationUriSet>
                                                                <presence entity=""pres:<EMAIL>"" xmlns=""urn:ietf:params:xml:ns:pidf"" xmlns:xsd=""http://www.w3.org/2001/XMLSchema"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"" >
                                                                    <tuple id=""lisLocation"">
                                                                        <status>
                                                                            <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                                                <location-info>
                                                                                    {0}
                                                                                </location-info>
                                                                                <usage-rules>
                                                                                    <retransmission-allowed>true</retransmission-allowed>
                                                                                    <retention-expiry>2022-09-27T15:05:21</retention-expiry>
                                                                                </usage-rules>
                                                                                <provided-by>
                                                                                    {1}
                                                                                </provided-by>
                                                                                <method>Cell123</method>
                                                                            </geopriv>
                                                                        </status>
                                                                        <timestamp>2022-09-27T15:05:21</timestamp>
                                                                        <contact>5552223333</contact>
                                                                    </tuple>
                                                                </presence>
                                                            </locationResponse></held>
                                                            </heldResponse>
                                                    </LogEvent>";

        [TestMethod]
        public void EventLog_HELD_CivicAddress()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();
            

            string innerStructure = @"<ca:civicAddress xml:lang=""en-us"" xmlns:ca=""urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr"">
                                        <ca:country>AU</ca:country>
                                        <ca:A1>NSW</ca:A1>
                                        <ca:A2>354</ca:A2>
                                        <ca:A3>Wollongon</ca:A3>
                                        <ca:A4>Gwynneville</ca:A4>
                                        <ca:STS>Northfield Avenue</ca:STS>
                                        <ca:LMK>University of Wollongong</ca:LMK>
                                        <ca:NAM>Andrew Corporation sant..</ca:NAM>
                                        <ca:PC>23445</ca:PC>
                                        <ca:BLD>12</ca:BLD>
                                        <ca:FLR>2</ca:FLR>
                                        <ca:POBOX>U40</ca:POBOX>
                                        <ca:SEAT>WS-183</ca:SEAT>
                                        <ca:RD>Raccoon Valley</ca:RD>
                                    </ca:civicAddress>";
            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Template, innerStructure, string.Empty));

            Assert.IsTrue(eventLog.heldResponse.LocationData.LocationEventSource == EventReceiver.BusinessLogic.Enums.LocationEventSource.HELD);
            Assert.IsTrue(eventLog.heldResponse.LocationData.CivicAddress.Country == "AU");
            Assert.IsTrue(eventLog.heldResponse.LocationData.CivicAddress.POBOX == "U40");


        }

        [TestMethod]
        public void EventLog_Point_Parsing()
        {
            string testString = "28.113055 -81.64747 10";

            Entities.Point pt = Entities.Point.ParsePointString(testString);

            Assert.IsTrue(pt.Latitude == 28.113055);
            Assert.IsTrue(pt.Longitude == -81.64747);
            Assert.IsTrue(pt.Altitude == 10);
        }

        [TestMethod]
        public void EventLog_Polygon_Parsing()
        {
            List<string> testString = new List<string>() { "28.113055 -81.64747 10", "28.113055 -81.64747 10", "28.113055 -81.64747 10", "28.113055 -81.64747 10" };

            Entities.Polygon polygon = new Entities.Polygon();
            polygon.PointList = Entities.Polygon.ProcessStringToPolygon(testString);

            Assert.IsTrue(polygon.PointList[0].Latitude == 28.113055);
            Assert.IsTrue(polygon.PointList[0].Longitude == -81.64747);
            Assert.IsTrue(polygon.PointList[0].Altitude == 10);
        }

        [TestMethod]
        public void EventLog_HELD_Point()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();


            string innerStructure = @"<gml:Point srsName=""urn:ogc:def:crs:EPSG::4326"">
                                        <gml:pos>28.113055 -81.64747</gml:pos>
                                    </gml:Point>";
            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Template, innerStructure, ""));

            Assert.IsTrue(eventLog.heldResponse.LocationData.Point.POS == "28.113055 -81.64747");
        }

        [TestMethod]
        public void EventLog_HELD_Ellipsoid()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();


            string innerStructure = @"<ns8:Ellipsoid srsName=""urn:ogc:def:crs:EPSG::4979"">
                                            <gml:pos>35.360608 -91.781256 123</gml:pos>
                                            <ns8:semiMajorAxis>11.1</ns8:semiMajorAxis>
                                            <ns8:semiMinorAxis>22.2</ns8:semiMinorAxis>
                                            <ns8:verticalAxis>33.3</ns8:verticalAxis>
                                            <ns8:orientation>44.4</ns8:orientation>
                                    </ns8:Ellipsoid>";
            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Template, innerStructure, ""));

            Assert.IsTrue(eventLog.heldResponse.LocationData.Ellipsoid.SemiMajorAxis == "11.1");
            Assert.IsTrue(eventLog.heldResponse.LocationData.Ellipsoid.Orientation == "44.4");
        }

        [TestMethod]
        public void EventLog_HELD_Circle()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();


            string innerStructure = @"<ns8:Circle srsName=""urn:ogc:def:crs:EPSG::4326"">
                                          <gml:pos>-34.410649 150.87651</gml:pos>
                                          <ns8:radius uom=""urn:ogc:def:uom:EPSG::9001"">30</ns8:radius>
                                        </ns8:Circle>";
            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Template, innerStructure, ""));

            Assert.IsTrue(eventLog.heldResponse.LocationData.Circle.POS == "-34.410649 150.87651");
            Assert.IsTrue(eventLog.heldResponse.LocationData.Circle.Radius == "30");
        }

        [TestMethod]
        public void EventLog_HELD_Ellipse()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();


            string innerStructure = @"<ns8:Ellipse srsName=""urn:ogc:def:crs:EPSG::4326"">
                                        <gml:pos>42.5463 -73.2512</gml:pos>
                                        <ns8:semiMajorAxis uom=""urn:ogc:def:uom:EPSG::9001"">1275</ns8:semiMajorAxis>
                                        <ns8:semiMinorAxis uom=""urn:ogc:def:uom:EPSG::9001"">670</ns8:semiMinorAxis>
                                        <ns8:orientation uom=""urn:ogc:def:uom:EPSG::9102"">43.2</ns8:orientation>
                                    </ns8:Ellipse>";
            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Template, innerStructure, ""));

            Assert.IsTrue(eventLog.heldResponse.LocationData.Ellipse.SemiMajorAxis == "1275");
            Assert.IsTrue(eventLog.heldResponse.LocationData.Ellipse.Orientation == "43.2");
        }

        [TestMethod]
        public void EventLog_HELD_ArcBand()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();


            string innerStructure = @"<ns8:ArcBand srsName=""urn:ogc:def:crs:EPSG::4326"">
                                        <gml:pos>-43.5723 153.21760</gml:pos>
                                        <ns8:innerRadius uom=""urn:ogc:def:uom:EPSG::9001"">3594</ns8:innerRadius>
                                        <ns8:outerRadius uom=""urn:ogc:def:uom:EPSG::9001"">4148</ns8:outerRadius>
                                        <ns8:startAngle uom=""urn:ogc:def:uom:EPSG::9102"">20</ns8:startAngle>
                                        <ns8:openingAngle uom=""urn:ogc:def:uom:EPSG::9102"">20</ns8:openingAngle>
                                      </ns8:ArcBand>";
            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Template, innerStructure, ""));

            Assert.IsTrue(eventLog.heldResponse.LocationData.ArcBand.InnerRadius == "3594");
            Assert.IsTrue(eventLog.heldResponse.LocationData.ArcBand.StartAngle == "20");
            Assert.IsTrue(eventLog.heldResponse.LocationData.ArcBand.OpeningAngle == "20");
        }

        [TestMethod]
        public void EventLog_HELD_Prism()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();


            string innerStructure = @"<ns8:Prism srsName=""urn:ogc:def:crs:EPSG::4979"">
                                        <ns8:base>
                                          <gml:Polygon>
                                            <gml:exterior>
                                              <gml:LinearRing>
                                                <gml:posList>
                                                  42.556844 -73.248157 36.6 
                                                  42.656844 -73.248157 36.6 
                                                  42.656844 -73.348157 36.6 
                                                  42.556844 -73.348157 36.6 
                                                  42.556844 -73.248157 36.6 
                                                </gml:posList>
                                              </gml:LinearRing>
                                            </gml:exterior>
                                          </gml:Polygon>
                                        </ns8:base>
                                        <ns8:height uom=""urn:ogc:def:uom:EPSG::9001"">2.4</ns8:height>
                                      </ns8:Prism>";
            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Template, innerStructure, ""));

            Assert.IsTrue(eventLog.heldResponse.LocationData.Prism.Height == "2.4");
            Assert.IsTrue(eventLog.heldResponse.LocationData.Prism.ExteriorPointCollection[0] == "42.556844 -73.248157 36.6");
            Assert.IsTrue(eventLog.heldResponse.LocationData.Prism.ExteriorPointCollection[3] == "42.556844 -73.348157 36.6");
        }

        [TestMethod]
        public void EventLog_HELD_Prism_Ignores_Inner_Polygon()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();

            string inner = @"
                <ns8:Prism srsName=""urn:ogc:def:crs:EPSG::4979"">
                    <ns8:base>
                        <gml:Polygon><gml:exterior> ... </gml:exterior></gml:Polygon>
                    </ns8:base>
                    <ns8:height>5.0</ns8:height>
                </ns8:Prism>";

            locationHandler.PopulateEventLogByHELD(
                eventLog,
                string.Format(EventLogExample_HELDResponse_Template, inner, "")
            );

            // Assert – Prism parsed
            Assert.IsNotNull(eventLog.heldResponse.LocationData.Prism);
            Assert.AreEqual("5.0", eventLog.heldResponse.LocationData.Prism.Height);

            // Assert – Polygon ignored
            Assert.IsNull(eventLog.heldResponse.LocationData.Polygon);
        }

        [TestMethod]
        public void EventLog_HELD_Sphere()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();


            string innerStructure = @"<ns8:Sphere srsName=""urn:ogc:def:crs:EPSG::4979"">
                                        <gml:pos>42.5463 -73.2512 26.3</gml:pos>
                                        <ns8:radius uom=""urn:ogc:def:uom:EPSG::9001"">850.24</ns8:radius>
                                      </ns8:Sphere>";
            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Template, innerStructure, ""));

            Assert.IsTrue(eventLog.heldResponse.LocationData.Sphere.POS == "42.5463 -73.2512 26.3");
            Assert.IsTrue(eventLog.heldResponse.LocationData.Sphere.Radius == "850.24");
            
        }

        [TestMethod]
        public void EventLog_HELD_Polygon()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();


            string innerStructure = @"<gml:Polygon srsName=""urn:ogc:def:crs:EPSG::4326"">
                                          <gml:exterior>
                                            <gml:LinearRing>
                                              <gml:pos>43.311 -73.422</gml:pos> 
                                              <gml:pos>43.111 -73.322</gml:pos> 
                                              <gml:pos>43.111 -73.222</gml:pos> 
                                              <gml:pos>43.311 -73.122</gml:pos> 
                                              <gml:pos>43.411 -73.222</gml:pos> 
                                              <gml:pos>43.411 -73.322</gml:pos> 
                                              <gml:pos>43.311 -73.422</gml:pos> 
                                            </gml:LinearRing>
                                          </gml:exterior>
                                        </gml:Polygon>";
            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Template, innerStructure, ""));

            Assert.IsTrue(eventLog.heldResponse.LocationData.Polygon.ExteriorPointCollection[0] == "43.311 -73.422");
            Assert.IsTrue(eventLog.heldResponse.LocationData.Polygon.ExteriorPointCollection[3] == "43.311 -73.122");
        }

        [TestMethod]
        public void EventLog_HELD_Confidence()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();


            string innerStructure = @"<confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">18</confidence>";
            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Template, innerStructure, ""));

            Assert.IsTrue(eventLog.heldResponse.LocationData.Confidence.Text == "18");
        }

        [TestMethod]
        public void EventLog_HELD_ServiceAndMethod()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();

            //Note: live data this node is expected under the EIDD event - however, parsing is still performed under the HELD event, so still a valid test of the sub node parsing
            string innerStructure = @"<EmergencyCallDataValue xmlns=""urn:ietf:params:xml:ns:EmergencyCallData"">
                                        <EmergencyCallData.ServiceInfo xmlns=""urn:ietf:params:xml:ns:EmergencyCallData:ServiceInfo"" xmlns:default=""en-US"">
                                        <DataProviderReference/>
                                        <ServiceEnvironment>Unknown</ServiceEnvironment>
                                        <ServiceType>wireless</ServiceType>
                                        <ServiceMobility>Mobile</ServiceMobility>
                                        <default:legacy_class_of_service xmlns=""en-US"">8</default:legacy_class_of_service>
                                        </EmergencyCallData.ServiceInfo>
                                    </EmergencyCallDataValue>";

            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Template, "", innerStructure));

            Assert.IsTrue(eventLog.heldResponse.LocationData.ServiceInfo.ServiceMobility == "Mobile");
            Assert.IsTrue(eventLog.heldResponse.LocationData.Method == "Cell123");
        }

        [TestMethod]
        public void EventLog_HELD_DeviceInfo()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();


            //Note: live data this node is expected under the EIDD event - however, parsing is still performed under the HELD event, so still a valid test of the sub node parsing
            string innerStructure = @"<EmergencyCallData.DeviceInfo xmlns=""urn:ietf:params:xml:ns:EmergencyCallData:DeviceInfo"">
                                        <DataProviderReference>urn:nena:NGA</DataProviderReference>
                                        <DeviceClassification>NA</DeviceClassification>
                                        <DeviceMfgr>NA</DeviceMfgr>
                                        <DeviceModelNr>NA</DeviceModelNr>
                                        <UniqueDeviceID TypeOfDeviceID=""NA"">NA</UniqueDeviceID>
                                    </EmergencyCallData.DeviceInfo>";

            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Template, "", innerStructure));

            Assert.IsTrue(eventLog.heldResponse.LocationData.DeviceInfo.DeviceMfgr == "NA");
            Assert.IsTrue(eventLog.heldResponse.LocationData.DeviceInfo.TypeOfDeviceID == "NA");
        }

        [TestMethod]
        public void EventLog_HELD_ProviderInfo()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();

            //Note: live data this node is expected under the EIDD event - however, parsing is still performed under the HELD event, so still a valid test of the sub node parsing
            string innerStructure = @"<EmergencyCallData.ProviderInfo xmlns=""urn:ietf:params:xml:ns:EmergencyCallData:ProviderInfo"">
                                        <DataProviderReference>urn:nena:NGA</DataProviderReference>
                                        <DataProviderString>NGA</DataProviderString>
                                        <ProviderID>urn:nena:companyid:NGA</ProviderID>
                                        <ProviderIDSeries>NENA</ProviderIDSeries>
                                        <TypeOfProvider>Emergency Service Provider</TypeOfProvider>
                                        <ContactURI>tel:</ContactURI>
                                        <Language>en</Language>
                                    </EmergencyCallData.ProviderInfo>";

            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Template, "", innerStructure));

            Assert.IsTrue(eventLog.heldResponse.LocationData.ProviderInfo.DataProviderString == "NGA");
            Assert.IsTrue(eventLog.heldResponse.LocationData.ProviderInfo.Language == "en");
        }

        #endregion

        #region Heirarchy Location Tests


        private string EventLogExample_HELDResponse_Heirarchy = @"<LogEvent xmlns=""http://solacom.com/Logging\"">
                                                                        <timestamp>2023-02-06T14:11:41.533Z</timestamp>
                                                                        <agencyOrElement>PORSCHE-A_A</agencyOrElement>
                                                                        <agent>.</agent>
                                                                        <callIdentifier>_CI_18627105C08C000011D1 @PORSCHE-A</callIdentifier>
                                                                        <incidentIdentifier>_II_18627105C08C000011D1 @PORSCHE-A</incidentIdentifier>
                                                                        <eventType>HELDresponse</eventType>
                                                                        <heldResponse>
                                                                                <mediaLabel>_ML_18627105C08C000011D1 @PORSCHE-A</mediaLabel>
                                                                                <heldDomain>************</heldDomain>
                                                                                <responseCode>200</responseCode>
                                                                        {0}
                                                                        </heldResponse>
                                                                </LogEvent>";

        private string paramCivicAddress = @"<ca:civicAddress xml:lang=""en-us"">
                                                <ca:country>Canada_{0}</ca:country>
                                                <ca:A1>A1_{0}</ca:A1>
                                                <ca:A2>A2_{0}</ca:A2>
                                                <ca:A3>A3_{0}</ca:A3>
                                                <ca:A4>A4_{0}</ca:A4>
                                                <ca:A5>A5_{0}</ca:A5>
                                                <ca:A6>A6_{0}</ca:A6>
                                                <ca:PRD>PRD_{0}</ca:PRD>
                                                <ca:POD>POD_{0}</ca:POD>
                                                <ca:STS>STS_{0}</ca:STS>
                                                <ca:HNO>11111_{0}</ca:HNO>
                                                <ca:HNS>HNS_{0}</ca:HNS>
                                                <ca:LMK>LMK_{0}</ca:LMK>
                                                <ca:LOC>LOC_{0}</ca:LOC>
                                                <ca:NAM>NAM_{0}</ca:NAM>
                                                <ca:PC>PC_{0}</ca:PC>
                                                <ca:BLD>BLD_{0}</ca:BLD>
                                                <ca:UNIT>UNIT_{0}</ca:UNIT>
                                                <ca:FLR>FLR_{0}</ca:FLR>
                                                <ca:ROOM>ROOM_{0}</ca:ROOM>
                                                <ca:PLC>PLC_{0}</ca:PLC>
                                                <ca:PCN>PCN_{0}</ca:PCN>
                                                <ca:POBOX>POBOX_{0}</ca:POBOX>
                                                <ca:ADDCODE>ADDCODE_{0}</ca:ADDCODE>
                                                <ca:SEAT>SEAT_{0}</ca:SEAT>
                                                <ca:RD>RD_{0}</ca:RD>
                                                <ca:RDSEC>RDSEC_{0}</ca:RDSEC>
                                                <ca:RDBR>RDBR_{0}</ca:RDBR>
                                                <ca:RDSUBBR>RDSUBBR_{0}</ca:RDSUBBR>
                                                <ca:PRM>PRM_{0}</ca:PRM>
                                                <ca:POM>POM_{0}</ca:POM>
                                            </ca:civicAddress>";

        [TestMethod]
        public void EventLog_HELD_Heirarchy_CivicAddress_DEVICE()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();

            string device1 = string.Format(paramCivicAddress, "device1");
            string device2 = string.Format(paramCivicAddress, "device2");
            string tuple1 = string.Format(paramCivicAddress, "tuple1");
            string tuple2 = string.Format(paramCivicAddress, "tuple2");
            string person1 = string.Format(paramCivicAddress, "person1");
            string person2 = string.Format(paramCivicAddress, "person2");

            string innerstructure = @$"<held xmlns=""http://solacom.com/Logging"">
                                          <locationResponse xmlns=""urn:ietf:params:xml:ns:geopriv:held"">
                                            <presence xmlns=""urn:ietf:params:xml:ns:pidf"" xmlns:ca=""urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr"" xmlns:gml=""http://www.opengis.net/gml"" xmlns:ns4=""urn:ietf:params:xml:ns:pidf"" xmlns:ns5=""urn:ietf:params:xml:ns:pidf:data-model"" xmlns:ns6=""urn:ietf:params:xml:ns:pidf:geopriv10"" xmlns:ns7=""urn:ietf:params:xml:ns:pidf:geopriv10:basicPolicy"" xmlns:ns8=""http://www.opengis.net/pidflo/1.0"" xmlns:ns9=""urn:ietf:params:xml:ns:geopriv:held:id"" xmlns:xsd=""http://www.w3.org/2001/XMLSchema"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"" entity=""pres:<EMAIL>"">
                                                <tuple id=""tuple1"">
                                                    <status>
                                                      <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                        <location-info>
                                                          {tuple1}
                                                          <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                        </location-info>
                                                        <usage-rules>
                                                          <retransmission-allowed>true</retransmission-allowed>
                                                          <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                        </usage-rules>
                                                        <provided-by>provided-by</provided-by>
                                                        <method>tuple1MethodCivic</method>
                                                      </geopriv>
                                                    </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>tuple1ContactCivic</contact>
                                              </tuple>
                                              <tuple id=""tuple2"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                      {tuple2}
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>tuple2MethodGeo</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>tuple2ContactGeo</contact>
                                              </tuple>
                                              <device id=""device1"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                      {device1}
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>device1MethodCivic</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>device1ContactCivic</contact>
                                              </device>
                                              <device id=""device2"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                      {device2}
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>device2MethodGeo</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>device2ContactGeo</contact>
                                              </device>
                                              
                                              <person id=""person1"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                      {person1}
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>person1MethodCivic</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>person1ContactCivic</contact>
                                              </person>
                                              <person id=""person2"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                      {person2}
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>person2MethodGeo</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>person2ContactGeo</contact>
                                              </person>
                                            </presence>
                                          </locationResponse>
                                        </held>";

            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Heirarchy, innerstructure));

            Assert.IsTrue(eventLog.heldResponse.LocationData.CivicAddress.LocationSource == EventReceiver.BusinessLogic.Enums.LocationSource.device);
            Assert.IsTrue(eventLog.heldResponse.LocationData.CivicAddress.Country == "Canada_device1");
            Assert.IsTrue(eventLog.heldResponse.LocationData.CivicAddress.POBOX == "POBOX_device1");
        }

        [TestMethod]
        public void EventLog_HELD_Heirarchy_CivicAddress_TUPLE()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();

            string tuple1 = string.Format(paramCivicAddress, "tuple1");
            string tuple2 = string.Format(paramCivicAddress, "tuple2");
            string person1 = string.Format(paramCivicAddress, "person1");
            string person2 = string.Format(paramCivicAddress, "person2");

            string innerstructure = @$"<held xmlns=""http://solacom.com/Logging"">
                                          <locationResponse xmlns=""urn:ietf:params:xml:ns:geopriv:held"">
                                            <presence xmlns=""urn:ietf:params:xml:ns:pidf"" xmlns:ca=""urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr"" xmlns:gml=""http://www.opengis.net/gml"" xmlns:ns4=""urn:ietf:params:xml:ns:pidf"" xmlns:ns5=""urn:ietf:params:xml:ns:pidf:data-model"" xmlns:ns6=""urn:ietf:params:xml:ns:pidf:geopriv10"" xmlns:ns7=""urn:ietf:params:xml:ns:pidf:geopriv10:basicPolicy"" xmlns:ns8=""http://www.opengis.net/pidflo/1.0"" xmlns:ns9=""urn:ietf:params:xml:ns:geopriv:held:id"" xmlns:xsd=""http://www.w3.org/2001/XMLSchema"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"" entity=""pres:<EMAIL>"">
                                            <person id=""person1"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                      {person1}
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>person1MethodCivic</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>person1ContactCivic</contact>
                                              </person>
                                              <person id=""person2"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                      {person2}
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>person2MethodGeo</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>person2ContactGeo</contact>
                                              </person>
                                              <tuple id=""tuple1"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                      {tuple1}
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>tuple1MethodCivic</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>tuple1ContactCivic</contact>
                                              </tuple>
                                              <tuple id=""tuple2"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                      {tuple2}
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>tuple2MethodGeo</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>tuple2ContactGeo</contact>
                                              </tuple>
                                            </presence>
                                          </locationResponse>
                                        </held>";

            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Heirarchy, innerstructure));

            Assert.IsTrue(eventLog.heldResponse.LocationData.CivicAddress.LocationSource == EventReceiver.BusinessLogic.Enums.LocationSource.tuple);
            Assert.IsTrue(eventLog.heldResponse.LocationData.CivicAddress.Country == "Canada_tuple1");
            Assert.IsTrue(eventLog.heldResponse.LocationData.CivicAddress.POBOX == "POBOX_tuple1");
        }

        [TestMethod]
        public void EventLog_HELD_Heirarchy_CivicAddress_Person()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();

            string person1 = string.Format(paramCivicAddress, "person1");
            string person2 = string.Format(paramCivicAddress, "person2");

            string innerstructure = @$"<held xmlns=""http://solacom.com/Logging"">
                                          <locationResponse xmlns=""urn:ietf:params:xml:ns:geopriv:held"">
                                            <presence xmlns=""urn:ietf:params:xml:ns:pidf"" xmlns:ca=""urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr"" xmlns:gml=""http://www.opengis.net/gml"" xmlns:ns4=""urn:ietf:params:xml:ns:pidf"" xmlns:ns5=""urn:ietf:params:xml:ns:pidf:data-model"" xmlns:ns6=""urn:ietf:params:xml:ns:pidf:geopriv10"" xmlns:ns7=""urn:ietf:params:xml:ns:pidf:geopriv10:basicPolicy"" xmlns:ns8=""http://www.opengis.net/pidflo/1.0"" xmlns:ns9=""urn:ietf:params:xml:ns:geopriv:held:id"" xmlns:xsd=""http://www.w3.org/2001/XMLSchema"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"" entity=""pres:<EMAIL>"">
                                              <person id=""person1"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                      {person1}
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>person1MethodCivic</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>person1ContactCivic</contact>
                                              </person>
                                              <person id=""person2"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                      {person2}
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>person2MethodGeo</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>person2ContactGeo</contact>
                                              </person>
                                            </presence>
                                          </locationResponse>
                                        </held>";

            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Heirarchy, innerstructure));

            Assert.IsTrue(eventLog.heldResponse.LocationData.CivicAddress.LocationSource == EventReceiver.BusinessLogic.Enums.LocationSource.person);
            Assert.IsTrue(eventLog.heldResponse.LocationData.CivicAddress.Country == "Canada_person1");
            Assert.IsTrue(eventLog.heldResponse.LocationData.CivicAddress.POBOX == "POBOX_person1");
        }

        private string paramPoint = @"<gml:Point srsName=""urn:ogc:def:crs:EPSG::4326"">
                                        <gml:pos>{0}</gml:pos>
                                      </gml:Point>";
        
        [TestMethod]
        public void EventLog_HELD_Heirarchy_Point_DEVICE()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();

            string device1 = string.Format(paramPoint, "1.11 -22.22");
            string device2 = string.Format(paramPoint, "2.11 -22.22");
            string tuple1 = string.Format(paramPoint, "10.11 -22.22");
            string tuple2 = string.Format(paramPoint, "20.11 -22.22");
            string person1 = string.Format(paramPoint, "100.11 -22.22");
            string person2 = string.Format(paramPoint, "200.11 -22.22");

            string innerstructure = @$"<held xmlns=""http://solacom.com/Logging"">
                                          <locationResponse xmlns=""urn:ietf:params:xml:ns:geopriv:held"">
                                            <presence xmlns=""urn:ietf:params:xml:ns:pidf"" xmlns:ca=""urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr"" xmlns:gml=""http://www.opengis.net/gml"" xmlns:ns4=""urn:ietf:params:xml:ns:pidf"" xmlns:ns5=""urn:ietf:params:xml:ns:pidf:data-model"" xmlns:ns6=""urn:ietf:params:xml:ns:pidf:geopriv10"" xmlns:ns7=""urn:ietf:params:xml:ns:pidf:geopriv10:basicPolicy"" xmlns:ns8=""http://www.opengis.net/pidflo/1.0"" xmlns:ns9=""urn:ietf:params:xml:ns:geopriv:held:id"" xmlns:xsd=""http://www.w3.org/2001/XMLSchema"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"" entity=""pres:<EMAIL>"">
                                              <device id=""device1"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                      {device1}
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>device1MethodCivic</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>device1ContactCivic</contact>
                                              </device>
                                              <device id=""device2"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                      {device2}
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>device2MethodGeo</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>device2ContactGeo</contact>
                                              </device>
                                              <tuple id=""tuple1"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                      {tuple1}
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>tuple1MethodCivic</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>tuple1ContactCivic</contact>
                                              </tuple>
                                              <tuple id=""tuple2"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                      {tuple2}
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>tuple2MethodGeo</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>tuple2ContactGeo</contact>
                                              </tuple>
                                              <person id=""person1"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                      {person1}
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>person1MethodCivic</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>person1ContactCivic</contact>
                                              </person>
                                              <person id=""person2"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                      {person2}
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>person2MethodGeo</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>person2ContactGeo</contact>
                                              </person>
                                            </presence>
                                          </locationResponse>
                                        </held>";

            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Heirarchy, innerstructure));

            Assert.IsTrue(eventLog.heldResponse.LocationData.Point.LocationSource == EventReceiver.BusinessLogic.Enums.LocationSource.device);
            Assert.IsTrue(eventLog.heldResponse.LocationData.Point.POS == "1.11 -22.22");
        }

        [TestMethod]
        public void EventLog_HELD_Heirarchy_Point_PERSON()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();

            string person1 = string.Format(paramPoint, "100.11 -22.22");
            string person2 = string.Format(paramPoint, "200.11 -22.22");

            string innerstructure = @$"<held xmlns=""http://solacom.com/Logging"">
                                          <locationResponse xmlns=""urn:ietf:params:xml:ns:geopriv:held"">
                                            <presence xmlns=""urn:ietf:params:xml:ns:pidf"" xmlns:ca=""urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr"" xmlns:gml=""http://www.opengis.net/gml"" xmlns:ns4=""urn:ietf:params:xml:ns:pidf"" xmlns:ns5=""urn:ietf:params:xml:ns:pidf:data-model"" xmlns:ns6=""urn:ietf:params:xml:ns:pidf:geopriv10"" xmlns:ns7=""urn:ietf:params:xml:ns:pidf:geopriv10:basicPolicy"" xmlns:ns8=""http://www.opengis.net/pidflo/1.0"" xmlns:ns9=""urn:ietf:params:xml:ns:geopriv:held:id"" xmlns:xsd=""http://www.w3.org/2001/XMLSchema"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"" entity=""pres:<EMAIL>"">
                                              <person id=""person1"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                      {person1}
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>person1MethodCivic</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>person1ContactCivic</contact>
                                              </person>
                                              <person id=""person2"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                      {person2}
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">111</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>person2MethodGeo</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>person2ContactGeo</contact>
                                              </person>
                                            </presence>
                                          </locationResponse>
                                        </held>";

            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Heirarchy, innerstructure));

            Assert.IsTrue(eventLog.heldResponse.LocationData.Point.LocationSource == EventReceiver.BusinessLogic.Enums.LocationSource.person);
            Assert.IsTrue(eventLog.heldResponse.LocationData.Point.POS == "100.11 -22.22");
        }

        [TestMethod]
        public void EventLog_HELD_Heirarchy_Confidence_DEVICE()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();

            string device1 = "device1";
            string tuple1 = "tuple1";
            string person1 = "person1";

            string innerstructure = @$"<held xmlns=""http://solacom.com/Logging"">
                                          <locationResponse xmlns=""urn:ietf:params:xml:ns:geopriv:held"">
                                            <presence xmlns=""urn:ietf:params:xml:ns:pidf"" xmlns:ca=""urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr"" xmlns:gml=""http://www.opengis.net/gml"" xmlns:ns4=""urn:ietf:params:xml:ns:pidf"" xmlns:ns5=""urn:ietf:params:xml:ns:pidf:data-model"" xmlns:ns6=""urn:ietf:params:xml:ns:pidf:geopriv10"" xmlns:ns7=""urn:ietf:params:xml:ns:pidf:geopriv10:basicPolicy"" xmlns:ns8=""http://www.opengis.net/pidflo/1.0"" xmlns:ns9=""urn:ietf:params:xml:ns:geopriv:held:id"" xmlns:xsd=""http://www.w3.org/2001/XMLSchema"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"" entity=""pres:<EMAIL>"">
                                                <tuple id=""tuple1"">
                                                    <status>
                                                      <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                        <gml:Point srsName=""urn:ogc:def:crs:EPSG::4326"">
                                                            <gml:pos>0 0</gml:pos>
                                                          </gml:Point>
                                                        <location-info>
                                                          <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">{tuple1}</confidence>
                                                        </location-info>
                                                        <usage-rules>
                                                          <retransmission-allowed>true</retransmission-allowed>
                                                          <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                        </usage-rules>
                                                        <provided-by>provided-by</provided-by>
                                                        <method>tuple1MethodCivic</method>
                                                      </geopriv>
                                                    </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>tuple1ContactCivic</contact>
                                              </tuple>
                                              <device id=""device1"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                    <gml:Point srsName=""urn:ogc:def:crs:EPSG::4326"">
                                                        <gml:pos>0 0</gml:pos>
                                                    </gml:Point>
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">{device1}</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>device1MethodCivic</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>device1ContactCivic</contact>
                                              </device>
                                              <person id=""person1"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                    <gml:Point srsName=""urn:ogc:def:crs:EPSG::4326"">
                                                        <gml:pos>0 0</gml:pos>
                                                    </gml:Point>
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">{person1}</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>person1MethodCivic</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>person1ContactCivic</contact>
                                              </person>
                                            </presence>
                                          </locationResponse>
                                        </held>";

            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Heirarchy, innerstructure));

            Assert.IsTrue(eventLog.heldResponse.LocationData.Confidence.LocationSource == EventReceiver.BusinessLogic.Enums.LocationSource.device);
            Assert.IsTrue(eventLog.heldResponse.LocationData.Confidence.Text == "device1");
        }

        [TestMethod]
        public void EventLog_HELD_Heirarchy_Confidence_PERSON()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();

            string person1 = "person1";

            string innerstructure = @$"<held xmlns=""http://solacom.com/Logging"">
                                          <locationResponse xmlns=""urn:ietf:params:xml:ns:geopriv:held"">
                                            <presence xmlns=""urn:ietf:params:xml:ns:pidf"" xmlns:ca=""urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr"" xmlns:gml=""http://www.opengis.net/gml"" xmlns:ns4=""urn:ietf:params:xml:ns:pidf"" xmlns:ns5=""urn:ietf:params:xml:ns:pidf:data-model"" xmlns:ns6=""urn:ietf:params:xml:ns:pidf:geopriv10"" xmlns:ns7=""urn:ietf:params:xml:ns:pidf:geopriv10:basicPolicy"" xmlns:ns8=""http://www.opengis.net/pidflo/1.0"" xmlns:ns9=""urn:ietf:params:xml:ns:geopriv:held:id"" xmlns:xsd=""http://www.w3.org/2001/XMLSchema"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"" entity=""pres:<EMAIL>"">
                                              <person id=""person1"">
                                                <status>
                                                  <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
                                                    <location-info>
                                                    <gml:Point srsName=""urn:ogc:def:crs:EPSG::4326"">
                                                        <gml:pos>0 0</gml:pos>
                                                    </gml:Point>
                                                      <confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">{person1}</confidence>
                                                    </location-info>
                                                    <usage-rules>
                                                      <retransmission-allowed>true</retransmission-allowed>
                                                      <retention-expiry>2023-06-13T09:20:53</retention-expiry>
                                                    </usage-rules>
                                                    <provided-by>provided-by</provided-by>
                                                    <method>person1MethodCivic</method>
                                                  </geopriv>
                                                </status>
                                                <timestamp>2023-06-13T09:20:53</timestamp>
                                                <contact>person1ContactCivic</contact>
                                              </person>
                                            </presence>
                                          </locationResponse>
                                        </held>";

            locationHandler.PopulateEventLogByHELD(eventLog, string.Format(EventLogExample_HELDResponse_Heirarchy, innerstructure));

            Assert.IsTrue(eventLog.heldResponse.LocationData.Confidence.LocationSource == EventReceiver.BusinessLogic.Enums.LocationSource.person);
            Assert.IsTrue(eventLog.heldResponse.LocationData.Confidence.Text == "person1");
        }

        #endregion

        #region EIDD Response (location) tests

        /// <summary>
        /// Sample HELD Response XML
        /// </summary>
        private string EventLogExample_EIDD_template = @"<LogEvent xmlns=""http://solacom.com/Logging\"">
                                                        <timestamp>2023-02-06T14:11:41.533Z</timestamp>
                                                        <agencyOrElement>PORSCHE-A_A</agencyOrElement>
                                                        <agent>.</agent>
                                                        <callIdentifier>_CI_18627105C08C000011D1 @PORSCHE-A</callIdentifier>
                                                        <incidentIdentifier>_II_18627105C08C000011D1 @PORSCHE-A</incidentIdentifier>
                                                        <eventType>EIDD</eventType>
                                                        <EIDD>
                                                            <EIDDBody>
	                                                            <ReasonForIssueRegistryText xmlns=""http://solacom.com/Logging"">IncidentUpdate</ReasonForIssueRegistryText>
	                                                                <DocumentCreationDate xmlns=""http://solacom.com/Logging"">
		                                                                <DateTime>2023-02-13T13:20:03.4819559-05:00</DateTime>
	                                                                </DocumentCreationDate>
	                                                                <IssuingElementIdentification xmlns=""http://solacom.com/Logging"">Nicolas_A</IssuingElementIdentification>
	                                                                <DocumentIdentification xmlns=""http://solacom.com/Logging"">07229140-c7eb-4c5f-8d21-302265d5cd00</DocumentIdentification>
	                                                                <IncidentTrackingIdentification xmlns=""http://solacom.com/Logging"">_II_1864BFFD1B0C000000AD@Nicolas</IncidentTrackingIdentification>
	                                                                <Agent xmlns=""http://solacom.com/Logging"">
		                                                                <AgentIdentification>.</AgentIdentification>
		                                                                <AgentWorkstationPositionIdentification>.</AgentWorkstationPositionIdentification>
		                                                                <AgentDeviceRoleRegistryText>.</AgentDeviceRoleRegistryText>
	                                                                </Agent>
	                                                                <Call xmlns=""http://solacom.com/Logging"">
		                                                                <CallIdentifier>_CI_1864BFFD1B0C000000AD@Nicolas</CallIdentifier>
		                                                                <CallTypeDescriptionRegistryText>NG911</CallTypeDescriptionRegistryText>
		                                                                <ActivityDateRange>
			                                                                <StartDate>2023-02-13T13:18:22.842</StartDate>
		                                                                </ActivityDateRange>
		                                                                <CallStatusRegistryText>Active</CallStatusRegistryText>
                                                                        <Location>
			                                                                <LocationTypeDescriptionRegistryText>Caller</LocationTypeDescriptionRegistryText>
			                                                                <LocationByValue>
				                                                                <presence entity=""pres:<EMAIL>"" xmlns=""urn:ietf:params:xml:ns:pidf"" xmlns:ca=""urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr"" xmlns:gml=""http://www.opengis.net/gml"" xmlns:ns4=""urn:ietf:params:xml:ns:pidf"" xmlns:ns5=""urn:ietf:params:xml:ns:pidf:data-model"" xmlns:ns6=""urn:ietf:params:xml:ns:pidf:geopriv10"" xmlns:ns7=""urn:ietf:params:xml:ns:pidf:geopriv10:basicPolicy"" xmlns:ns8=""http://www.opengis.net/pidflo/1.0"" xmlns:ns9=""urn:ietf:params:xml:ns:geopriv:held:id"">
					                                                                <tuple id=""lisLocation"">
						                                                                <status>
							                                                                <geopriv xmlns=""urn:ietf:params:xml:ns:pidf:geopriv10"">
								                                                                <location-info>
									                                                                {0}
								                                                                </location-info>
								                                                                <usage-rules>
									                                                                <retransmission-allowed>true</retransmission-allowed>
									                                                                <retention-expiry>2022-09-27T15:05:21</retention-expiry>
								                                                                </usage-rules>
								                                                                <method>Cell123</method>
							                                                                </geopriv>
						                                                                </status>
						                                                                <timestamp>2022-09-27T15:05:21</timestamp>
						                                                                <contact>5552223333</contact>
					                                                                </tuple>
				                                                                </presence>
			                                                                </LocationByValue>
		                                                                </Location>
		                                                            <AdditionalData>
			                                                            <AdditionalDataURL>.</AdditionalDataURL>
			                                                            <AdditionalDataDetail>
				                                                            <EmergencyCallData.ProviderInfo xmlns=""urn:ietf:params:xml:ns:EmergencyCallData:ProviderInfo"">
					                                                            <DataProviderReference>a357c90e-10ce-463f-9df6-96bb63701881</DataProviderReference>
					                                                            <DataProviderString>Intrado Inc</DataProviderString>
					                                                            <ProviderID>ProviderID</ProviderID>
					                                                            <ProviderIDSeries>NENA</ProviderIDSeries>
					                                                            <TypeOfProvider>Access Network Provider</TypeOfProvider>
					                                                            <ContactURI>17205551111</ContactURI>
					                                                            <Language>EN</Language>
				                                                            </EmergencyCallData.ProviderInfo>
				                                                            <EmergencyCallData.ServiceInfo xmlns=""urn:ietf:params:xml:ns:EmergencyCallData:ServiceInfo"">
					                                                            <DataProviderReference>07b7c3ea-67e4-46c2-8a73-cdf6bb407ef6</DataProviderReference>
					                                                            <ServiceEnvironment/>
					                                                            <ServiceType>wireless</ServiceType>
					                                                            <ServiceMobility>Mobile</ServiceMobility>
				                                                            </EmergencyCallData.ServiceInfo>
				                                                            <EmergencyCallData.DeviceInfo xmlns=""urn:ietf:params:xml:ns:EmergencyCallData:DeviceInfo"">
					                                                            <DataProviderReference>1fb8696d-7c50-47ba-bb7b-a3eefcb21a39</DataProviderReference>
					                                                            <DeviceClassification>DevClass</DeviceClassification>
					                                                            <DeviceMfgr>Mfgr</DeviceMfgr>
					                                                            <DeviceModelNr>Model</DeviceModelNr>
					                                                            <UniqueDeviceID TypeOfDeviceID="""">Id</UniqueDeviceID>
					                                                            <DeviceSpecificData>SpeData</DeviceSpecificData>
					                                                            <DeviceSpecificType>SpecType</DeviceSpecificType>
				                                                            </EmergencyCallData.DeviceInfo>
				                                                            <EmergencyCallData.SubscriberInfo privacyRequested=""false"" xmlns=""urn:ietf:params:xml:ns:EmergencyCallData:SubscriberInfo"">
					                                                            <DataProviderReference>1733ed4b-8387-4116-9177-762c68a2b97e</DataProviderReference>
					                                                            <SubscriberData>
						                                                            <vcard xmlns=""urn:ietf:params:xml:ns:vcard-4.0"">
							                                                            <fn>
								                                                            <text>John Doe</text>
							                                                            </fn>
							                                                            <tel>
								                                                            <text>voice</text>
							                                                            </tel>
							                                                            <tel>
								                                                            <parameters>
									                                                            <type>
										                                                            <text>voice</text>
										                                                            <text>main</text>
									                                                            </type>
								                                                            </parameters>
								                                                            <uri>8195551001</uri>
							                                                            </tel>
							                                                            <tel>
								                                                            <parameters>
									                                                            <type>
										                                                            <text>voice</text>
									                                                            </type>
								                                                            </parameters>
								                                                            <uri>17205551000</uri>
							                                                            </tel>
						                                                            </vcard>
					                                                            </SubscriberData>
				                                                            </EmergencyCallData.SubscriberInfo>
				                                                            <EmergencyCallData.Comment xmlns=""urn:ietf:params:xml:ns:EmergencyCallData:Comment"">
					                                                            <DataProviderReference>d59b3daf-4f89-4bad-8a03-ec85fa45eef3</DataProviderReference>
					                                                            <Comment xml:lang="""">&amp;   &gt;&lt;   </Comment>
					                                                            <Comment xml:lang="""">Co&amp;mment2</Comment>
					                                                            <Comment xml:lang="""">Comment3</Comment>
				                                                            </EmergencyCallData.Comment>
			                                                            </AdditionalDataDetail>
		                                                            </AdditionalData>
	                                                            </Call>
                                                            </EIDDBody>
                                                        </EIDD>
                                                    </LogEvent>";

        [TestMethod]
        public void EventLog_EIDD_CivicAddress()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();
            
            string innerStructure = @"<gml:Point srsName=""urn:ogc:def:crs:EPSG::4326"">
										<gml:pos>28.113055 -81.64747</gml:pos>
									</gml:Point>
									<ca:civicAddress xml:lang=""en-us"">
										<ca:country>AU</ca:country>
										<ca:A1>NSW</ca:A1>
										<ca:A2>354</ca:A2>
										<ca:A3>Wollongon</ca:A3>
										<ca:A4>Gwynneville</ca:A4>
										<ca:STS>Northfield Avenue</ca:STS>
										<ca:LMK>University of Wollongong</ca:LMK>
										<ca:NAM>Andrew Corporation sant..</ca:NAM>
										<ca:PC>23445</ca:PC>
										<ca:BLD>12</ca:BLD>
										<ca:FLR>2</ca:FLR>
										<ca:POBOX>U40</ca:POBOX>
										<ca:SEAT>WS-183</ca:SEAT>
										<ca:RD>Raccoon Valley</ca:RD>
									</ca:civicAddress>
									<confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">18</confidence>";
            locationHandler.PopulateEventLogByEIDD(eventLog, string.Format(EventLogExample_EIDD_template, innerStructure));

            Assert.IsTrue(eventLog.heldResponse.LocationData.LocationEventSource == EventReceiver.BusinessLogic.Enums.LocationEventSource.EIDD);

            Assert.IsTrue(eventLog.heldResponse.LocationData.CivicAddress.Country == "AU");
            Assert.IsTrue(eventLog.heldResponse.LocationData.CivicAddress.POBOX == "U40");
        }

        [TestMethod]
        public void EventLog_EIDD_Point()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();

            string innerStructure = @"<gml:Point srsName=""urn:ogc:def:crs:EPSG::4326"">
										<gml:pos>28.113055 -81.64747</gml:pos>
									</gml:Point>";
            locationHandler.PopulateEventLogByEIDD(eventLog, string.Format(EventLogExample_EIDD_template, innerStructure));

            Assert.IsTrue(eventLog.heldResponse.LocationData.Point.POS == "28.113055 -81.64747");
        }

        [TestMethod]
        public void EventLog_EIDD_Confidence()
        {
            EventLog eventLog = new EventLog();
            eventLog.heldResponse = new EventReceiver.Entities.HeldResponse();
            EventReceiver.BusinessLogic.EventDataHandler.LocationData locationHandler = new EventReceiver.BusinessLogic.EventDataHandler.LocationData();

            string innerStructure = @"<confidence xmlns=""urn:ietf:params:xml:ns:geopriv:conf"">18</confidence>";
            locationHandler.PopulateEventLogByEIDD(eventLog, string.Format(EventLogExample_EIDD_template, innerStructure));

            Assert.IsTrue(eventLog.heldResponse.LocationData.Confidence.Text == "18");
        }
        #endregion

        #region Geo Shape related tests

        [TestMethod]
        public void GeoShape_CenterPoint_Rectangle()
        {
            Solacom.InfoHub.EventReceiver.ElasticSearch.Entities.Polygon poly = new Entities.Polygon();

            poly.Point = new Entities.Point() { Latitude = 0, Longitude = 0 };
            poly.PointList = new List<Entities.Point>() {   new Entities.Point(){ Latitude = 1, Longitude = 1 }, 
                                                            new Entities.Point(){ Latitude = 10, Longitude = 10 },
                                                            new Entities.Point(){ Latitude = 10, Longitude = 20 },
                                                            new Entities.Point(){ Latitude = 1, Longitude = 1 }
                                                        };

            Entities.Point centerPoint = poly.CalculateCenterPointBasedOnPoints();

            Assert.IsTrue(centerPoint.Longitude == 8 && centerPoint.Latitude == 5.5);
           
        }

        #endregion
    }
}
