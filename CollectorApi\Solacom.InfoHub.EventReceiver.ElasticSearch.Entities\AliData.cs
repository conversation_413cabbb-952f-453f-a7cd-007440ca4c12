﻿using System;

namespace Solacom.InfoHub.EventReceiver.ElasticSearch.Entities
{
    public class AliData
    {
        public string CallId { get; set; }
        public string MediaLabel { get; set; }
        public string AliLink { get; set; }
        public string Ali { get; set; }
        public string AliResponseCode { get; set; }
        public string AliQueryType { get; set; }
        public string Uri { get; set; }
        public string ServiceArea { get; set; }
        public DateTime TimeStamp { get; set; }
        public string AgencyOrElement { get; set; }
        public string Agent { get; set; }
        public string EventType { get; set; }
    }
}