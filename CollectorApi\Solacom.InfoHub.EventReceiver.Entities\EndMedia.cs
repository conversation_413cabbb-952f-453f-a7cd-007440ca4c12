﻿using System.Xml.Serialization;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    [XmlRoot(ElementName = "endMedia")]
    public class EndMedia
    {
        [XmlElement(ElementName = "mediaLabel")]
        public string MediaLabel { get; set; }

        /// <summary>
        /// Introduced to catch non-standard integers to allow for casting / default if not an expected int.
        /// Defaults @ResponseCode = -9999 when non-int.
        /// </summary>
        [XmlElement(ElementName = "responseCode")]
        public string? ResponseCodeStr
        {
            get
            {
                return this.ResponseCode.ToString();
            }
            set
            {
                int _responsecode;
                if( int.TryParse(value, out _responsecode) )
                {
                    ResponseCode = _responsecode;
                }
                else// if(value == ".")
                {
                    ResponseCode = -9999;
                }
            }
        }

        /// <summary>
        /// ResponseCode
        /// </summary>
        /// <remarks>
        /// Leveraged in code to int compare if > 256
        /// </remarks>
        [XmlIgnore]
        public int? ResponseCode { get; set; }



        [XmlElement(ElementName = "disconnectReason")]
        public string DisconnectReason { get; set; }
        [XmlElement(ElementName = "voiceQOS")]
        public VoiceQOS VoiceQOS { get; set; }
    }
}
