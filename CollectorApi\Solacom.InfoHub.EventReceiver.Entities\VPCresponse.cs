﻿using System.Xml.Serialization;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    [XmlRoot(ElementName = "vpcResponse")]
    public class VpcResponse
    {
        [XmlElement(ElementName = "mediaLabel")]
        public string MediaLabel { get; set; }
        [XmlElement(ElementName = "vpcDomain")]
        public string VpcDomain { get; set; }
        [XmlElement(ElementName = "responseCode")]
        public string ResponseCode { get; set; }
        [XmlElement(ElementName = "esrn")]
        public string Esrn { get; set; }
        [XmlElement(ElementName = "esqk")]
        public string Esqk { get; set; }
        [XmlElement(ElementName = "esn")]
        public string Esn { get; set; }
    }
}
