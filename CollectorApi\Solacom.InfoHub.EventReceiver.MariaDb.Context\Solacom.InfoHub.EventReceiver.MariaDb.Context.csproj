﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="3.1.6">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="3.1.6" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="3.1.6" />
    <PackageReference Include="MySqlConnector" Version="2.1.13" />
    <PackageReference Include="Newtonsoft.Json" Version="12.0.3" />
    
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Solacom.InfoHub.EventReceiver.ElasticSearch.Entities\Solacom.InfoHub.EventReceiver.ElasticSearch.Entities.csproj" />
  </ItemGroup>

</Project>
