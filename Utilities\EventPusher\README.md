﻿# EventPusher

This is a simple tool that reads from the local resource for Events and sends them to the target API end point.  Repeating with token replacement logic as configured.

## How To Use ##

From the console location, run the following command structure:

```
EventPusher.exe [[FULL URL]] [[PSAP LIST]] [[Iterations]] [[DELAY BETWEEN loops (ms)]
```
Example: 
```
EventPusher.exe http://localhost:55247/api/event  Ottawa,Gatineau 100 500
```

| **Parameter** | **Description** | **Data Type** | **Default Value** | **Required** |
| --- | --- | --- | --- | --- |
| FULL URL | Fully qualified url of the API end point to send the payload to | string | - | yes |
| psap list | Comma seperated list of PSAPs to iterate while pushing the full event to the end point | comma string list | - | yes |
| Iterations | Number of times the Events will be sent to the end point.  If set to -1, the process will not stop until terminated. | int | 1 | no |
| DELAY BETWEEN loops | Delay in milliseconds between loops | int | 1 | no | 


### Output ###
All information is dropped to the Console.  This includes:

Summary line on each full iteration.  Example:
``` 
2023-07-11 4:42:23 PM - Iteration 99 - Unique Id: 20230711164223_Gatineau_99 Status: 16/16
```

If a failure occurs, the line will be shown in the Console.

### Early Termination ###
You can force stop the process at any time by doing **CTRL-C**