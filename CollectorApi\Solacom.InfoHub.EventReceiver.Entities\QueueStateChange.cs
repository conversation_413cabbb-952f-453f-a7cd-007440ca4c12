﻿using System.Xml.Serialization;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    [XmlRoot(ElementName = "QueueStateChange")]
    public class QueueStateChange
    {
        [XmlElement(ElementName = "StateChangeNotificationContents")]
        public string StateChangeNotificationContents { get; set; }
        [XmlElement(ElementName = "queueId")]
        public string QueueId { get; set; }
        [XmlElement(ElementName = "queueName")]
        public string QueueName { get; set; }
        [XmlElement(ElementName = "direction")]
        public string Direction { get; set; }
        [XmlElement(ElementName = "count")]
        public string Count { get; set; }
    }
}
