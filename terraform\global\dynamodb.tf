# Creates a DynamoDB table to be used as a locking mechanism for the remote backend
resource "aws_dynamodb_table" "terraform_state_lock" {
  name           = "terraform-lock"
  read_capacity  = 5
  write_capacity = 5
  hash_key       = "LockID"
  
  attribute {
    name = "LockID"
    type = "S"
  }


  tags  = {
    Name        = "CollectorAPI-${terraform.workspace}"
    Environment = "${terraform.workspace}"
  }
}
