# ---------------------------------------------------------------------------------------------------------------------
# General Variables
# ---------------------------------------------------------------------------------------------------------------------


variable "vpc_cidr_block" {
  description = "The CIDR block for the VPC"
  default     = "10.0.0.0/16"
  type        = "string"
}


variable "vpc_instance_tenancy" {
  description = "A tenancy option for instances launched into the VPC"
  default     = "default"
  type        = "string"
}


variable "vpc_id" {
  description = "The VPC ID"
  type        = "string"
}


variable "vpc_subnet_cidr_block" {
  description = "The CIDR block for the subnet"
  default     = "********/24"
  type        = "string"
}


variable "vpc_tag_name" {
  description = "Tag name"
  type        = "string"
}


variable "subnet_tag_name" {
  description = "Tag name"
  type        = "string"
}
