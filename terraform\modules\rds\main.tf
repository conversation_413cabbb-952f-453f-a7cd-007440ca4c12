resource "aws_db_instance" "main" {
  allocated_storage         = "${var.db_instance_allocated_storage}"
  storage_type              = "${var.db_instance_storage_type}"
  engine                    = "${var.db_instance_engine}"
  engine_version            = "${var.db_instance_engine_version}"
  instance_class            = "${var.db_instance_instance_class}"
  identifier                = "${var.db_instance_identifier}"
  name                      = "${var.db_instance_name}"
  username                  = "${var.db_instance_username}"
  password                  = "${var.db_instance_password}"
  port                      = "${var.db_instance_port}"
  # FIX: Error: Error creating DB Instance: DBParameterGroupNotFound: DBParameterGroup not found: collectorapi.mariadb.10.3.13 status code: 404, request id: caeaef23-39e9-4677-8b42-999bb347b05a
  # parameter_group_name      = "${var.db_instance_parameter_group_name}"
  backup_retention_period   = "${var.db_instance_backup_retention_period}"
  final_snapshot_identifier = "${var.db_instance_final_snapshot_identifier}"
  publicly_accessible = "${var.db_instance_publicly_accessible}"
  vpc_security_group_ids    = [var.db_instance_vpc_security_group_ids]

  # TODO: Autoscaling storage?

  tags  = {
    Environment = "${terraform.workspace}"
    Name        = "${var.db_instance_tag_name}-${terraform.workspace}"
  }
}
