﻿using System.Xml.Serialization;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    [XmlRoot(ElementName = "ecrfResponse")]
    public class EcrfResponse
    {
        [XmlElement(ElementName = "mediaLabel")]
        public string MediaLabel { get; set; }
        [XmlElement(ElementName = "ecrfDomain")]
        public string EcrfDomain { get; set; }
        [XmlElement(ElementName = "responseCode")]
        public string ResponseCode { get; set; }
        /// <summary>
        /// Currently not fully supported - excluding.
        /// </summary>
        /// <remarks>https://solacomtech.atlassian.net/browse/INFO-1771</remarks>
        [XmlIgnore]
        [XmlElement(ElementName = "lost")]
        public string Lost { get; set; }
    }
}
