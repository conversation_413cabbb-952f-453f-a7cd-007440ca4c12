CREATE DATABASE  IF NOT EXISTS mis;
USE mis;
DROP TABLE IF EXISTS alidata;
DROP TABLE IF EXISTS callrecord;
DROP TABLE IF EXISTS routetransferdata;
DROP TABLE IF EXISTS outboundcalldata;
DROP TABLE IF EXISTS startcallrecord;
DROP TABLE IF EXISTS mediarecord;
DROP TABLE IF EXISTS mastercallrecord;
DROP TABLE IF EXISTS callstate;
DROP TABLE IF EXISTS callmobilitytype;
DROP TABLE IF EXISTS agentbusiedrecord;
DROP TABLE IF EXISTS agentavailablerecord;
DROP TABLE IF EXISTS agentsessionrecord;
DROP TABLE IF EXISTS replayevent;

DROP TABLE IF EXISTS failedevent;

DROP TABLE IF EXISTS eventhash;

CREATE TABLE callstate
(
	id					INT NOT NULL AUTO_INCREMENT,
	name				VARCHAR(200) NOT NULL,

	PRIMARY KEY (id)
);

CREATE TABLE callmobilitytype
(
	id					INT NOT NULL AUTO_INCREMENT,
	name				VA<PERSON>HA<PERSON>(200) NOT NULL,

	PRIMARY KEY (id)
);

CREATE TABLE mastercallrecord
(
    id   						INT NOT NULL AUTO_INCREMENT,
    callid 						VARCHAR(200) NOT NULL,
    incidentid 					VARCHAR(200) NOT NULL,
    starttime 					DATETIME NULL,
    endtime 					DATETIME NULL,        
    ani 						VARCHAR(200) NULL,
    callbacknumber 				VARCHAR(200) NULL,
    calltypeid					INT NOT NULL,
    callstateid					INT NOT NULL,
	isabandoned					BIT NULL,
	originalcos					VARCHAR(100) NULL,
	finalcos					VARCHAR(100) NULL,
	callmobilitytypeid 			INT NOT NULL,
	cloudid						VARCHAR(255) NOT NULL,
	istransferred				BIT NULL,
	totalcallduration			double(10,3) NULL,
	timetoanswer				double(10,3) NULL,
	carrier						VARCHAR(200) NULL,
	clientcode					VARCHAR(200) NULL,
	elasticEntities				TEXT NULL,
    PRIMARY KEY (id),
	FOREIGN KEY (callmobilitytypeid) REFERENCES callmobilitytype(id),
	FOREIGN KEY (callstateid) REFERENCES callstate(id)
);
CREATE TABLE agentsessionrecord
(
	id					INT NOT NULL AUTO_INCREMENT,
    logintime			DATETIME NULL,
    logouttime			DATETIME NULL,
	timestamp			DATETIME NULL,
	cloudid				VARCHAR(255) NOT NULL,
	devicename				VARCHAR(200) NULL,
	medialabel				VARCHAR(200) NULL,
	operatorid				VARCHAR(200) NULL,
	psapname				VARCHAR(200) NULL,
	agentname				VARCHAR(200) NULL,
	agentrole				VARCHAR(200) NULL,
	reason					VARCHAR(200) NULL,
	responsecode			VARCHAR(200) NULL,
	tenantgroup				VARCHAR(200) NULL,
	uri						VARCHAR(200) NULL,
	workstation				VARCHAR(200) NULL,
	isonduty				TINYINT NULL,
	isavailable				TINYINT NULL,
	availabletimeminutes	FLOAT,
	busiedOutTimeMinutes	FLOAT,
	dutytimeminutes			FLOAT,
	elasticEntities			TEXT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE agentbusiedrecord
(
	id					INT NOT NULL AUTO_INCREMENT,
    agentsessionrecordid	INT NOT NULL,
    busiedoutaction			VARCHAR(200) NULL,
    endbusyinterval			DATETIME NULL,
    startbusyinterval		DATETIME NULL,
	intervaltimeinminutes	FLOAT,
    PRIMARY KEY (id),
    FOREIGN KEY (agentsessionrecordid) REFERENCES agentsessionrecord(id)
);

CREATE TABLE agentavailablerecord
(
	id					INT NOT NULL AUTO_INCREMENT,
    agentsessionrecordid	INT NOT NULL,
    startavailableinterval	DATETIME NULL,
    endavailableinterval	DATETIME NULL,
	intervaltimeinminutes	FLOAT,
    PRIMARY KEY (id),
    FOREIGN KEY (agentsessionrecordid) REFERENCES agentsessionrecord(id)
);

CREATE TABLE callrecord
(
	id					INT NOT NULL AUTO_INCREMENT,
    mastercallrecordid	INT NOT NULL,
    psapname 			VARCHAR(200) NULL,
    agentname 			VARCHAR(200) NOT NULL,
    positionid			VARCHAR(200)  NULL,
    loginid				VARCHAR(200)  NULL,
	medialabel			VARCHAR(200)  NULL,
    callarrived			DATETIME NULL,
    callpresented		DATETIME NULL,
    callanswered		DATETIME NULL,
    callreleased		DATETIME NULL,
    issystemanswer		TINYINT NULL,
	cloudid				VARCHAR(255) NOT NULL,
	agenttimetoanswer				double(10,3) NULL,
    PRIMARY KEY (id),
    FOREIGN KEY (mastercallrecordid) REFERENCES mastercallrecord(id)
);

CREATE TABLE startcallrecord
(
	id						INT NOT NULL AUTO_INCREMENT,
    mastercallrecordid		INT NOT NULL,
    eventtimestamp			DATETIME NULL,
	isstartcallevent		TINYINT NULL,
	isendcallevent			TINYINT NULL,
	agencyorelement			VARCHAR(200) NOT NULL,
	agentname				VARCHAR(200) NOT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY (mastercallrecordid) REFERENCES mastercallrecord(id)
);
CREATE TABLE mediarecord
(
	id						INT NOT NULL AUTO_INCREMENT,
    mastercallrecordid		INT NOT NULL,
    eventtimestamp			DATETIME NULL,
	isstartmediaevent		TINYINT NULL,
	isendmediaevent			TINYINT NULL,
	agencyorelement			VARCHAR(200) NOT NULL,
	agentname				VARCHAR(200) NOT NULL,
	medialabel				VARCHAR(255) NOT NULL,
	responsecode			INT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY (mastercallrecordid) REFERENCES mastercallrecord(id)
);



CREATE TABLE alidata
(
	id					INT NOT NULL AUTO_INCREMENT,
    mastercallrecordid	INT NOT NULL,
    city				VARCHAR(200) NULL,
    state				VARCHAR(200) NULL,
    country				VARCHAR(200) NULL,
    zipcode				VARCHAR(200) NULL,
    latitude			REAL,
    longitude 			REAL,
    confidence			REAL,
    uncertainty			REAL,
	classofservice		VARCHAR(200) NULL,
	date				VARCHAR(200) NULL,
	time				VARCHAR(200) NULL,
	callbacknumber		VARCHAR(200) NULL,
	address				VARCHAR(200) NULL,
	positionid			VARCHAR(200) NULL,
	type				VARCHAR(200) NULL,
	carrier				VARCHAR(200) NULL,
    PRIMARY KEY (id),
    FOREIGN KEY (mastercallrecordid) REFERENCES mastercallrecord(id)
);

CREATE TABLE routetransferdata
(
	id						INT NOT NULL AUTO_INCREMENT,
    mastercallrecordid		INT NOT NULL,
    eventtimestamp 			DATETIME NOT NULL,
	agency					VARCHAR(200) NULL,
	agent					VARCHAR(200) NULL,
	phone					VARCHAR(200) NULL,
	attemptnumber			INT NULL,
	callpriority			INT NULL,
	iscallended				BIT NULL,
    isTransfer				BIT NOT NULL,
    isAnswered				BIT NOT NULL,
    cloudid					VARCHAR(255) NOT NULL,
	medialabel				VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY (mastercallrecordid) REFERENCES mastercallrecord(id)
);

CREATE TABLE outboundcalldata
(
	id						INT NOT NULL AUTO_INCREMENT,
    mastercallrecordid		INT NOT NULL,
    eventtimestamp 			DATETIME NOT NULL,
	agency					VARCHAR(200) NULL,
	agent					VARCHAR(200) NULL,
    cloudid					VARCHAR(255) NOT NULL,
	outboundtarget			VARCHAR(200) NULL,
	rule					VARCHAR(200) NULL,
    reason					VARCHAR(200) NULL,
	medialabel				VARCHAR(200) NULL,
	attempt					VARCHAR(200) NULL,
    priority				VARCHAR(200) NULL,
	ani						VARCHAR(200) NULL,
	anidomain				VARCHAR(200) NULL,
    dnis					VARCHAR(200) NULL,
	pani					VARCHAR(200) NULL,
	callername				VARCHAR(200) NULL,
    anitranslated			VARCHAR(200) NULL,
	dnistranslated			VARCHAR(200) NULL,
    callernametranslated	VARCHAR(200) NULL,
	method					VARCHAR(200) NULL,
	targettype				VARCHAR(200) NULL,
    targetname				VARCHAR(200) NULL,
    PRIMARY KEY (id),
    FOREIGN KEY (mastercallrecordid) REFERENCES mastercallrecord(id)
);


/*reference data for call states*/
INSERT INTO callstate (id, name) VALUES (1, 'InProgress');
INSERT INTO callstate (id, name) VALUES (2, 'Completed');
INSERT INTO callstate (id, name) VALUES (3, 'Abandoned');

/*reference data for call mobility types*/
INSERT INTO callmobilitytype (id, name) VALUES (1, 'Landline');
INSERT INTO callmobilitytype (id, name) VALUES (2, 'Wireless');
INSERT INTO callmobilitytype (id, name) VALUES (3, 'Voip');
INSERT INTO callmobilitytype (id, name) VALUES (4, 'SMS');
INSERT INTO callmobilitytype (id, name) VALUES (5, 'TDD');
INSERT INTO callmobilitytype (id, name) VALUES (6, 'RTT');
INSERT INTO callmobilitytype (id, name) VALUES (7, 'RecordNotFound');
INSERT INTO callmobilitytype (id, name) VALUES (8, 'Unknown');



CREATE INDEX IF NOT EXISTS callrecord_mastercallrecordid ON callrecord (mastercallrecordid);

CREATE INDEX IF NOT EXISTS routetransferdata_mastercallrecordid ON routetransferdata (mastercallrecordid);

CREATE INDEX IF NOT EXISTS outboundcalldata_mastercallrecordid ON outboundcalldata (mastercallrecordid);

CREATE INDEX IF NOT EXISTS mastercallrecord_callid ON mastercallrecord (callid);



CREATE TABLE eventhash
(
	id						INT NOT NULL AUTO_INCREMENT,
    content					TEXT NOT NULL,
	hashedcontent			VARCHAR(3000) NOT NULL,
	callid 					VARCHAR(200) NULL,
	medialabel				VARCHAR(200) NULL,
    eventtimestamp 			DATETIME NULL,
	eventreceived 			DATETIME NOT NULL,
	clientcode				VARCHAR(255) NOT NULL,
	islastsavesuccessful	BIT NULL,
    message					TEXT NULL,
    PRIMARY KEY (id)
);


CREATE TABLE failedevent
(
	id						INT NOT NULL AUTO_INCREMENT,
    content					TEXT NOT NULL,
	clientcode				VARCHAR(255) NOT NULL,
	eventtimestamp 			DATETIME NULL,
    errormessage			TEXT NULL,
	callid 					VARCHAR(200) NULL,
	medialabel				VARCHAR(200) NULL,
    PRIMARY KEY (id)
);

CREATE TABLE replayevent
(
	id						INT NOT NULL AUTO_INCREMENT,
    content					TEXT NOT NULL,
	clientcode				VARCHAR(255) NOT NULL,
    errormessage			TEXT NULL,
	eventtimestamp 			DATETIME NULL,
	callid 					VARCHAR(200) NULL,
	medialabel				VARCHAR(200) NULL,
    PRIMARY KEY (id)
);



CREATE INDEX IF NOT EXISTS eventhash_hashedcontent ON eventhash (hashedcontent);
CREATE INDEX IF NOT EXISTS replayevent_callid ON replayevent (callid);
CREATE INDEX IF NOT EXISTS failedevent_callid ON failedevent (callid);
CREATE INDEX IF NOT EXISTS agentsessionrecord_medialabel ON agentsessionrecord (medialabel);
CREATE INDEX IF NOT EXISTS eventhash_callid ON eventhash (callid);

