﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "System": "Warning",
      "Microsoft": "Warning"
    }
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Debug",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "Properties": { "ApplicationName": "CollectorAPI" }
  },
  "elasticsearchSettings": {
    "url": "http://localhost:9200",
    "username": "",
    "password": "",
    "listLimitOfQuery": 1000
  },
  "client": "wiregrass-al_reindex_test",
  "tenantCode": "wial",
  "sourceIndex_override": "",
  "targetIndex_suffix": "",
  "filter": {
    "dateStart": "2020-05-10T22:58:10", //
    "dateEnd": "2022-05-12T23:58:10"
  },
  "loop_interval": {
    "period": 2,
    "periodType": "week",
    "dateStart": "",
    "dateEnd": ""
  },
  "clientTenantMapping": {
    "wial": {
      "covington": "covington",
      "crenshaw": "crenshaw",
      "geneva": "geneva",
      "enterprise": "enterprise",
      "coffee": "coffee",
      "headland": "headland",
      "bullock": "bullock",
      "ozark-dale": "ozark-dale",
      "houston": "houston",
      "dothan": "dothan",
      "wiregrass": "wiregrass",
      "butler": "butler",
      "daleville": "daleville",
      "abbeville": "abbeville"
    },
    "thwa": {
      "cresa": "cresa",
      "tcomm": "tcomm",
      "rivercom": "rivercom",
      "wahk": "wahk"
    },
    "buoh": {
      "9com": "9com",
      "8com": "8com",
      "3com": "3com",
      "7com": "7com",
      "6com": "6com",
      "4com": "4com"
    }
  },
  "version": "1.0.0.0"
}
