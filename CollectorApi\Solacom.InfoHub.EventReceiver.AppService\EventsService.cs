﻿using System.Threading.Tasks;
using Solacom.InfoHub.EventReceiver.AppService.Interfaces;
using Solacom.InfoHub.EventReceiver.BusinessLogic.Interfaces;
using Solacom.InfoHub.EventReceiver.Entities;

namespace Solacom.InfoHub.EventReceiver.AppService
{
    public class EventsService : IEventsService
    {
        private readonly IEventsManager _eventsManager;

        public EventsService(IEventsManager eventsManager)
        {
            _eventsManager = eventsManager;
        }

        public async Task<SaveEventResult> SaveEvent(string eventDataXml, EventLog eventLog, UserKeyData userKeyData)
        {
            return await _eventsManager.SaveEvent(eventDataXml, eventLog, userKeyData);
        }
        public async Task<bool> ProcessCall(RootEvent rootEvent, string clientCode, System.Collections.Generic.Dictionary<string, string> tenantLookup, NodaTime.DateTimeZone clientTimezone, int maxEventId, bool expiredProcessingOccurrence = false)
        {
            return await _eventsManager.ProcessCall(rootEvent, clientCode, tenantLookup, clientTimezone, maxEventId, expiredProcessingOccurrence);
        }

        public async Task CleanUpEventHash(string eventData, string clientCode)
        {
            await _eventsManager.CleanUpEventHash(eventData, clientCode);
        }
    }
}
