﻿namespace Solacom.InfoHub.EventReceiver.Entities
{
    public class AboutApiData
    {
        public string Environment { get; set; }
        public string Name { get; set; }

        /// <summary>
        /// stores the version as based on the app.settings
        /// </summary>
        public string Version{ get; set; }

        /// <summary>
        /// stores the build assembly information
        /// </summary>
        public string Build { get; set; }

        /// <summary>
        /// Gets the Version sttring if available, if not, returns a build string.
        /// </summary>
        public string VersionOrBuild
        {
            get
            {
                if( string.IsNullOrEmpty(this.Version))
                {
                    return string.Format("Build: {0}", this.Build);
                }

                return string.Format("Version: {0}", this.Version);
            }
        }
    }
}