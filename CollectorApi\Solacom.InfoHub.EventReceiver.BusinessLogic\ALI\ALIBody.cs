﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Serialization;
using System.Xml;
using Serilog;



namespace Solacom.InfoHub.EventReceiver.BusinessLogic.ALI
{

    public static class ALIHelper
    {
        /// <summary>
        /// Parsing out the ALI Raw data from XML based on hardcoded XPath definitions
        /// </summary>
        /// <param name="xmlString">Raw string XML</param>
        /// <returns>Raw XML Object - null if it wasn't a valid XML structure</returns>
        public static ALIRawData parseALIBody(string xmlString)
        {
            XmlDocument xmlDoc = new XmlDocument();

            try
            {
                xmlDoc.LoadXml(xmlString);
            }
            catch(Exception ex)
            {
                Log.Logger.Error($"Exception on ALI deserilization: {ex.Message}");
                return null;
            }

            ALIRawData rtnAli = new ALIRawData();

            XmlNamespaceManager nsmgr = new XmlNamespaceManager(xmlDoc.NameTable);
            nsmgr.AddNamespace("tns", "urn:nena-org:dtc:aqstcp");
            nsmgr.AddNamespace("aqs", "urn:nena-org:dtc:aqs");
            nsmgr.AddNamespace("tqpe", "TELUS_QP_Extn");
            nsmgr.AddNamespace("nali", "http://www.nena9-1-1.org/schemas/2003/ali");
            nsmgr.AddNamespace("ali", "http://www.nena9-1-1.org/schemas/2003/ali");

            string xpathPrefix = "/tns:Response/aqs:QueryResponse/aqs:QueryResultData/";
            string queryPropPrefix = "/tns:Response/aqs:QueryResponse/aqs:QueryProperities";
            string bodyPrefix = "nali";


            rtnAli.TrunkID = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{queryPropPrefix}/aqs:TrunkID");
            rtnAli.CallingPartyNum = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:CallInfo/{bodyPrefix}:CallingPartyNum");
            rtnAli.MainTelNum = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:CallInfo/{bodyPrefix}:MainTelNum");
            rtnAli.CallBackNum = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:CallInfo/{bodyPrefix}:CallbackNum");
            rtnAli.ESN = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Agencies/{bodyPrefix}:ESN");
            rtnAli.ALIUpdateGMT = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:SourceInfo/{bodyPrefix}:ALIUpdateGMT");
            rtnAli.ALIRetrievalGMT = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:SourceInfo/{bodyPrefix}:ALIRetrieveGMT");

            #region Location Info
            rtnAli.LocationInfo.StreetAddress.HouseNum = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:LocationInfo/{bodyPrefix}:StreetAddress/{bodyPrefix}:HouseNum");
            rtnAli.LocationInfo.StreetAddress.HouseNumSuffix = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:LocationInfo/{bodyPrefix}:StreetAddress/{bodyPrefix}:HouseNumSuffix");
            rtnAli.LocationInfo.StreetAddress.PrefixDirectional = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:LocationInfo/{bodyPrefix}:StreetAddress/{bodyPrefix}:PrefixDirectional");
            rtnAli.LocationInfo.StreetAddress.StreetName = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:LocationInfo/{bodyPrefix}:StreetAddress/{bodyPrefix}:StreetName");
            rtnAli.LocationInfo.StreetAddress.PostDirectional = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:LocationInfo/{bodyPrefix}:StreetAddress/{bodyPrefix}:PostDirectional");
            rtnAli.LocationInfo.StreetAddress.MSAGCommunity = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:LocationInfo/{bodyPrefix}:StreetAddress/{bodyPrefix}:MSAGCommunity");
            rtnAli.LocationInfo.StreetAddress.StateProvince = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:LocationInfo/{bodyPrefix}:StreetAddress/{bodyPrefix}:StateProvince");
            rtnAli.LocationInfo.StreetAddress.PostalZipCode = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:LocationInfo/{bodyPrefix}:StreetAddress/{bodyPrefix}:PostalZipCode");
            rtnAli.LocationInfo.StreetAddress.Country = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:LocationInfo/{bodyPrefix}:StreetAddress/{bodyPrefix}:Country");
            rtnAli.LocationInfo.StreetAddress.UnitNum = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:LocationInfo/{bodyPrefix}:StreetAddress/{bodyPrefix}:UnitNum");
            rtnAli.LocationInfo.StreetAddress.Description = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:LocationInfo/{bodyPrefix}:StreetAddress/{bodyPrefix}:LocationDescription");

            rtnAli.LocationInfo.GeoLocation.Latitude = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:LocationInfo/{bodyPrefix}:GeoLocation/{bodyPrefix}:Latitude");
            rtnAli.LocationInfo.GeoLocation.Longitude = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:LocationInfo/{bodyPrefix}:GeoLocation/{bodyPrefix}:Longitude");
            rtnAli.LocationInfo.GeoLocation.Uncertainty = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:LocationInfo/{bodyPrefix}:GeoLocation/{bodyPrefix}:Uncertainty");
            rtnAli.LocationInfo.GeoLocation.Confidence = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:LocationInfo/{bodyPrefix}:GeoLocation/{bodyPrefix}:Confidence");
            rtnAli.LocationInfo.GeoLocation.Description = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:LocationInfo/{bodyPrefix}:GeoLocation/{bodyPrefix}:LocationDescription");
            #endregion

            #region Agency Elements
            Agency agency = new Agency(Agency.AgencyType.Police);
            agency.Name = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Agencies/{bodyPrefix}:Police/{bodyPrefix}:Name");
            agency.TN = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Agencies/{bodyPrefix}:Police/{bodyPrefix}:TN");
            rtnAli.AgencyList.Add(agency);

            agency = new Agency(Agency.AgencyType.Fire);
            agency.Name = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Agencies/{bodyPrefix}:Fire/{bodyPrefix}:Name");
            agency.TN = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Agencies/{bodyPrefix}:Fire/{bodyPrefix}:TN");
            rtnAli.AgencyList.Add(agency);

            agency = new Agency(Agency.AgencyType.EMS);
            agency.Name = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Agencies/{bodyPrefix}:EMS/{bodyPrefix}:Name");
            agency.TN = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Agencies/{bodyPrefix}:EMS/{bodyPrefix}:TN");
            rtnAli.AgencyList.Add(agency);
            #endregion

            #region Provider* fields

            rtnAli.DataProvider.ID = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:SourceInfo/{bodyPrefix}:DataProvider/{bodyPrefix}:DataProviderID");
            rtnAli.DataProvider.TN = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:SourceInfo/{bodyPrefix}:DataProvider/{bodyPrefix}:TN");
            rtnAli.DataProvider.Name = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:SourceInfo/{bodyPrefix}:DataProvider/{bodyPrefix}:Name");

            rtnAli.AccessProvider.ID = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:SourceInfo/{bodyPrefix}:AccessProvider/{bodyPrefix}:AccessProviderID");
            rtnAli.AccessProvider.TN = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:SourceInfo/{bodyPrefix}:AccessProvider/{bodyPrefix}:TN");
            rtnAli.AccessProvider.Name = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:SourceInfo/{bodyPrefix}:AccessProvider/{bodyPrefix}:Name");

            #endregion

            rtnAli.NetworkInfo.PSAPID = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:NetworkInfo/{bodyPrefix}:PSAPID");
            rtnAli.NetworkInfo.CLLI = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:NetworkInfo/{bodyPrefix}:CLLI");

            //Check the Extension definition to know which code logic to go into
            //Source from testing: urn:bell-canada:ali:aqs:alibody:ext, updating logic checks to do contains logic
            string extensionType = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/@source");
            
            if (extensionType.ToLower().Contains("telus"))
            {
                rtnAli.TELUSExtension = new TELUSExtension();
                nsmgr.AddNamespace("tabx", "TELUS_AB_Extn");

                #region TELUS Extension

                rtnAli.TELUSExtension.CallTakerPosition = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{queryPropPrefix}/aqs:Extension/tqpe:TELUSQueryPropertyExt/tqpe:AgentPosition");
                rtnAli.TELUSExtension.CadPacketId = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{queryPropPrefix}/aqs:Extension/tqpe:TELUSQueryPropertyExt/tqpe:CadPacketId");
                rtnAli.TELUSExtension.CallDateTime = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{queryPropPrefix}/aqs:Extension/tqpe:TELUSQueryPropertyExt/tqpe:CallDateTime");

                rtnAli.TELUSExtension.LocationResultCode = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/tabx:TELUSAliBodyExt/tabx:LocationInfo/tabx:GeoLocation/tabx:LocationResultCode");
                rtnAli.TELUSExtension.LocationResultText = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/tabx:TELUSAliBodyExt/tabx:LocationInfo/tabx:GeoLocation/tabx:LocationResultText");
                rtnAli.TELUSExtension.StreetType = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/tabx:TELUSAliBodyExt/tabx:LocationInfo/tabx:StreetAddress/tabx:StreetType");
                rtnAli.TELUSExtension.Community = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/tabx:TELUSAliBodyExt/tabx:LocationInfo/tabx:StreetAddress/tabx:Community");
                rtnAli.TELUSExtension.LspServAddrComments = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/tabx:TELUSAliBodyExt/tabx:LocationInfo/tabx:StreetAddress/tabx:LspServiceAddrComments");
                rtnAli.TELUSExtension.LocationDescription = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/tabx:TELUSAliBodyExt/tabx:LocationInfo/tabx:StreetAddress/tabx:LocationDescription");
                rtnAli.TELUSExtension.ClassOfService = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/tabx:TELUSAliBodyExt/tabx:LocationInfo/tabx:CallInfo/tabx:ClassOfService");
                rtnAli.TELUSExtension.CustomerName = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/tabx:TELUSAliBodyExt/tabx:LocationInfo/tabx:CallInfo/tabx:CustomerName");
                rtnAli.TELUSExtension.PSAPNumber = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/tabx:TELUSAliBodyExt/tabx:Agencies/tabx:PSAPNumber");

                #endregion
            }
            else if (extensionType.ToLower().Contains("bell"))
            {
                rtnAli.BELLExtension = new BELLExtension();
                nsmgr.AddNamespace("bcabex", "urn:bell-canada:ali:aqs:alibody:ext");

                #region BELL Extension

                rtnAli.BELLExtension.StreetName = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/bcabex:BellAliBodyExt/bcabex:LocationInfo/bcabex:StreetAddress/bcabex:StreetName");
                rtnAli.BELLExtension.PostDirectional = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/bcabex:BellAliBodyExt/bcabex:LocationInfo/bcabex:StreetAddress/bcabex:PostDirectional");
                rtnAli.BELLExtension.PostalCommunity = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/bcabex:BellAliBodyExt/bcabex:LocationInfo/bcabex:StreetAddress/bcabex:PostalCommunity");
                rtnAli.BELLExtension.MSAGCommunityCode = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/bcabex:BellAliBodyExt/bcabex:LocationInfo/bcabex:StreetAddress/bcabex:MSAGCommunityCode");
                rtnAli.BELLExtension.LocationType = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/bcabex:BellAliBodyExt/bcabex:LocationInfo/bcabex:StreetAddress/bcabex:LocationType");
                rtnAli.BELLExtension.LocationNumber = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/bcabex:BellAliBodyExt/bcabex:LocationInfo/bcabex:StreetAddress/bcabex:LocationNumber");
                rtnAli.BELLExtension.ErrorFlag = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/bcabex:BellAliBodyExt/bcabex:LocationInfo/bcabex:StreetAddress/bcabex:ErrorFlag");
                rtnAli.BELLExtension.ResultCode = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/bcabex:BellAliBodyExt/bcabex:LocationInfo/bcabex:GeoLocation/bcabex:ResultCode");
                rtnAli.BELLExtension.ResultMessage = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/bcabex:BellAliBodyExt/bcabex:LocationInfo/bcabex:GeoLocation/bcabex:ResultMessage");
                rtnAli.BELLExtension.ResultDateTime = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/bcabex:BellAliBodyExt/bcabex:LocationInfo/bcabex:GeoLocation/bcabex:ResultDateTime");
                rtnAli.BELLExtension.CustomerName = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/bcabex:BellAliBodyExt/bcabex:CallInfo/bcabex:CustomerName");
                rtnAli.BELLExtension.ClassOfService = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/bcabex:BellAliBodyExt/bcabex:CallInfo/bcabex:ClassOfService");
                rtnAli.BELLExtension.PSAPAnswerDateTime = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/bcabex:BellAliBodyExt/bcabex:CallInfo/bcabex:PSAPAnswerDateTime");
                rtnAli.BELLExtension.NonAnsweredCallDuration = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/bcabex:BellAliBodyExt/bcabex:CallInfo/bcabex:NonAnsweredCallDuration");
                rtnAli.BELLExtension.TransferringPSAPName = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/bcabex:BellAliBodyExt/bcabex:CallInfo/bcabex:TransferringPSAPName");
                rtnAli.BELLExtension.TransferringPSAPConfDuration = XmlHelper.ParseNode(xmlDoc, nsmgr, $"{xpathPrefix}{bodyPrefix}:ALIBody/{bodyPrefix}:Extension/bcabex:BellAliBodyExt/bcabex:CallInfo/bcabex:TransferringPSAPConferenceDuration");

                #endregion
            }
            else
            {
                //logger - no extension found - Currently does not fail the parsing, but should log a tracable Error.  @ERRORCODE event should also be triggered her for later re-processing.
                Log.Logger.Error($"No Extension type '{extensionType}' was not found. ALI parsing is incomplete for ALI XML:\r\n{xmlString}");   
            }

            return rtnAli;
        }

        
    }

    /// <summary>
    /// Raw XML ALI data
    /// </summary>
    public class ALIRawData
    {
        public string TrunkID { get; set; }

        public string CallingPartyNum { get; set; }
        /// <summary>
        /// Telephone number from the caller
        /// </summary>
        /// <remarks>Also known as CallBackNum</remarks>
        public string MainTelNum { get; set; }
        /// <summary>
        /// Telephone number from the caller
        /// </summary>
        /// <remarks>Bell leveraged vs MainTelNum for TELUS.</remarks>
        public string CallBackNum { get; set; }

        public string ESN { get; set; }
        public string ALIUpdateGMT { get; set; }
        public string ALIRetrievalGMT { get; set; }

        public LocationInfo LocationInfo { get; set; }
        public IList<Agency> AgencyList { get; set; }
        public DataProvider DataProvider { get; set; }
        public AccessProvider AccessProvider { get; set; }
        public NetworkInfo NetworkInfo { get; set; }
        public TELUSExtension TELUSExtension { get; set; }
        public BELLExtension BELLExtension { get; set; }

        public ALIRawData()
        {
            LocationInfo = new LocationInfo();
            AgencyList = new List<Agency>();
            DataProvider = new DataProvider();
            AccessProvider = new AccessProvider();
            NetworkInfo = new NetworkInfo();

            //default to null for quicker awareness of which Extension is sourced
            TELUSExtension = null;
            BELLExtension = null;
        }
    }

    public class LocationInfo
    {

        public StreetAddress StreetAddress { get; set; }
        public GeoLocation GeoLocation { get; set; }

        public LocationInfo()
        {
            StreetAddress = new StreetAddress();
            GeoLocation = new GeoLocation();
        }
    }
    public class StreetAddress
    {
        public string HouseNum { get; set; }
        public string HouseNumSuffix { get; set; }
        public string PrefixDirectional { get; set; }
        public string StreetName { get; set; }
        public string PostDirectional { get; set; }
        public string MSAGCommunity { get; set; }
        public string StateProvince { get; set; }
        public string PostalZipCode { get; set; }
        public string Country { get; set; }
        public string UnitNum { get; set; }
        public string Description { get; set; }
    }
    public class GeoLocation
    {
        public string Latitude { get; set; }
        public string Longitude { get; set; }
        public string Uncertainty { get; set; }
        public string Confidence { get; set; }
        public string Description { get; set; }
    }
    public class Agency
    {
        public AgencyType Type { get; set; }
        public string Name { get; set; }
        public string TN { get; set; }

        public enum AgencyType
        {
            Police,
            Fire,
            EMS
        }

        public Agency(AgencyType type)
        {
            Type = type;
        }
    }
    public class AccessProvider
    {
        public string ID { get; set; }
        public string TN { get; set; }
        public string Name { get; set; }
    }
    public class DataProvider
    {
        public string ID { get; set; }
        public string TN { get; set; }
        public string Name { get; set; }
    }
    public class NetworkInfo
    {
        public string PSAPID { get; set; }
        public string CLLI { get; set; }

    }
    public class TELUSExtension
    {
        public string CallTakerPosition { get; set; }
        public string CadPacketId { get; set; }
        public string CallDateTime { get; set; }
        public string LocationResultCode { get; set; }
        public string LocationResultText { get; set; }
        public string StreetType { get; set; }
        public string Community { get; set; }
        public string LspServAddrComments { get; set; }
        public string LocationDescription { get; set; }
        public string ClassOfService { get; set; }
        public string CustomerName { get; set; }
        public string PSAPNumber { get; set; }
    }
    public class BELLExtension
    {
        public string StreetName { get; set; }
        public string PostDirectional { get; set; }
        public string PostalCommunity { get; set; }
        public string MSAGCommunityCode { get; set; }
        public string LocationType { get; set; }
        public string LocationNumber { get; set; }
        public string ErrorFlag { get; set; }
        public string ResultCode { get; set; }
        public string ResultMessage { get; set; }
        public string ResultDateTime { get; set; }
        public string CustomerName { get; set; }
        public string ClassOfService { get; set; }
        public string PSAPAnswerDateTime { get; set; }
        public string NonAnsweredCallDuration { get; set; }
        public string TransferringPSAPName { get; set; }
        public string TransferringPSAPConfDuration { get; set; }
    }



    #region Working code on straight ALI XML Parsing

    #region Test case sample
    //public void ALIDataXML_Deserialized()
    //{
    //    var xmldata = @"<nali:ALIBody schemaVersion=""4.4"">
    //                        <nali:CallInfo>
    //                            <nali:CallingPartyNum>**********</nali:CallingPartyNum>
    //                            <nali:MainTelNum>**********</nali:MainTelNum>
    //                        </nali:CallInfo>
    //                        <nali:LocationInfo>
    //                            <nali:StreetAddress>
    //                                <nali:HouseNum>402</nali:HouseNum>
    //                                <nali:StreetName>8</nali:StreetName>
    //                                <nali:PostDirectional>S</nali:PostDirectional>
    //                                <nali:MSAGCommunity>CITY OF LETHBRIDGE</nali:MSAGCommunity>
    //                                <nali:StateProvince>AB</nali:StateProvince>
    //                                <nali:Country>CA</nali:Country>
    //                            </nali:StreetAddress>
    //                            <nali:GeoLocation>
    //                                <nali:Latitude>49.695609</nali:Latitude>
    //                                <nali:Longitude>-112.833935</nali:Longitude>
    //                                <nali:Uncertainty>246</nali:Uncertainty>
    //                                <nali:Confidence>90</nali:Confidence>
    //                            </nali:GeoLocation>
    //                        </nali:LocationInfo>
    //                        <nali:Agencies>
    //                            <nali:Police>
    //                                <nali:Name>LETHBRIDGE POLICE</nali:Name>
    //                                <nali:TN>4037779720</nali:TN>
    //                            </nali:Police>
    //                            <nali:Fire>
    //                                <nali:Name>LETHBRIDGE PSCC</nali:Name>
    //                                <nali:TN>**********</nali:TN>
    //                            </nali:Fire>
    //                            <nali:EMS>
    //                                <nali:Name>AHSSCC RURAL 1</nali:Name>
    //                                <nali:TN>**********</nali:TN>
    //                            </nali:EMS>
    //                            <nali:ESN>00103</nali:ESN>
    //                        </nali:Agencies>
    //                        <nali:SourceInfo>
    //                            <nali:AccessProvider>
    //                                <nali:AccessProviderID>TELMU</nali:AccessProviderID>
    //                                    <nali:TN>**********</nali:TN>
    //                                    <nali:Name>TELUS</nali:Name>
    //                            </nali:AccessProvider>
    //                        </nali:SourceInfo>
    //                        <nali:Extension name=""TELUSAliBodyExtV1.0"" source=""TELUS"">
    //	            <!--TELUS ALI Body Extension-->
    //	            <tabx:TELUSAliBodyExt xsi:schemaLocation=""TELUS_AB_Extn TELUS_ALI_Body_Ext.xsd"" xmlns:tabx=""TELUS_AB_Extn"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"">
    //		            <tabx:test>test</tabx:test>
    //                            </tabx:TELUSAliBodyExt>
    //            </nali:Extension>
    //                    </nali:ALIBody>";

    //    //BELL TEST
    //    //xmldata = @"<nali:ALIBody schemaVersion=""4.4"">
    //    //                <nali:CallInfo>
    //    //                    <nali:CallingPartyNum>**********</nali:CallingPartyNum>
    //    //                    <nali:MainTelNum>**********</nali:MainTelNum>
    //    //                </nali:CallInfo>
    //    //                <nali:LocationInfo>
    //    //                    <nali:StreetAddress>
    //    //                        <nali:HouseNum>402</nali:HouseNum>
    //    //                        <nali:StreetName>8</nali:StreetName>
    //    //                        <nali:PostDirectional>S</nali:PostDirectional>
    //    //                        <nali:MSAGCommunity>CITY OF LETHBRIDGE</nali:MSAGCommunity>
    //    //                        <nali:StateProvince>AB</nali:StateProvince>
    //    //                        <nali:Country>CA</nali:Country>
    //    //                    </nali:StreetAddress>
    //    //                    <nali:GeoLocation>
    //    //                        <nali:Latitude>49.695609</nali:Latitude>
    //    //                        <nali:Longitude>-112.833935</nali:Longitude>
    //    //                        <nali:Uncertainty>246</nali:Uncertainty>
    //    //                        <nali:Confidence>90</nali:Confidence>
    //    //                    </nali:GeoLocation>
    //    //                </nali:LocationInfo>
    //    //                <nali:Agencies>
    //    //                    <nali:Police>
    //    //                        <nali:Name>LETHBRIDGE POLICE</nali:Name>
    //    //                        <nali:TN>4037779720</nali:TN>
    //    //                    </nali:Police>
    //    //                    <nali:Fire>
    //    //                        <nali:Name>LETHBRIDGE PSCC</nali:Name>
    //    //                        <nali:TN>**********</nali:TN>
    //    //                    </nali:Fire>
    //    //                    <nali:EMS>
    //    //                        <nali:Name>AHSSCC RURAL 1</nali:Name>
    //    //                        <nali:TN>**********</nali:TN>
    //    //                    </nali:EMS>
    //    //                    <nali:ESN>00103</nali:ESN>
    //    //                </nali:Agencies>
    //    //                <nali:SourceInfo>
    //    //                    <nali:AccessProvider>
    //    //                        <nali:AccessProviderID>TELMU</nali:AccessProviderID>
    //    //                            <nali:TN>**********</nali:TN>
    //    //                            <nali:Name>TELUS</nali:Name>
    //    //                    </nali:AccessProvider>
    //    //                </nali:SourceInfo>
    //    //                <nali:Extension name=""BELLAliBodyExtV1.0"" source=""BELL"">
    //    //     <!--TELUS ALI Body Extension-->
    //    //     <tabx:BELLAliBodyExt xsi:schemaLocation=""TELUS_AB_Extn TELUS_ALI_Body_Ext.xsd"" xmlns:tabx=""BELL_AB_Extn"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"">
    //    //      <tabx:test>test</tabx:test>
    //    //                    </tabx:BELLAliBodyExt>
    //    //    </nali:Extension>
    //    //            </nali:ALIBody>";

    //    xmldata = xmldata.Replace("nali:", "");

    //    var data = XmlHelper.DeserializeXml<ALIBody>(xmldata, true);

    //    //Assert.AreEqual("LETHBRIDGE POLICE", data.agencies.police.Name);
    //    Assert.AreEqual("00103", data.agencies.ESN);
    //}
    #endregion

    ///// <summary>
    ///// Parses the structure ALI tns:Response XML to extract only the required ALIBody node
    ///// This includes namespace removal for the core ALIBody
    ///// </summary>
    ///// <param name="xmlString"></param>
    ///// <returns></returns>
    //public static string getALIBody(string xmlString)
    //{
    //    XmlDocument xmlDoc = new XmlDocument();
    //    //Due to the two distinct namespaces between the providers, clearing out the reference for easier parsing of core data.
    //    xmlString = xmlString.Replace("nali:", "").Replace("ali:", "");

    //    XmlNamespaceManager nsmgr = new XmlNamespaceManager(xmlDoc.NameTable);
    //    nsmgr.AddNamespace("tns", "urn:nena-org:dtc:aqstcp");
    //    nsmgr.AddNamespace("aqs", "urn:nena-org:dtc:aqs");

    //    xmlDoc.LoadXml(xmlString);

    //    XmlNode node = xmlDoc.SelectSingleNode("/tns:Response/aqs:QueryResponse/aqs:QueryResultData", nsmgr);

    //    if (node != null)
    //    {
    //        return node.InnerXml;
    //    }

    //    return string.Empty;
    //}

    //[XmlRoot(ElementName = "ALIBody", Namespace="")]
    //public class ALIBody
    //{
    //    [XmlAttribute(AttributeName = "schemaVersion")]
    //    public string schemaVersion { get; set; }

    //    [XmlElement(ElementName = "CallInfo")]
    //    public CallInfo callInfo {get; set;}
    //    [XmlElement(ElementName = "LocationInfo")]
    //    public LocationInfo locationInfo { get; set; }
    //    [XmlElement(ElementName = "Agencies")]
    //    public Agencies agencies { get; set; }
    //    [XmlElement(ElementName = "SourceInfo")]
    //    public SourceInfo sourceInfo { get; set; }
    //    [XmlElement(ElementName = "Extension")]
    //    public Extension extension { get; set; }

    //}

    //[XmlRoot(ElementName = "CallInfo")]
    //public class CallInfo
    //{
    //    [XmlElement(ElementName = "CallingPartyNum")]
    //    public string callingPartyNum { get; set; }
    //    [XmlElement(ElementName = "MainTelNum")]
    //    public string mainTelNum { get; set; }
    //}

    //[XmlRoot(ElementName = "LocationInfo")]
    //public class LocationInfo
    //{
    //    [XmlElement(ElementName = "StreetAddress")]
    //    public StreetAddress streetAddress { get; set; }
    //    [XmlElement(ElementName = "GeoLocation")]
    //    public GeoLocation geoLocation { get; set; }
    //}

    //[XmlRoot(ElementName = "StreetAddress")]
    //public class StreetAddress
    //{
    //    [XmlElement(ElementName = "HouseNum")]
    //    public string houseNum { get; set; }
    //    [XmlElement(ElementName = "StreetName")]
    //    public string streetName { get; set; }
    //    [XmlElement(ElementName = "PostDirectional")]
    //    public string postDirectional { get; set; }
    //    [XmlElement(ElementName = "MSAGCommunity")]
    //    public string msagCommunity { get; set; }
    //    [XmlElement(ElementName = "StateProvince")]
    //    public string stateProvince { get; set; }
    //    [XmlElement(ElementName = "Country")]
    //    public string country { get; set; }
    //}

    //[XmlRoot(ElementName = "GeoLocation")]
    //public class GeoLocation
    //{
    //    [XmlElement(ElementName = "Latitude")]
    //    public string latitude { get; set; }
    //    [XmlElement(ElementName = "Longitude")]
    //    public string longitude { get; set; }
    //    [XmlElement(ElementName = "Uncertainty")]
    //    public string uncertainty { get; set; }
    //    [XmlElement(ElementName = "Confidence")]
    //    public string confidence { get; set; }
    //}

    //[XmlRoot(ElementName = "Agencies")]
    //public class Agencies
    //{
    //    [XmlElement(ElementName ="Police")]
    //    public Police police { get; set; }
    //    [XmlElement(ElementName = "Fire")]
    //    public Fire fire { get; set; }
    //    [XmlElement(ElementName = "EMS")]
    //    public EMS ems { get; set; }

    //    [XmlElement(ElementName = "ESN")]
    //    public string ESN { get; set; } 
    //}

    //[XmlRoot(ElementName = "Police")]
    //public class Police
    //{
    //    [XmlElement(ElementName = "Name")]
    //    public string Name { get; set; }
    //    [XmlElement(ElementName = "TN")]
    //    public string TN { get; set; }
    //}
    //[XmlRoot(ElementName = "Fire")]
    //public class Fire
    //{
    //    [XmlElement(ElementName = "Name")]
    //    public string Name { get; set; }
    //    [XmlElement(ElementName = "TN")]
    //    public string TN { get; set; }

    //}
    //[XmlRoot(ElementName = "EMS")]
    //public class EMS
    //{
    //    [XmlElement(ElementName = "Name")]
    //    public string Name { get; set; }
    //    [XmlElement(ElementName = "TN")]
    //    public string TN { get; set; }
    //}
    //[XmlRoot(ElementName = "SourceInfo")]
    //public class SourceInfo
    //{
    //    [XmlElement(ElementName = "AccessProvider")]
    //    public AccessProvider accessProvider { get; set; }
    //}
    //[XmlRoot(ElementName = "AccessProvider")]
    //public class AccessProvider
    //{
    //    [XmlElement(ElementName = "AccessProviderID")]
    //    public string accessProviderTD { get; set; }
    //    [XmlElement(ElementName = "TN")]
    //    public string TN { get; set; }
    //    [XmlElement(ElementName = "Name")]
    //    public string name { get; set; }
    //}

    //[XmlRoot(ElementName ="Extension")]
    //public class Extension
    //{
    //    [XmlAttribute(AttributeName = "name")]
    //    public string Name { get; set; }
    //    [XmlAttribute(AttributeName = "source")]
    //    public string Source{ get; set; }

    //    [XmlElement(ElementName = "TELUSAliBodyExt", Namespace = "TELUS_AB_Extn")]
    //    public TELUSAliBodyExt telusAliBodyExt { get; set; }


    //    [XmlElement(ElementName = "BELLAliBodyExt", Namespace = "BELL_AB_Extn")]
    //    public BELLAliBodyExt bellAliBodyExt { get; set; }
    //}

    //[XmlRoot(ElementName = "TELUSAliBodyExt", Namespace = "TELUS_AB_Extn")]
    //public class TELUSAliBodyExt
    //{
    //    [XmlAttribute("schemaLocation", Namespace = "http://www.w3.org/2001/XMLSchema-instance")]
    //    public string xsiSchemaLocation { get; set; }

    //    [XmlElement(ElementName ="test", Namespace = "TELUS_AB_Extn")]
    //    public string test { get; set; }
    //}

    //[XmlRoot(ElementName = "BELLAliBodyExt", Namespace = "BELL_AB_Extn")]
    //public class BELLAliBodyExt
    //{
    //    [XmlAttribute("schemaLocation", Namespace = "http://www.w3.org/2001/XMLSchema-instance")]
    //    public string xsiSchemaLocation { get; set; }

    //    [XmlElement(ElementName = "test", Namespace = "BELL_AB_Extn")]
    //    public string test { get; set; }
    //}

    //#endregion
    #endregion

}
