﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Solacom.InfoHub.EventReceiver.Entities;
using Solacom.InfoHub.EventReceiver.MariaDb.Context.Models;

namespace Solacom.InfoHub.EventReceiver.MariaDb.Context.Interfaces
{
    public interface IUnitOfWork_InsightsData : IDisposable
    {
        Task SetCallSummary(string customerName, string tenantPsapName, ElasticSearch.Entities.CallSummary callSummary);

        Task SetCallEventByList(string customerName, string tenantPsapName, List<EventLog> eventLogList);
        Task<(bool, DateTime, long)> CallSummaryExists(string customerName, string callIdentifier);
        Task<(long, long)> DeleteCallData(string customerName, string callIdentifier);
    }

    public interface IDataProvider_InsightsData
    {
        Task SetCallSummary(string customerName, string tenantPsapName, ElasticSearch.Entities.CallSummary callSummary);
        Task SetCallEventByList(string customerName, string tenantPsapName, List<EventLog> eventLogList);
        Task<(bool, DateTime, long)> CallSummaryExists(string customerName, string callIdentifier);
        Task<(long, long)> DeleteCallData(string customerName, string callIdentifier);
    }
}