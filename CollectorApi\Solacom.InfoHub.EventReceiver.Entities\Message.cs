﻿using System.Xml.Serialization;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    [XmlRoot(ElementName = "message")]
    public class Message
    {
        [XmlElement(ElementName = "mediaLabel")]
        public string MediaLabel { get; set; }
        [XmlElement(ElementName = "direction")]
        public string Direction { get; set; }
        [XmlElement(ElementName = "messageType")]
        public string MessageType { get; set; }
        [XmlElement(ElementName = "text")]
        public string Text { get; set; }
        [XmlElement(ElementName = "from")]
        public string From { get; set; }
    }
}
