﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MySqlConnector;
using Newtonsoft.Json;

namespace Solacom.InfoHub.EventReceiver.MariaDb.Context.DAL
{
    public partial class DAL
    {

        /// <summary>
        /// Sets the Call Summary record 
        /// </summary>
        /// <param name="customerName">Customer Name (aka. ClientId)</param>
        /// <param name="tenantPsapName">Psap name</param>
        /// <param name="callsummary">Object containing Call Summary data</param>
        public void SetCallSummary(string customerName, string tenantPsapName, ElasticSearch.Entities.CallSummary callsummary)
        {
            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "SetCallSummary";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_customerName", customerName);
                _SQLCommand.Parameters.AddWithValue("p_tenantPsapName", tenantPsapName);
                _SQLCommand.Parameters.AddWithValue("p_isAbandonedState", callsummary.AbandonedState);
                _SQLCommand.Parameters.AddWithValue("p_address", callsummary.Address);
                _SQLCommand.Parameters.AddWithValue("p_isAdminCall", callsummary.AdminCall);
                _SQLCommand.Parameters.AddWithValue("p_isAdminEmergencyCall", callsummary.AdminEmergencyCall);
                _SQLCommand.Parameters.AddWithValue("p_agentAnsweredWithin10s", callsummary.AgentAnsweredWithin10s);
                _SQLCommand.Parameters.AddWithValue("p_agentAnsweredWithin15s", callsummary.AgentAnsweredWithin15s);
                _SQLCommand.Parameters.AddWithValue("p_agentAnsweredWithin20s", callsummary.AgentAnsweredWithin20s);
                _SQLCommand.Parameters.AddWithValue("p_agentAnsweredWithin40s", callsummary.AgentAnsweredWithin40s);
                _SQLCommand.Parameters.AddWithValue("p_agentAnsweredMoreThan10s", callsummary.AgentAnsweredMoreThan10s);
                _SQLCommand.Parameters.AddWithValue("p_agentAnsweredMoreThan20s", callsummary.AgentAnsweredMoreThan20s);
                _SQLCommand.Parameters.AddWithValue("p_agentAnsweredMoreThan40s", callsummary.AgentAnsweredMoreThan40s);
                _SQLCommand.Parameters.AddWithValue("p_agentCallbacknumber", callsummary.AgentCallbacknumber);
                _SQLCommand.Parameters.AddWithValue("p_agentName", callsummary.AgentName);
                _SQLCommand.Parameters.AddWithValue("p_agentTimeToAnswerInSeconds", callsummary.AgentTimeToAnswerInSeconds);
                _SQLCommand.Parameters.AddWithValue("p_answeredBySystem", callsummary.AnsweredBySystem);
                _SQLCommand.Parameters.AddWithValue("p_systemAnsweredWithin10s", callsummary.SystemAnsweredWithin10s);
                _SQLCommand.Parameters.AddWithValue("p_systemAnsweredWithin15s", callsummary.SystemAnsweredWithin15s);
                _SQLCommand.Parameters.AddWithValue("p_systemAnsweredWithin20s", callsummary.SystemAnsweredWithin20s);
                _SQLCommand.Parameters.AddWithValue("p_systemAnsweredWithin40s", callsummary.SystemAnsweredWithin40s);
                _SQLCommand.Parameters.AddWithValue("p_systemAnsweredMoreThan10s", callsummary.SystemAnsweredMoreThan10s);
                _SQLCommand.Parameters.AddWithValue("p_systemAnsweredMoreThan20s", callsummary.SystemAnsweredMoreThan20s);
                _SQLCommand.Parameters.AddWithValue("p_systemAnsweredMoreThan40s", callsummary.SystemAnsweredMoreThan40s);
                _SQLCommand.Parameters.AddWithValue("p_nonEmergencyAnsweredWithin10s", callsummary.NonEmergencyAnsweredWithin10s);
                _SQLCommand.Parameters.AddWithValue("p_nonEmergencyAnsweredWithin15s", callsummary.NonEmergencyAnsweredWithin15s);
                _SQLCommand.Parameters.AddWithValue("p_nonEmergencyAnsweredWithin20s", callsummary.NonEmergencyAnsweredWithin20s);
                _SQLCommand.Parameters.AddWithValue("p_nonEmergencyAnsweredWithin40s", callsummary.NonEmergencyAnsweredWithin40s);
                _SQLCommand.Parameters.AddWithValue("p_nonEmergencyAnsweredMoreThan10s", callsummary.NonEmergencyAnsweredMoreThan10s);
                _SQLCommand.Parameters.AddWithValue("p_nonEmergencyAnsweredMoreThan20s", callsummary.NonEmergencyAnsweredMoreThan20s);
                _SQLCommand.Parameters.AddWithValue("p_nonEmergencyAnsweredMoreThan40s", callsummary.NonEmergencyAnsweredMoreThan40s);
                _SQLCommand.Parameters.AddWithValue("p_callAnswered", callsummary.CallAnswered);
                _SQLCommand.Parameters.AddWithValue("p_callAnsweredToLocal", callsummary.CallAnsweredToLocal);
                _SQLCommand.Parameters.AddWithValue("p_callArrivedSystem", callsummary.CallArrivedSystem);
                _SQLCommand.Parameters.AddWithValue("p_callArrivedSystemToLocal", callsummary.CallArrivedSystemToLocal);
                _SQLCommand.Parameters.AddWithValue("p_callDetailsIndex", callsummary.CallDetailsIndex);
                _SQLCommand.Parameters.AddWithValue("p_callPresented", callsummary.CallPresented);
                _SQLCommand.Parameters.AddWithValue("p_callPresentedToLocal", callsummary.CallPresentedToLocal);
                _SQLCommand.Parameters.AddWithValue("p_callReleased", callsummary.CallReleased);
                _SQLCommand.Parameters.AddWithValue("p_callReleasedToLocal", callsummary.CallReleasedToLocal);
                _SQLCommand.Parameters.AddWithValue("p_callTransferred", callsummary.CallTransferred);
                _SQLCommand.Parameters.AddWithValue("p_callTransferredToLocal", callsummary.CallTransferredToLocal);
                _SQLCommand.Parameters.AddWithValue("p_callBackNumber", callsummary.CallBackNumber);
                _SQLCommand.Parameters.AddWithValue("p_callIdentifier", callsummary.CallIdentifier);
                _SQLCommand.Parameters.AddWithValue("p_callMobilityType", callsummary.CallMobilityType);
                _SQLCommand.Parameters.AddWithValue("p_callState", callsummary.CallState);
                _SQLCommand.Parameters.AddWithValue("p_callType", callsummary.CallType);
                _SQLCommand.Parameters.AddWithValue("p_carrier", callsummary.Carrier);
                _SQLCommand.Parameters.AddWithValue("p_isCompleted", callsummary.IsCompleted);
                _SQLCommand.Parameters.AddWithValue("p_confidence", callsummary.Confidence);
                _SQLCommand.Parameters.AddWithValue("p_isEmergencyCall", callsummary.EmergencyCall);
                _SQLCommand.Parameters.AddWithValue("p_endCallTime", callsummary.EndCallTime);
                _SQLCommand.Parameters.AddWithValue("p_endCallTimeToLocal", callsummary.EndCallTimeToLocal);
                _SQLCommand.Parameters.AddWithValue("p_esn", callsummary.Esn);
                _SQLCommand.Parameters.AddWithValue("p_finalCos", callsummary.FinalCos);
                _SQLCommand.Parameters.AddWithValue("p_holdTimeInSeconds", callsummary.HoldTimeInSeconds);
                _SQLCommand.Parameters.AddWithValue("p_isInProgress", callsummary.InProgress);
                _SQLCommand.Parameters.AddWithValue("p_incidentIdentifier", callsummary.IncidentIdentifier);
                _SQLCommand.Parameters.AddWithValue("p_isAbandoned", (callsummary.IsAbandoned.GetValueOrDefault() ? 1: 0));
                _SQLCommand.Parameters.AddWithValue("p_isAbandonedCallback", callsummary.IsAbandonedCallback);
                _SQLCommand.Parameters.AddWithValue("p_isAdmin", (callsummary.IsAdmin ? 1: 0));
                _SQLCommand.Parameters.AddWithValue("p_isAdminEmergency", (callsummary.IsAdminEmergency ? 1 : 0));
                _SQLCommand.Parameters.AddWithValue("p_isAlternativeRoute", callsummary.IsAlternativeRoute);
                _SQLCommand.Parameters.AddWithValue("p_isCallback", callsummary.IsCallback);
                _SQLCommand.Parameters.AddWithValue("p_isEmergency", (callsummary.IsEmergency ? 1: 0));
                _SQLCommand.Parameters.AddWithValue("p_isInternalTransferCall", callsummary.IsInternalTransferCall);
                _SQLCommand.Parameters.AddWithValue("p_isOutbound", callsummary.IsOutbound);
                _SQLCommand.Parameters.AddWithValue("p_isTandem", (callsummary.IsTandem ? 1: 0));
                _SQLCommand.Parameters.AddWithValue("p_isTransferred", callsummary.IsTransferred);
                _SQLCommand.Parameters.AddWithValue("p_isUnknownType", callsummary.IsUnknownType);
                _SQLCommand.Parameters.AddWithValue("p_isLandlineType", callsummary.LandlineType);
                if( callsummary.Location != null )
                {
                    _SQLCommand.Parameters.AddWithValue("p_latitude", callsummary.Location.Lat);
                    _SQLCommand.Parameters.AddWithValue("p_longitude", callsummary.Location.Lon);
                }                
                else
                {
                    _SQLCommand.Parameters.AddWithValue("p_latitude", DBNull.Value);
                    _SQLCommand.Parameters.AddWithValue("p_longitude", DBNull.Value);
                }
                _SQLCommand.Parameters.AddWithValue("p_mediaLabel", callsummary.MediaLabel);
                _SQLCommand.Parameters.AddWithValue("p_nonEmergencyHoldTimeInSeconds", callsummary.NonEmergencyHoldTimeInSeconds);
                _SQLCommand.Parameters.AddWithValue("p_nonEmergencyPsapTimeToAnswerInSeconds", callsummary.NonEmergencyPsapTimeToAnswerInSeconds);
                _SQLCommand.Parameters.AddWithValue("p_isNotFoundType", callsummary.NotFoundType);
                _SQLCommand.Parameters.AddWithValue("p_originalCos", callsummary.OriginalCos);
                _SQLCommand.Parameters.AddWithValue("p_processedTime", callsummary.ProcessedTime);
                _SQLCommand.Parameters.AddWithValue("p_psapName", callsummary.PsapName);
                _SQLCommand.Parameters.AddWithValue("p_psapTimeToAnswerInSeconds", callsummary.PsapTimeToAnswerInSeconds);
                _SQLCommand.Parameters.AddWithValue("p_isRTTType", callsummary.RTTType);
                _SQLCommand.Parameters.AddWithValue("p_isSMSType", callsummary.SMSType);
                _SQLCommand.Parameters.AddWithValue("p_startCallTime", callsummary.StartCallTime);
                _SQLCommand.Parameters.AddWithValue("p_startCallTimeToLocal", callsummary.StartCallTimeToLocal);
                _SQLCommand.Parameters.AddWithValue("p_isTDDChallenge", callsummary.TDDChallenge);
                _SQLCommand.Parameters.AddWithValue("p_isTDDType", callsummary.TDDType);
                _SQLCommand.Parameters.AddWithValue("p_talkTimeInSeconds", callsummary.TalkTimeInSeconds);
                _SQLCommand.Parameters.AddWithValue("p_nonEmergencyTalkTimeInSeconds", callsummary.NonEmergencyTalkTimeInSeconds);
                _SQLCommand.Parameters.AddWithValue("p_isTandemCall", callsummary.TandemCall);
                _SQLCommand.Parameters.AddWithValue("p_timeStamp", callsummary.TimeStamp);
                _SQLCommand.Parameters.AddWithValue("p_timeStampToLocal", callsummary.TimeStampToLocal);
                _SQLCommand.Parameters.AddWithValue("p_timeToAnswerInSeconds", callsummary.TimeToAnswerInSeconds);
                _SQLCommand.Parameters.AddWithValue("p_nonEmergencyTimeToAnswerInSeconds", callsummary.NonEmergencyTimeToAnswerInSeconds);
                _SQLCommand.Parameters.AddWithValue("p_timeToTransferInSeconds", callsummary.TimeToTransferInSeconds);
                _SQLCommand.Parameters.AddWithValue("p_totalCallTimeInSeconds", callsummary.TotalCallTimeInSeconds);
                _SQLCommand.Parameters.AddWithValue("p_nonEmergencyTotalCallTimeInSeconds", callsummary.NonEmergencyTotalCallTimeInSeconds);
                _SQLCommand.Parameters.AddWithValue("p_transferFrom", callsummary.TransferFrom);
                _SQLCommand.Parameters.AddWithValue("p_transferTo", callsummary.TransferTo);
                _SQLCommand.Parameters.AddWithValue("p_uncertainty", callsummary.Uncertainty);
                _SQLCommand.Parameters.AddWithValue("p_isUnknownCall", callsummary.UnknownCall);
                _SQLCommand.Parameters.AddWithValue("p_isVoipType", callsummary.VoipType);
                _SQLCommand.Parameters.AddWithValue("p_isWirelessType", callsummary.WirelessType);
                _SQLCommand.Parameters.AddWithValue("p_zipcode", callsummary.Zipcode);
                _SQLCommand.Parameters.AddWithValue("p_locationDataList", JsonConvert.SerializeObject(callsummary.LocationDataList));

                OpenSQLConnection();

                _SQLCommand.ExecuteNonQuery();
            }
            finally
            {
                CleanupSQLResources();
            }
        }

        /// <summary>
        /// Sets the Event Log 
        /// </summary>
        /// <param name="customerName">Customer Name (aka. ClientId)</param>
        /// <param name="tenantPsapName">Psap name</param>
        /// <param name="eventLog">Event Log object</param>
        public void SetCallEvent(string customerName, string tenantPsapName, Entities.EventLog eventLog)
        {
            try
            {
                //in case check.
                if( eventLog == null )
                {
                    return;
                }

                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "SetCallEvent";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_customerName", customerName);
                _SQLCommand.Parameters.AddWithValue("p_tenantPsapName", tenantPsapName);
                _SQLCommand.Parameters.AddWithValue("p_callIdentifier", eventLog.callIdentifier);
                _SQLCommand.Parameters.AddWithValue("p_eventType", eventLog.eventType);
                _SQLCommand.Parameters.AddWithValue("p_eventDatetime", eventLog.timestamp);
                _SQLCommand.Parameters.AddWithValue("p_eventDatetimeToLocal", eventLog.timestampToLocal);
                _SQLCommand.Parameters.AddWithValue("p_eventReceived", eventLog.eventReceived);
                _SQLCommand.Parameters.AddWithValue("p_eventData", JsonConvert.SerializeObject(eventLog));

                OpenSQLConnection();

                _SQLCommand.ExecuteNonQuery();
            }
            finally
            {
                CleanupSQLResources();
            }
        }

        /// <summary>
        /// Checks to see if the given Call identifier was previously processed
        /// </summary>
        /// <param name="customerName">Unique Customer Name</param>
        /// <param name="callIdentifier">Unique call identifier</param>
        /// <returns>if previously processed, with when and how many records found</returns>
        public (bool, DateTime, long) CallSummaryExists(string customerName, string callIdentifier)
        {
            DateTime dateCreated = DateTime.Now;
            long callSummaryCount = 0;
            bool exists = false;

            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "CallSummaryExists";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_customerName", customerName);
                _SQLCommand.Parameters.AddWithValue("p_callIdentifier", callIdentifier);

                OpenSQLConnection();

                MySqlDataReader dr = _SQLCommand.ExecuteReader();

                if (dr.Read())
                {
                    dateCreated = (DateTime)dr["dateCreated"];
                    callSummaryCount = (long)dr["CallSummaryCount"];

                    exists = true;
                }

                dr.Close();
                dr = null;
            }
            finally
            {
                CleanupSQLResources();
            }

            return (exists, dateCreated, callSummaryCount);
        }

        /// <summary>
        /// Deletes all associated data to a given Call identifier from the final data
        /// </summary>
        /// <param name="customerName">Unique Customer Name</param>
        /// <param name="callIdentifier">Unique call identifier</param>
        /// <returns>number of Call Summary records and Event records deleted.</returns>
        public (long, long) DeleteCallData(string customerName, string callIdentifier)
        {
            long callSummaryCount = 0;
            long callEventCount = 0;

            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "DeleteCallData";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_customerName", customerName);
                _SQLCommand.Parameters.AddWithValue("p_callIdentifier", callIdentifier);

                OpenSQLConnection();

                MySqlDataReader dr = _SQLCommand.ExecuteReader();

                if (dr.Read())
                {
                    callSummaryCount = (long)dr["CallSummaryCount"];
                    callEventCount = (long)dr["CallEventCount"];
                }

                dr.Close();
                dr = null;
            }
            finally
            {
                CleanupSQLResources();
            }

            return (callSummaryCount, callEventCount);
        }

    }
}
