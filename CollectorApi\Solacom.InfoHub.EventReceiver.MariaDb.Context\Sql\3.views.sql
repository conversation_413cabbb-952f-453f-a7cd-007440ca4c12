﻿/** 
	Created: Aug 2021
	Author: <PERSON>
	Drop / Create logic for all views.
	
	Updated: Nov 2023 - added in core view example for InsightsData.CallSummary
	
**/

/**
create or replace
algorithm = UNDEFINED view GuardianInsights.`VIEWNAME` as
--QUERY
**/
CREATE OR REPLACE VIEW 
	InsightsData.CallSummary_ALLCLIENT AS
	SELECT 
		customerName,
		CASE WHEN tenantPsapName = '' OR tenantPsapName IS NULL THEN 'Not Assigned'
		ELSE tenantPsapName END AS tenantPsapName,
		callDetailsIndex,
		isAbandonedState as abandonedState, 
		address, 
		isAdminCall as adminCall, 
		isAdminEmergencyCall AS adminEmergencyCall, 
		agentAnsweredWithin10s, agentAnsweredWithin15s, agentAnsweredWithin20s, agentAnsweredWithin40s, agentAnsweredMoreThan10s, agentAnsweredMoreThan20s, agentAnsweredMoreThan40s, 
		agentName, 
		agentTimeToAnswerInSeconds, 
		answeredBySystem, 
		systemAnsweredWithin10s, systemAnsweredWithin15s, systemAnsweredWithin20s, systemAnsweredWithin40s, systemAnsweredMoreThan10s, systemAnsweredMoreThan20s, systemAnsweredMoreThan40s, 
		callAnsweredToLocal,
		callArrivedSystemToLocal, 
		callPresentedToLocal, 
		callReleasedToLocal,
		callTransferredToLocal,
		callBackNumber, 
		callIdentifier, 
		callMobilityType AS carrierIdType,  
		callState, 
		callType, 
		carrier, 
		confidence, 
		isEmergencyCall AS emergencyCall, 
		endCallTimeToLocal,
		esn, 
		finalCos, 
		holdTimeInSeconds, 
		incidentIdentifier, 
		isAbandonedCallback as abandonedCallback, 
		isAdmin as admin, 
		isAdminEmergency as adminEmergency, 
		isAlternativeRoute as alternativeRoute, 
		isCallback as callBack, 
		isInternalTransferCall as internalTransferCall, 
		isOutbound as outbound, 
		isTandem as tandem, 
		isTransferred as transferred, 
		isUnknownType as unknownType, 
		isLandlineType AS landlineType, 
		latitude, 
		longitude,
		mediaLabel,
		nonEmergencyHoldTimeInSeconds, 
		nonEmergencyPsapTimeToAnswerInSeconds, 
		isNotFoundType AS notFoundType, 
		originalCos, 
		psapTimeToAnswerInSeconds, 
		isRTTType AS rTTType, 
		isSMSType AS sMSType, 
		startCallTimeToLocal, 
		isTDDChallenge AS tDDChallengeType, 
		isTDDType AS tDDType, 
		talkTimeInSeconds, 
		nonEmergencyTalkTimeInSeconds,
		isTandemCall AS tandemCall, 
		`timeStamp`, 
		`timeStampToLocal`, 
		timeToAnswerInSeconds, 
		nonEmergencyTimeToAnswerInSeconds,
		timeToTransferInSeconds, 
		totalCallTimeInSeconds, 
		nonEmergencyTotalCallTimeInSeconds,
		transferFrom, 
		transferTo, 
		uncertainty, 
		isUnknownCall AS unknownCall, 
		isVoipType AS voipType, 
		isWirelessType AS wirelessType, 
		zipcode 
	FROM InsightsData.callsummary;
	
CREATE OR REPLACE VIEW 
	InsightsData.CallEvent_ALLCLIENT AS
		select 	customerName,
				tenantPsapName,
				callIdentifier,
				eventType,
				eventDatetime as timestamp,
				eventDatetimeToLocal as timestampToLocal,
				JSON_VALUE(eventData, '$.startCall.CallType') as callType,
				JSON_VALUE(eventData, '$.agent') as agent,
				JSON_VALUE(eventData, '$.startCall.IncomingCallPolicy') as incomingCallPolicy,
				CONCAT_WS('', 	JSON_VALUE(eventData, '$.outboundCall.CallerName'),
								JSON_VALUE(eventData, '$.route.CallerName'),
								JSON_VALUE(eventData, '$.startCall.CallerName'),
								JSON_VALUE(eventData, '$.transferCall.CallerName')
						) as callerName,
				CONCAT_WS('', 	JSON_VALUE(eventData, '$.answer.Workstation'),
								JSON_VALUE(eventData, '$.busiedOut.Workstation'),
								JSON_VALUE(eventData, '$.agentAvailable.Workstation')
						) as workstation,
				CONCAT_WS('', 	JSON_VALUE(eventData, '$.answer.AgentRole'),
								JSON_VALUE(eventData, '$.busiedOut.AgentRole')
						) as agentRole,
				CONCAT_WS('',	JSON_VALUE(eventData, '$.route.Dnis'),
								JSON_VALUE(eventData, '$.transferCall.Dnis')
						) as dnis,
				CONCAT_WS('',	JSON_VALUE(eventData, '$.route.DnisTranslated'),
								JSON_VALUE(eventData, '$.transferCall.DnisTranslated')
						) as dnisTranslated,
				CONCAT_WS('',	JSON_VALUE(eventData, '$.route.AniTranslated'),
								JSON_VALUE(eventData, '$.transferCall.AniTranslated')
						) as AniTranslated,
				JSON_VALUE(eventData, '$.route.Esrn') as esrn,
				JSON_VALUE(eventData, '$.transferCall.Method') as method,
				JSON_VALUE(eventData, '$.transferCall.TargetType') as targetType,
				JSON_VALUE(eventData, '$.transferCall.TransferTarget') as transferTarget,
				JSON_VALUE(eventData, '$.transferCall.TargetName') as targetName,
				JSON_VALUE(eventData, '$.transferCall.CallerNameTranslated') as callerNameTranslated,
				CONCAT_WS('',	JSON_VALUE(eventData, '$.startCall.Pani'),
								JSON_VALUE(eventData, '$.transferCall.Pani')
						) as pani,
				JSON_VALUE(eventData, '$.aliResponse.Ali') as aliResponse,				
				JSON_VALUE(eventData, '$.endMedia.DisconnectReason') as disconnectReason,
				JSON_VALUE(eventData, '$.endMedia.ResponseCode') as responseCode,
				CONCAT_WS('',	JSON_VALUE(eventData, '$.startCall.Ani'),
								JSON_VALUE(eventData, '$.transferCall.Ani')
						) as ani,
				JSON_VALUE(eventData, '$.message.Direction') as direction,
				JSON_VALUE(eventData, '$.message.MessageType') as messageType,
				JSON_VALUE(eventData, '$.message.From') as messageFrom,
				JSON_VALUE(eventData, '$.message.Text') as text,
				CONCAT_WS('', 	JSON_VALUE(eventData, '$.busiedOut.BusiedOutAction'),
								JSON_VALUE(eventData, '$.agentAvailable.BusiedOutAction')
						) as busiedOutAction
			from InsightsData.callevent;