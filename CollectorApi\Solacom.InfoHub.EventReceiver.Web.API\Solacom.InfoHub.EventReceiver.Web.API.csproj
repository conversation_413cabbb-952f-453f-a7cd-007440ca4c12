﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <AspNetCoreHostingModel>OutOfProcess</AspNetCoreHostingModel>
    <AspNetCoreModuleName>AspNetCoreModule</AspNetCoreModuleName>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>
	<Deterministic>false</Deterministic>
    <AssemblyVersion>1.0.*</AssemblyVersion>
    <UserSecretsId>9f71fac4-7fc3-4a9c-b67a-cdfc81f9d9c4</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Logs\**" />
    <Content Remove="Logs\**" />
    <EmbeddedResource Remove="Logs\**" />
    <None Remove="Logs\**" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="aws-beanstalk-tools-defaults.json" />
    <Content Remove="DockerrunVersion\v1\Dockerrun.aws-CAN.json" />
    <Content Remove="DockerrunVersion\v1\Dockerrun.aws-STG.json" />
    <Content Remove="DockerrunVersion\v1\Dockerrun.aws-US.json" />
    <Content Remove="DockerrunVersion\v1\Dockerrun.aws.json" />
  </ItemGroup>

  <ItemGroup>
    <None Include="DockerrunVersion\v1\Dockerrun.aws-CAN-FR.json">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
    <None Include="DockerrunVersion\v1\Dockerrun.aws-STG.json" />
    <None Include="DockerrunVersion\v1\Dockerrun.aws-US.json">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
    <None Include="DockerrunVersion\v1\Dockerrun.aws.json">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Amazon.Extensions.Configuration.SystemsManager" Version="4.0.0" />
    <PackageReference Include="AWSSDK.SecretsManager" Version="3.7.400.15" />
    <PackageReference Include="Kralizek.Extensions.Configuration.AWSSecretsManager" Version="1.7.0" />
    <PackageReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="Microsoft.AspNetCore.Cryptography.Internal" Version="7.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Cryptography.KeyDerivation" Version="7.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="3.1.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="3.1.6">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="3.1.6" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.10.8" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="3.1.3" />
    <PackageReference Include="NEST" Version="7.17.5" />
    <PackageReference Include="NodaTime" Version="3.1.11" />
    <PackageReference Include="Serilog.Enrichers.AspnetcoreHttpcontext" Version="1.1.0" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="3.0.1" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="3.1.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="1.4.0" />
    <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="8.4.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="4.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Solacom.InfoHub.EventReceiver.AppService\Solacom.InfoHub.EventReceiver.AppService.csproj" />
    <ProjectReference Include="..\Solacom.InfoHub.EventReceiver.BusinessLogic\Solacom.InfoHub.EventReceiver.BusinessLogic.csproj" />
    <ProjectReference Include="..\Solacom.InfoHub.EventReceiver.Entities\Solacom.InfoHub.EventReceiver.Entities.csproj" />
    <ProjectReference Include="..\Solacom.InfoHub.EventReceiver.Exceptions\Solacom.InfoHub.EventReceiver.Exceptions.csproj" />
    <ProjectReference Include="..\Solacom.InfoHub.EventReceiver.MariaDb.Context\Solacom.InfoHub.EventReceiver.MariaDb.Context.csproj" />
    <ProjectReference Include="..\Solacom.InfoHub.EventReceiver.Web.Dtos\Solacom.InfoHub.EventReceiver.Web.Dtos.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="ALIParsingXML\nicholascounty-wv_ALISchema.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ALIParsingXML\clinton-oh_ALISchema.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ALIParsingXML\roanecounty-wv_ALISchema.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ALIParsingXML\flagler-fl_ALISchema.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ALIParsingXML\wasilla-ak_ALISchema.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ALIParsingXML\ashley-ar_ALISchema.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ALIParsingXML\bonneville-id_ALISchema.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ALIParsingXML\danecounty-wi_ALISchema.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ALIParsingXML\wiregrass-al_ALISchema.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ALIParsingXML\butler-oh_ALISchema.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ALIParsingXML\thurston-wa_ALISchema.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ALIParsingXML\brandon-mb_ALISchema.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ALIParsingXML\nicholascounty-wv_ALISchema.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
