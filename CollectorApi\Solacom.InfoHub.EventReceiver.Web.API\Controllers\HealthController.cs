﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using Solacom.InfoHub.EventReceiver.MariaDb.Context.DAL;
using Solacom.InfoHub.EventReceiver.Entities.Security;
using System.Text;
using System.Net.Http;
using System.Net;
using Nest;

namespace Solacom.InfoHub.EventReceiver.Web.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class HealthController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<EventsController> _logger;
        public HealthController(ILogger<EventsController> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
        }

        [HttpGet]
        [Route("/api/health/ping")]
        public IActionResult GetPing()
        {
            return Ok("CollectorAPI is running");
        }

        /// <summary>
        /// Retrieves the DB version of the active connected Database.
        /// Currently disabled/marked private - maintained to enable debugging when required / can be introduced during more detailed Health status end point creation.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/sqlDatabase/status")]
        public IActionResult GetSqlDatabaseStatus()
        {
            string versionString = "";
            string connectionString = Encryption.Decrypt(_configuration["Database:mysql:connectionstring.CollectorAPI"]);
            try
            {
                using (DAL dal = new DAL(connectionString))
                {
                    versionString = dal.GetVersion();
                }
            }
            catch (Exception ex)
            {
                //Quick logic to remove the Password form any debug information sent out.  
                System.Data.Common.DbConnectionStringBuilder builder = new System.Data.Common.DbConnectionStringBuilder();
                builder.ConnectionString = connectionString;
                builder.Remove("password");

                _logger.LogError(ex, $"HealthController.GetDBVersion() failure. {ex.Message}. \r\n Connecting to {builder.ConnectionString}. (password removed for security)");
                return BadRequest("Error connecting to SQL Database");
            }

            return Ok($"DB Version detected: {versionString}");
        }


    }
}
