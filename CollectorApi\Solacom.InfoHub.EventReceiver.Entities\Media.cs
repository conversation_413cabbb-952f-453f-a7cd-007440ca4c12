﻿using System.Xml.Serialization;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    [XmlRoot(ElementName = "media")]
    public class Media
    {
        [XmlElement(ElementName = "udp")]
        public string Udp { get; set; }
        [XmlElement(ElementName = "mediaLabel")]
        public string MediaLabel { get; set; }
        [XmlElement(ElementName = "mediaType")]
        public string MediaType { get; set; }
    }
}
