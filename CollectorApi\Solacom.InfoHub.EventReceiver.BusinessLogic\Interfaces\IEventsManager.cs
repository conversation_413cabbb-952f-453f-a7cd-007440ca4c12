﻿using System.Threading.Tasks;
using Solacom.InfoHub.EventReceiver.Entities;
using Solacom.InfoHub.EventReceiver.MariaDb.Context.Models;

namespace Solacom.InfoHub.EventReceiver.BusinessLogic.Interfaces
{
    public interface IEventsManager
    {
        
        Task<SaveEventResult> SaveEvent(string eventLogDataXml, EventLog eventLog, UserKeyData userKeyData);
        Task<bool> ProcessCall(RootEvent rootEvent, string clientCode, System.Collections.Generic.Dictionary<string, string> tenantLookup, NodaTime.DateTimeZone clientTimezone, int maxEventId, bool expiredProcessingOccurrence = false);
        Task ProcessExpiredEvents(System.Collections.Generic.Dictionary<string, System.Collections.Generic.Dictionary<string,string>> tenantLookupCollection, System.Collections.Generic.Dictionary<string, NodaTime.DateTimeZone> clientTimeZoneMapping, int numberOfHours);
        Task ProcessQueueCalls(System.Collections.Generic.Dictionary<string, System.Collections.Generic.Dictionary<string, string>> tenantLookupCollection, System.Collections.Generic.Dictionary<string, NodaTime.DateTimeZone> clientTimeZoneMapping,  int olderthanMinutes);

        Task CleanUpTables(int hashEventsOlderThan, int eventsOlderThan, int agentSessionOlderThan);

        Task CleanUpEventHash(string eventData, string clientCode);
    }

}