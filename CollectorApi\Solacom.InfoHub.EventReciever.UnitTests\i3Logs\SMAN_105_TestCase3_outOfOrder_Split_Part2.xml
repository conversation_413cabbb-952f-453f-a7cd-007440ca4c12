<LogEvents>



<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:42:17.555Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>op4</agent>
        <callIdentifier>_CI_TESTCASE_3_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_3_@tng000</incidentIdentifier>
        <eventType>CDRtype1</eventType>
        <cdrType1>
                <startTime>2023-02-06T12:31:14.226Z</startTime>
                <operatorId>0</operatorId>
                <ani>8195551002</ani>
                <presentedTime>2023-02-06T12:31:14.541Z</presentedTime>
                <answeredTime>2023-02-06T12:31:16.159Z</answeredTime>
                <jobNumber>.</jobNumber>
                <transferTime></transferTime>
                <transferAnswerTime></transferAnswerTime>
                <disassociatedTime></disassociatedTime>
                <transferTargetType>.</transferTargetType>
                <transferTargetName>.</transferTargetName>
                <transferTarget>.</transferTarget>
                <disconnectReason>.</disconnectReason>
                <ivrOutcome>.</ivrOutcome>
                <externalTransferAttempts>0</externalTransferAttempts>
                <dnis>779</dnis>
                <endTime>2023-02-06T12:42:17.555Z</endTime>
                <callbackNumber>8195551002</callbackNumber>
        </cdrType1>
</LogEvent>


<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:42:17.553Z</timestamp>
        <agencyOrElement>tng000_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_3_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_3_@tng000</incidentIdentifier>
        <eventType>EndCall</eventType>
        <endCall>
                <responseCode>16</responseCode>
                <callReplaced>No</callReplaced>
        </endCall>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:42:17.554Z</timestamp>
        <agencyOrElement>tng000</agencyOrElement>
        <agent>op4</agent>
        <callIdentifier>_CI_TESTCASE_3_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_3_@tng000</incidentIdentifier>
        <eventType>EndMedia</eventType>
        <endMedia>
                <mediaLabel>_ML_18626BF8C88V0000240A@tng000</mediaLabel>
                <responseCode>16</responseCode>
                <disconnectReason></disconnectReason>
                <voiceQOS>
                        <mediaIpSourceAddr>.</mediaIpSourceAddr> 
                        <mediaIpDestAddr>.</mediaIpDestAddr>
                        <mediaUdpRtpSourcePort>-1</mediaUdpRtpSourcePort> 
                        <mediaUdpRtpDestPort>-1</mediaUdpRtpDestPort>
                        <mediaNumOfIpPktRxed>-1</mediaNumOfIpPktRxed> 
                        <mediaNumOfIpPktTxed>-1</mediaNumOfIpPktTxed>
                        <mediaNumOfIpErroredPktRxed>-1</mediaNumOfIpErroredPktRxed>
                        <mediaNumOfRtpPktRxed>-1</mediaNumOfRtpPktRxed>
                        <mediaNumOfRtpPktTxed>-1</mediaNumOfRtpPktTxed>
                        <mediaNumOfRtpPktLost>-1</mediaNumOfRtpPktLost>
                        <mediaNumOfRtpPktDiscarded>-1</mediaNumOfRtpPktDiscarded>
                        <mediaRtpJitter>-1</mediaRtpJitter>
                        <mediaRtpLatency>-1</mediaRtpLatency>
                        <mediaNumOfRtcpPktRxed>-1</mediaNumOfRtcpPktRxed>
                        <mediaNumOfRtcpPktTxed>-1</mediaNumOfRtcpPktTxed> 
                        <mediaFarEndPacketLostPercentage>-1</mediaFarEndPacketLostPercentage>
                        <mediaFarEndCumulativePacketLost>-1</mediaFarEndCumulativePacketLost> 
                        <mediaFarEndInterarrivalJitter>-1</mediaFarEndInterarrivalJitter> 
                </voiceQOS>
        </endMedia>
</LogEvent>




<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:42:13.890Z</timestamp>
        <agencyOrElement>tng000</agencyOrElement>
        <agent>op4</agent>
        <callIdentifier>_CI_TESTCASE_3_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_3_@tng000</incidentIdentifier>
        <eventType>Route</eventType>
        <route>
                <uri>7775553004</uri> 
                <rule>rule #67</rule>
                <reason>alternate</reason>
                <mediaLabel>_ML_18626BF8C88V0000240A@tng000</mediaLabel>
                <attempt>1</attempt>
                <priority>-1</priority>
                <ani>8195551002</ani>
                <aniDomain>.</aniDomain>
                <dnis>7775553004</dnis>
                <pani>8195551002</pani>
                <esrn>0009991504</esrn>
                <callerName>Preserve Call Test</callerName>
                <aniTranslated>8195551002</aniTranslated>
                <dnisTranslated>7775553004</dnisTranslated>
                <callerNameTranslated>Preserve Call Test</callerNameTranslated>
        </route>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:42:13.893Z</timestamp>
        <agencyOrElement>tng000</agencyOrElement>
        <agent>op4</agent>
        <callIdentifier>_CI_TESTCASE_3_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_3_@tng000</incidentIdentifier>
        <eventType>Media</eventType>
        <media>
                <udp>.</udp>
                <mediaLabel>_ML_18626BF8C88V0000240A@tng000</mediaLabel>
        </media>
</LogEvent>

<LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>2023-02-06T12:42:16.058Z</timestamp>
        <agencyOrElement>tng000</agencyOrElement>
        <agent>op4</agent>
        <callIdentifier>_CI_TESTCASE_3_@tng000</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_3_@tng000</incidentIdentifier>
        <eventType>Answer</eventType>
        <answer>
                <mediaLabel>_ML_18626BF8C88V0000240A@tng000</mediaLabel>
                <uri>tel:+7775553004</uri>
                <agentRole>Admin</agentRole>
                <tenantGroup>tng000</tenantGroup>
                <operatorId>0</operatorId>
                <workstation>PORSCHE-004</workstation>
        </answer>
</LogEvent>
</LogEvents>