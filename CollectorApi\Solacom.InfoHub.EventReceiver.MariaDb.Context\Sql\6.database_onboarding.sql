﻿/** 
	Created: Nov 2023
	Author: <PERSON>
	Example of onboarding actions required for a new Customer.
**/

--create the specific user.
CREATE USER 'danecounty_wi_user'@'%' IDENTIFIED BY 'Solacom';


--create the specific view
CREATE DATABASE IF NOT EXISTS danecounty_wi;


CREATE OR REPLACE VIEW 
	danecounty_wi.CallSummary AS
	SELECT * 
	FROM InsightsData.CallSummary_ALLCLIENT
	WHERE customerName = 'danecounty-wi'
	ORDER BY timestamp DESC;

CREATE OR REPLACE VIEW 
	danecounty_wi.CallEvent AS
	SELECT * 
	FROM InsightsData.CallEvent_ALLCLIENT
	WHERE customerName = 'danecounty-wi'
	ORDER BY timestamp DESC;

--create the specific permissions

GRANT Select ON danecounty_wi.CallSummary TO 'danecounty_wi_user'@'%';
GRANT SHOW VIEW ON danecounty_wi.CallSummary TO 'danecounty_wi_user'@'%';

GRANT Select ON danecounty_wi.CallEvent TO 'danecounty_wi_user'@'%';
GRANT SHOW VIEW ON danecounty_wi.CallEvent TO 'danecounty_wi_user'@'%';


--Specific View / User for Application Map usage

--- FOR US CLIENTS
CREATE OR REPLACE VIEW 
	danecounty_wi.ApplicationMapView AS
		SELECT 	tenantPsapName, 
				wirelessType, 
				landlineType, 
				voipType, 
				rTTType, 
				tDDType, 
				tDDChallengeType, 
				unknownType, 
				notFoundType, 
				sMSType, 
				totalCallTimeInSeconds, 
				callBackNumber, 
				startCallTimeToLocal, 
				endCallTimeToLocal, 
				timeStamp,  
				timeStampToLocal,  
				latitude, 
				longitude
			FROM danecounty_wi.CallSummary 
		where callDetailsIndex = 0 AND emergencyCall = 1 AND internalTransferCall = 0 AND latitude is not null AND longitude is not null;

--- FOR CANADIAN CLIENTS - where CAllBackNumber is emptied out.

CREATE OR REPLACE VIEW 
	danecounty_wi.ApplicationMapView AS
		SELECT 	tenantPsapName, 
				wirelessType, 
				landlineType, 
				voipType, 
				rTTType, 
				tDDType, 
				tDDChallengeType, 
				unknownType, 
				notFoundType, 
				sMSType, 
				totalCallTimeInSeconds, 
				NULL AS callBackNumber, 
				startCallTimeToLocal, 
				endCallTimeToLocal, 
				timeStamp,  
				timeStampToLocal,  
				latitude, 
				longitude
			FROM danecounty_wi.CallSummary 
		where callDetailsIndex = 0 AND emergencyCall = 1 AND internalTransferCall = 0 AND latitude is not null AND longitude is not null;

--isolated user for the Application Map View.
CREATE USER 'danecounty_wi_applicationmapview_user'@'%' IDENTIFIED BY 'Solacom';

GRANT Select ON danecounty_wi.ApplicationMapView TO 'danecounty_wi_applicationmapview_user'@'%';
GRANT SHOW VIEW ON danecounty_wi.ApplicationMapView TO 'danecounty_wi_applicationmapview_user'@'%';