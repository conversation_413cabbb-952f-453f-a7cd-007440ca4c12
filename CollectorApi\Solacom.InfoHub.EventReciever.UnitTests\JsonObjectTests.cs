using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Solacom.InfoHub.EventReceiver.BusinessLogic;
using Solacom.InfoHub.EventReceiver.Exceptions;
using EventLog = Solacom.InfoHub.EventReceiver.Entities.EventLog;
using Solacom.InfoHub.EventReceiver.Entities;
using Newtonsoft.Json;

namespace Solacom.InfoHub.EventReciever.UnitTests
{
    [TestClass]
    public class JsonObjectTests
    {        
        /// <summary>
        /// This test confirms Startcall parsing, even when there is a populated Location node (which is ignored in final Startcall object)
        /// </summary>
        /// <remarks>https://solacomtech.atlassian.net/browse/INFO-1742 source bug</remarks>
        [TestMethod]
        public void StartCall_Deserilize_INFO1742_locationNode()
        {
            string jsonString = @"{
        ""Ani"": ""123456"",
        ""Dnis"": ""654321"",
        ""Esrn"": ""."",
        ""Pani"": ""."",
        ""Header"": ""INVITE sip:12356@*********** SIP/2.0  Via: SIP/2.0/UDP *******:5060;branch=z9hG4bK833439214  From: \""Doe., John\"" < sip:123456@0.0.0.0:5555>;tag=azXXXXXXXXXXX  
                        To: \""User 5555551234\"" < sip:123456@*******:5060>  Call-ID: 54321@0.0.0.0  CSeq: 55555 INVITE  Contact: <sip:5555551234@0.0.0.0:5555>  Supported: 100rel  
                        Max-Forwards: 70  User-Agent: xxxxxxx/0.0.0RC1  Allow: INVITE, ACK, CANCEL, BYE, REFER, INFO, NOTIFY, PRACK, OPTIONS, UPDATE  Content-Type: application/sdp  Content-Length:   295"",
        ""Circuit"": ""55 /00/00/0000"",
        ""CallType"": ""Admin"",
        ""Location"": {
                ""Presence"": null
        },
        ""AniDomain"": ""0.0.0.0"",
        ""CircuitId"": ""123456789"",
        ""CallerName"": ""n /a"",
        ""DnisDomain"": ""0.0.0.0"",
        ""MediaLabel"": ""_ML_55555555555555 @CUSTOMER_ID"",
        ""TrunkGroupId"": ""000"",
        ""SignallingType"": ""VOIP"",
        ""IncomingCallPolicy"": ""555_TO_XYZ_ROUTE""
      }";

            StartCall jsonObj = JsonConvert.DeserializeObject<StartCall>(jsonString);

            Assert.AreEqual(jsonObj.Ani, "123456");

        }

    }
}
