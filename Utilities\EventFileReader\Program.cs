﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using EventsDataLayer;
using Solacom.InfoHub.EventReceiver.BusinessLogic;
using Solacom.InfoHub.EventReceiver.Entities;

namespace EventFileReader
{
    class Program
    {
        public static async Task Main(string[] args)
        {
            var apiErrorFileName = @"C:\Temp\EventFileReader\api_errors.csv";
            var errorFileName = @"C:\Temp\EventFileReader\errors.csv";

            var mode = args[0];
            if(mode == "read")
            {
                var fileNames = args[1].Split(',',StringSplitOptions.RemoveEmptyEntries);// @"C:\Temp\Dane\Dane_Nov24_Nov27.csv";
                var clientCode = args[2];

                string s = string.Empty;
                var sb = new StringBuilder();
                int counter = 0;

                foreach (var fileName in fileNames)
                {
                    using (StreamReader sr = File.OpenText(fileName.Trim()))
                    {
                        while ((s = sr.ReadLine()) != null)
                        {
                            if (s == "<LogEvents>" || s == "</LogEvents>" || s == "")
                            {
                                continue;
                            }

                            if (s.StartsWith("<LogEvent xmlns"))
                            {
                                sb.AppendLine(s);
                            }
                            else if (s == "</LogEvent>")
                            {
                                sb.AppendLine(s);
                                EventLog data;
                                var xmlContent = sb.ToString();
                                try
                                {
                                    data = XmlHelper.DeserializeXml<EventLog>(xmlContent);

                                    using var context = new EventsDbModel();
                                    var eventEntity = new Event
                                    {
                                        CallId = data.callIdentifier,
                                        TimeStamp = data.timestamp,
                                        XmlContent = xmlContent,
                                        ClientCode = clientCode,
                                        IsLastPushSuccess = false,
                                        LastPushTimeStamp = null
                                    };
                                    context.Events.Add(eventEntity);

                                    await context.SaveChangesAsync();
                                    counter++;

                                    Console.WriteLine(
                                        $"# of events processed: {counter}, database id of last record: {eventEntity.Id}");
                                }
                                catch (Exception e)
                                {
                                    Console.WriteLine($"Error ..........");
                                    File.AppendAllText(errorFileName, xmlContent + Environment.NewLine);
                                    File.AppendAllText(errorFileName, e.Message + Environment.NewLine);
                                }

                                finally
                                {
                                    sb.Clear();
                                }

                            }
                            else
                            {
                                sb.AppendLine(s);
                            }

                        }
                    }
                }
            }

            else if (mode == "push")
            {
                var clientCode = args[1];
                var apiUrl = $"http://localhost:62080/api/events/{clientCode}?userId=f1d78b47ad2d42838af07fab70114aea&rawApiKey=Tzs1s/0n0WgBRhGDfCP8cfw2H6kl4HYJ";

                //Get all ids first
                var eventIds = new List<Event>();

                

                var processed = 0;

                if (args.Length > 2)
                {
                    processed = int.Parse(args[2]);
                }

                var c = 100;
                using (var context = new EventsDbModel())
                {
                    eventIds = await FetchRecords(context, processed, c);

                    while (eventIds.Any())
                    {
                        foreach (var ev in eventIds)
                        {
                            if (ev.ClientCode != clientCode || ev.IsLastPushSuccess == true)
                            {
                                continue;
                            }

                            using (var client = new HttpClient())
                            {
                                var httpContent = new StringContent(ev.XmlContent, Encoding.UTF8, "application/xml");

                                ev.LastPushTimeStamp = DateTime.Now;

                                var response = await client.PostAsync(new Uri(apiUrl), httpContent);

                                string responseMessage;
                                if (response.StatusCode != HttpStatusCode.OK)
                                {
                                    File.AppendAllText(apiErrorFileName,
                                        $"Error in pushing data for Id: {ev.Id}, Xml: {ev.XmlContent}, response: {response.ToString()}" +
                                        Environment.NewLine);

                                    ev.IsLastPushSuccess = false;
                                    responseMessage = response.ToString();
                                }
                                else
                                {
                                    ev.IsLastPushSuccess = true;
                                    responseMessage = response.StatusCode.ToString();
                                }

                                ev.LastPushMessage = responseMessage;
                                await context.SaveChangesAsync();

                                Console.WriteLine($"Pushed event with Id: {ev.Id} with response: {responseMessage}");
                            }
                        }

                        //eventIds.Clear();
                        processed = processed + c;
                        eventIds = await FetchRecords(context, processed, c);
                    }
                }
            }
            //            string contents = File.ReadAllText(fileName);
            //            var pattern = @"\<LogEvent .*>((.|\n)*?)<\/LogEvent>";
            //            var matches = Regex.Matches(contents, pattern);
            //            Console.WriteLine($"Total events found {matches.Count}");
            //
            //            foreach (Match match in matches)
            //            {
            //                try
            //                {
            //                    var data = XmlHelper.DeserializeXml<EventLog>(match.Value);
            //                }
            //                catch (Exception e)
            //                {
            //                    Console.WriteLine("Error in deserialzing");
            //                    File.AppendAllText(@"C:\Temp\errors.csv",
            //                        match.Value + Environment.NewLine);
            //                }
            //            }

            Console.WriteLine($"Completed file");
            Console.ReadKey();
        }

        private static async Task<List<Event>> FetchRecords(EventsDbModel context, int processed, int c)
        {
            var max = processed + c;

            Console.WriteLine($"Fetching records, skipping {processed}, fetching {max}");
            var data = await context.Events.Where(e=>e.Id > processed && e.Id <= max).ToListAsync();

            return data;
        }
    }
}
