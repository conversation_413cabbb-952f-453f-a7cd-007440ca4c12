﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Solacom.InfoHub.EventReceiver.Entities;

namespace Solacom.InfoHub.EventReceiver.AppService.Interfaces
{
    public interface IEventsService
    {
        Task<SaveEventResult> SaveEvent(string eventDataXML, EventLog eventLog, UserKeyData userKeyData);
        Task<bool> ProcessCall(RootEvent rootEvent, string clientCode, System.Collections.Generic.Dictionary<string, string> tenantLookup, NodaTime.DateTimeZone clientTimezone, int maxEventId, bool expiredProcessingOccurrence = false);

        Task CleanUpEventHash(string eventData, string clientCode);
    }
}