﻿namespace Solacom.InfoHub.EventReceiver.ElasticSearch.Entities
{
    public class VoiceQOS
    {
        public string MediaIpSourceAddr { get; set; }
        public string MediaIpDestAddr { get; set; }
        public string MediaUdpRtpSourcePort { get; set; }
        public string MediaUdpRtpDestPort { get; set; }
        public string MediaNumOfIpPktRxed { get; set; }
        public string MediaNumOfIpPktTxed { get; set; }
        public string MediaNumOfIpErroredPktRxed { get; set; }
        public string MediaNumOfRtpPktRxed { get; set; }
        public string MediaNumOfRtpPktTxed { get; set; }
        public string MediaNumOfRtpPktLost { get; set; }
        public string MediaNumOfRtpPktDiscarded { get; set; }
        public string MediaRtpJitter { get; set; }
        public string MediaRtpLatency { get; set; }
        public string MediaNumOfRtcpPktRxed { get; set; }
        public string MediaNumOfRtcpPktTxed { get; set; }
        public string MediaFarEndPacketLostPercentage { get; set; }
        public string MediaFarEndCumulativePacketLost { get; set; }
        public string MediaFarEndInterarrivalJitter { get; set; }
    }
}