﻿using Newtonsoft.Json;
using Solacom.InfoHub.EventReceiver.ElasticSearch.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace Solacom.InfoHub.EventReceiver.MariaDb.Context.Models
{
    public class AgentSessionRecord
    {
        public AgentSessionRecord()
        {
            AgentBusiedRecords = new List<AgentBusiedRecord>();
            AgentAvailableRecords = new List<AgentAvailableRecord>();
            _agentAudit = new List<AgentAudit>();
        }
        public int Id { get; set; }
        public float? dutyTimeMinutes { get; set; }
        public float? availableTimeMinutes { get; set; }
        public float? busiedOutTimeMinutes { get; set; }
        public string MediaLabel { get; set; }
        public DateTime? Logintime { get; set; }
        public DateTime? Logouttime { get; set; }
        public DateTime TimeStamp { get; set; }
        public int? isOnDuty { get; set; }
        public int? isAvailable { get; set; }
        public string PsapName { get; set; }
        public string AgentName { get; set; }
        public string Uri { get; set; }
        public string AgentRole { get; set; }
        public string TenantGroup { get; set; }
        public string OperatorId { get; set; }
        public string Workstation { get; set; }
        public string Devicename { get; set; }
        public string ResponseCode { get; set; }
        public string Reason { get; set; }
        public Guid CloudId { get; set; }
        public string ElasticEntities { get; set; }
        [NotMapped]
        private List<AgentAudit> _agentAudit;
        public virtual ICollection<AgentBusiedRecord> AgentBusiedRecords { get; set; }      //Do we really need this?
        public virtual ICollection<AgentAvailableRecord> AgentAvailableRecords { get; set; }        //Do we really need this?
        public void AddAgentAudit(AgentAudit agentaudit)
        {
            _agentAudit.Add(agentaudit);
            ElasticEntities = JsonConvert.SerializeObject(_agentAudit);
        }

        private List<AgentAudit> GetAgentAudit()
        {
            if (!_agentAudit.Any() && !string.IsNullOrEmpty(ElasticEntities))
            {
                _agentAudit = JsonConvert.DeserializeObject<List<AgentAudit>>(ElasticEntities);
            }
            return _agentAudit;
        }

        public void UpdateAgentAudit(AgentAudit agentaudit)
        {
            ElasticEntities = JsonConvert.SerializeObject(_agentAudit);
        }

        public AgentAudit FindAgentAudit(Guid id)
        {
            return GetAgentAudit().Find(c => c.Id == id);
        }
    }
}
