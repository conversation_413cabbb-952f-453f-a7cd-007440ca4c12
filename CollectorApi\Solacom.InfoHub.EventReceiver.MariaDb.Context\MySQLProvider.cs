﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;
using Solacom.InfoHub.EventReceiver.Entities;
using Solacom.InfoHub.EventReceiver.MariaDb.Context.Interfaces;
using Solacom.InfoHub.EventReceiver.MariaDb.Context.Models;

namespace Solacom.InfoHub.EventReceiver.MariaDb.Context
{
    public class MySQLProvider : IDataProvider
    {
        //public string ClientId { get; set; }
        public string ConnectionString { get; set; }

        public MySQLProvider(string connectionString)
        {
            //ClientId = clientId;
            ConnectionString = connectionString;
        }
       

        /// <summary>
        /// Retrieves a given Hash event
        /// </summary>
        /// <param name="hashedEvent">Hashed key to search on</param>
        /// <param name="clientCode">Unique Client idendifier</param>
        /// <returns>Event Hash object if found, null if not</returns>
        /// <exception cref="Exception"></exception>
        public async Task<Eventhash> GetHashEvent(string hashedEvent, string clientCode)
        {
            string jsonStr = string.Empty;

            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                jsonStr = dal.GetHashedEvent(hashedEvent, clientCode);
            }

            if (string.IsNullOrEmpty(jsonStr))
            {
                return null;
            }

            return JsonConvert.DeserializeObject<Eventhash>(jsonStr);
        }
        /// <summary>
        /// Adds a hashed record to the system.  If the hashed key already exists, no action is taken. 
        /// </summary>
        /// <param name="eventHash">Hashed object</param>
        /// <exception cref="Exception"></exception>
        public async Task UpdateHashEvent(Eventhash eventHash)
        {
            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                dal.AddHashEvent(eventHash.Clientcode, eventHash.Hashedcontent, JsonConvert.SerializeObject(eventHash));
            }
        }
        /// <summary>
        /// Delete a specific hash record
        /// </summary>
        /// <param name="hashedContent">Hashed key to delete</param>
        /// <param name="clientCode">Unique Client idendifier</param>
        /// <exception cref="Exception"></exception>
        public async Task DeleteHashEvent(string hashedContent, string clientCode)
        {
            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                dal.DeleteHashedEvent(hashedContent, clientCode);
            }
        }
        /// <summary>
        /// Adds a Agent Session
        /// </summary>
        /// <param name="agentSessionRecord">Agent session record</param>
        /// <param name="clientCode">Unique Client idendifier</param>
        /// <exception cref="Exception"></exception>
        public async Task UpdateAgentSession(AgentSessionRecord agentSessionRecord, string clientCode)
        {
            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                dal.AddAgentSession(clientCode, agentSessionRecord?.MediaLabel, JsonConvert.SerializeObject(agentSessionRecord));
            }
        }
        /// <summary>
        /// Retrieves a agent session
        /// </summary>
        /// <param name="mediaLabel">Media label</param>
        /// <param name="clientCode">Unique Client idendifier</param>
        /// <returns>Agent Session record, null if not found.</returns>
        /// <exception cref="Exception"></exception>
        public async Task<AgentSessionRecord> GetAgentSession(string mediaLabel, string clientCode)
        {
            string jsonStr = string.Empty;

            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                jsonStr = dal.GetAgentSession(mediaLabel, clientCode);
            }
            if (string.IsNullOrEmpty(jsonStr))
            {
                return null;
            }

            return JsonConvert.DeserializeObject<AgentSessionRecord>(jsonStr);
        }
        /// <summary>
        /// Adds a Event. 
        /// </summary>
        /// <param name="eventLog">Event Log object</param>
        /// <exception cref="Exception"></exception>
        /// <remarks>If Call identifier is null/empty, the record is not added. (based on original redis logic)</remarks>
        public async Task<int> AddEvent(string clientCode, EventLog eventLog)
        {
            if (string.IsNullOrEmpty(eventLog.callIdentifier))
            {
                return -1;
            }

            int rtnEventId = 0;

            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                rtnEventId = dal.AddEvent(eventLog.callIdentifier, clientCode, eventLog.eventType, JsonConvert.SerializeObject(eventLog), 1);
            }

            //Original logic was to append the Events under one CallIdentifier - since it is now a DB, 
            //that is not required, and each event will present under the same Call identifier in the Table
            //When retrieved for usage later, it returns back the collection of events for a given Call identifier as expected.
            return rtnEventId;
        }

        /// <summary>
        /// Retrieves the count for each unique Eventtype from the given call.  
        /// </summary>
        /// <param name="callIdentifier">unique call identifier</param>
        /// <returns>Lookup of unique Events and the counts of each, with the maximum state and id for processing awareness</returns>
        public async Task<(Dictionary<string, int>, int, int)> GetEventTypeCount(string callIdentifier, string clientId, int maxEventId)
        {
            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                return dal.GetEventTypeCount(callIdentifier, clientId, maxEventId);
            }
        }

        /// <summary>
        /// retrievs all events for a given Call identifier for processing up to a Maxiumum id.  Setting these events in processing state.
        /// </summary>
        /// <param name="callIdentifier">Unique call identifier</param>
        /// <param name="maxEventId">Maximum Event Id to base the data set from</param>
        /// <returns>collection of events, empty list if none found.</returns>
        /// <exception cref="Exception"></exception>
        public async Task<List<EventLog>> GetEventsForProcessing(string callIdentifier, string clientId, int maxEventId)
        {
            List<EventLog> eventList = new List<EventLog>();
            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                eventList = dal.GetEventsForProcessing(callIdentifier, clientId, maxEventId);
            }

            return eventList;
        }

        /// <summary>
        /// Retrieves Call Identifiers for any Events that are in NEW state that have not been processed older than a number of hours
        /// </summary>
        /// <param name="olderthanHours">Older than number of hours</param>
        /// <returns>Collection of Call ids and associated Client Ids</returns>
        /// <exception cref="Exception"></exception>
        public async Task<IList<CallInstance>> GetExpiredEventsCallId(int olderthanHours)
        {
            IList<CallInstance> rtnCallLookup = new List<CallInstance>();
            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                rtnCallLookup = dal.GetExpiredEventsCallId(olderthanHours);
            }

            return rtnCallLookup;
        }

        /// <summary>
        /// Sets Events state to Processed (StateId = 3) for a given Call identifier up to a Maxiumum id.
        /// </summary>
        /// <param name="callIdentifier">Unique call identifier</param>
        /// <param name="clientId">Unique Client id</param>
        /// <param name="maxEventId">Maximum Event Id to base the data set from</param>
        /// <returns>Count of records updated for tracing</returns>
        /// <exception cref="Exception"></exception>
        public async Task<long> SetEventProcessed(string callIdentifier, string clientId, int maxEventId)
        {
            long eventsUpdateCount = 0;
            using(DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                eventsUpdateCount = dal.SetEventState(callIdentifier, clientId, maxEventId, 3);
            }

            return eventsUpdateCount;
        }


        /// <summary>
        /// Sets Events state to Error (StateId = 4) for a given Call identifier up to a Maxiumum id.
        /// </summary>
        /// <param name="callIdentifier">Unique call identifier</param>
        /// <param name="clientId">Unique Client id</param>
        /// <param name="maxEventId">Maximum Event Id to base the data set from</param>
        /// <returns>Count of records updated for tracing</returns>
        /// <exception cref="Exception"></exception>
        public async Task<long> SetErrorEventState(string callIdentifier, string clientId, int maxEventId)
        {
            long eventsUpdateCount = 0;
            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                eventsUpdateCount = dal.SetEventState(callIdentifier, clientId, maxEventId, 4);
            }
            return eventsUpdateCount;
        }
        /// <summary>
        /// Sets Events state to Expired (StateId = 5) for a given Call identifier up to a Maxiumum id.
        /// </summary>
        /// <param name="callIdentifier">Unique call identifier</param>
        /// <param name="clientId">Unique Client id</param>
        /// <param name="maxEventId">Maximum Event Id to base the data set from</param>
        /// <returns>Count of records updated for tracing</returns>
        /// <exception cref="Exception"></exception>
        public async Task<long> SetExpiredEventState(string callIdentifier, string clientId, int maxEventId)
        {
            long eventsUpdateCount = 0;
            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                eventsUpdateCount = dal.SetEventState(callIdentifier, clientId, maxEventId, 5);
            }
            return eventsUpdateCount;
        }

        /// <summary>
        /// Sets Call Identifier to the Process Queue
        /// </summary>
        /// <param name="callIdentifier">Unique call identifier</param>
        /// <param name="clientId">Unique Client id</param>
        /// <remarks>The underlying Procedure updates the date if already present in the queue</remarks>
        /// <exception cref="Exception"></exception>
        public async Task SetProcessQueue(string callIdentifier, string clientId)
        {
            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                dal.SetProcessQueue(callIdentifier, clientId);
            }
        }

        /// <summary>
        /// Retrieves Call Identifiers for any calls that are in the process queue awaiting processing based on time span passed
        /// </summary>
        /// <param name="olderthanHours">Older than number of hours</param>
        /// <returns>Collection of Call ids and associated Client Ids (CallIdentifier, ClientId)</returns>
        /// <exception cref="Exception"></exception>
        public async Task<IList<CallInstance>> GetProcessQueueOlderThan(int olderthanHours)
        {
            IList<CallInstance> rtnCallLookup = new List<CallInstance>();
            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                rtnCallLookup = dal.GetProcessQueueOlderThan(olderthanHours);
            }

            return rtnCallLookup;
        }

        /// <summary>
        /// Deletes Call Identifier in the Process Queue
        /// </summary>
        /// <param name="callIdentifier">Unique call identifier</param>
        /// <param name="clientId">Unique Client id</param>
        /// <exception cref="Exception"></exception>
        public async Task DeleteProcessQueue(string callIdentifier, string clientId)
        {
            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                dal.DeleteProcessQueue(callIdentifier, clientId);
            }
        }

        /// <summary>
        /// Stores the basic information of a error source
        /// </summary>
        /// <param name="callId">Unique call identifier</param>
        /// <param name="clientCode">Unique client short code.</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        /// <remarks>Stores the Client and Call Id - no other data?- can be refactored to include more informative data elements.
        ///          TODO: change the data structure to take in field data for Client / call Id - and store the event data instead of just message
        ///          NOTE: On purpose leaving it to NOT leverage the fields in the DB to better flag this refactoring improvement
        ///</remarks>
        public async Task LogError(string clientCode, string callId)
        {
            if (string.IsNullOrEmpty(callId))
            {
                return;
            }

            var data = $"{clientCode}+{callId}";
            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                dal.AddProcessError(data);
            }
        }
        /// <summary>
        /// Deletes a error record 
        /// </summary>
        /// <param name="data">Error record identifier</param>
        /// <exception cref="Exception"></exception>
        /// <remarks>TODO: upon refactoring, should delete based on the unique identifiers no the Data message.</remarks>
        public async Task DeleteErrorEvent(string data)
        {
            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                dal.DeleteProcessError(data);
            }
        }
        /// <summary>
        /// Retrieves all error events available
        /// </summary>
        /// <returns>Collection of error event identifier strings - empty collection if none are found</returns>
        /// <exception cref="Exception"></exception>
        public async Task<List<string>> GetErrorEvents()
        {
            List<string> errorList = new List<string>();

            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                errorList = dal.GetProcessErrorList();
            }

            return errorList;
        }

        /// <summary>
        /// Deletes data from the identified tables based on the Olderthan parameters.  Excludes any records in ErrorState (StateId=4)
        /// </summary>
        /// <param name="olderThan">Older than number of hours</param>
        /// <exception cref="exception"></exception>
        public async Task CleanUpTablesHashEvents(int olderThan)
        {
            using(DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                dal.CleanUpTablesHashedEvents(olderThan);
            }
        }
        /// <summary>
        /// Deletes data from the identified tables based on the Olderthan parameters.  Excludes any records in ErrorState (StateId=4)
        /// </summary>
        /// <param name="olderThan">Older than number of hours</param>
        /// <exception cref="exception"></exception>
        public async Task CleanUpTablesEvents(int olderThan)
        {
            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                dal.CleanUpTablesEvents(olderThan);
            }
        }
       
        /// <summary>
        /// Deletes data from the identified tables based on the Olderthan parameters.  Excludes any records in ErrorState (StateId=4)
        /// </summary>
        /// <param name="olderThan">Older than number of hours</param>
        /// <exception cref="exception"></exception>
        public async Task CleanUpTablesAgentSession(int olderThan)
        {
            using (DAL.DAL dal = new DAL.DAL(this.ConnectionString))
            {
                dal.CleanUpTablesAgentSession(olderThan);
            }
        }

    }
}