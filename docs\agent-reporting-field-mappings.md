# Agent Reporting System - Field Mappings & Calculations

## Executive Summary

This document provides detailed field mappings for each of the 5 ACD/Agent reports, explaining how each metric is calculated from the star schema and what agent events are required.

## Agent Event Processing Requirements

### Required Agent Events
1. **Login** - Agent logs into the system
2. **Logout** - Agent logs out of the system  
3. **AgentAvailable** - Agent becomes available for calls
4. **AgentBusiedOut** - Agent becomes unavailable (with reason codes)
5. **ACDLogin** - Agent logs into specific ACD queue
6. **ACDLogout** - Agent logs out of specific ACD queue

### Event Processing Flow
```
Agent Event → fact_agent_state (real-time) → fact_agent_intervals (aggregated) → Reports
```

## Report 1: ACD - Detailed Calls by Group

### Purpose
Provides comprehensive call volume and agent utilization metrics by ACD group/queue.

### Field Mappings

| RFP Field | Friendly Name | Data Source | Calculation |
|-----------|---------------|-------------|-------------|
| acdRingGroup | Ring Group | dim_queue.ring_group_name | Direct mapping from ACDLogin events |
| callsAnswered | Answered | fact_agent_intervals.calls_answered | SUM(calls_answered) grouped by queue |
| transferredIn | Transferred In | fact_call.is_transferred + transfer_from | COUNT where transfer_from != current_queue |
| transferredOut | Transferred Out | fact_call.is_transferred + transfer_to | COUNT where transfer_to != current_queue |
| loggedInTime | Staffed Time | fact_agent_intervals.logged_in_time_seconds | SUM(logged_in_time_seconds) converted to HH:MM:SS |
| availableTime | Available Time | fact_agent_intervals.available_time_seconds | SUM(available_time_seconds) converted to HH:MM:SS |
| wrapupTime | Wrap-up Time | fact_call.wrap_up_time_seconds | SUM from call data, calculated as post-call work time |
| talkTime | Talk Time | fact_call.talk_time_seconds | SUM(talk_time_seconds) converted to HH:MM:SS |
| handlingTime | Handling Time | talkTime + wrapupTime | Combined talk and wrap-up time |
| reasonCode | Reason Code | fact_agent_state.reason_code | From AgentBusiedOut events |
| agentsLoggedIn | Agents Logged In | DISTINCT dim_agent.agent_name | Agents who had logged_in_time > 0 in period |
| avgAgentsLoggedIn | Average Agents | AVG(agent_count) | Average number of agents logged in per hour |
| callsAnsweredIn10s% | Service Level | fact_agent_intervals.calls_answered_within_10s | (SUM(calls_answered_within_10s) / SUM(calls_answered)) * 100 |

### Key Calculations

#### Staffed Time (loggedInTime)
```sql
SUM(
  CASE WHEN fas.is_logged_in = true 
  THEN fas.state_duration_seconds 
  ELSE 0 END
) as logged_in_time_seconds
```

#### Available Time
```sql
SUM(
  CASE WHEN fas.is_available = true AND fas.is_logged_in = true
  THEN fas.state_duration_seconds 
  ELSE 0 END
) as available_time_seconds
```

#### Service Level Calculation
```sql
ROUND(
  (SUM(fai.calls_answered_within_10s)::decimal / 
   NULLIF(SUM(fai.calls_answered), 0)) * 100, 2
) as service_level_percent
```

## Report 2: ACD - Call Queue Summary Dashboard

### Purpose
High-level dashboard showing queue performance metrics and utilization.

### Field Mappings

| Friendly Name | Database Field | Calculation |
|---------------|----------------|-------------|
| Queue Name | dim_queue.ring_group_name | Direct from queue dimension |
| Calls | fact_agent_intervals.calls_answered | SUM(calls_answered) by queue |
| Calls Answered in 10s% | callsAnsweredIn10s% | Service level calculation |
| Abandoned | fact_call.is_abandoned | COUNT where is_abandoned = true |
| Logged in Time | loggedInTime | SUM(logged_in_time_seconds) |
| Agents Logged In | agentsLoggedIn | COUNT(DISTINCT agent_key) where logged_in_time > 0 |
| Group Utilization% | groupUtilization | (available_time / logged_in_time) * 100 |

### Key Calculations

#### Group Utilization
```sql
ROUND(
  (SUM(fai.available_time_seconds)::decimal / 
   NULLIF(SUM(fai.logged_in_time_seconds), 0)) * 100, 2
) as group_utilization_percent
```

## Report 3: ACD - Call Taking Group Overview

### Purpose
Executive dashboard showing high-level ACD group efficiency metrics.

### Field Mappings

| Database Field | Calculation | Source Events |
|----------------|-------------|---------------|
| acdGroup | dim_queue.ring_group_name | ACDLogin events |
| calls | COUNT(fact_call.call_key) | Call events + agent association |
| callsAnsweredWithin10s/15s/20s/40s | Service level breakdowns | Call timing data |
| callsAbandoned | COUNT where is_abandoned = true | Call events |
| callsTransferred | COUNT where is_transferred = true | Call events |
| loggedInTime | SUM(logged_in_time_seconds) | Login/Logout events |
| availableTime | SUM(available_time_seconds) | Available/BusiedOut events |
| groupUtilization% | (availableTime / loggedInTime) * 100 | Calculated from above |

### Service Level Breakdown Calculation
```sql
-- Multiple service level thresholds
SUM(CASE WHEN fc.agent_time_to_answer_seconds <= 10 THEN 1 ELSE 0 END) as answered_within_10s,
SUM(CASE WHEN fc.agent_time_to_answer_seconds <= 15 THEN 1 ELSE 0 END) as answered_within_15s,
SUM(CASE WHEN fc.agent_time_to_answer_seconds <= 20 THEN 1 ELSE 0 END) as answered_within_20s,
SUM(CASE WHEN fc.agent_time_to_answer_seconds <= 40 THEN 1 ELSE 0 END) as answered_within_40s
```

## Report 4: Agent Performance - Call Distribution

### Purpose
Individual agent performance with shift-based calculations and service levels.

### Field Mappings

| RFP Field | Database Field | Calculation | Source Events |
|-----------|----------------|-------------|---------------|
| acdGroup | dim_queue.ring_group_name | From ACDLogin events | ACDLogin |
| calls | Average calls per shift | Total calls / number of shifts | Call events |
| availableTime | Available time per shift | SUM(available_time_seconds) per shift | Available/BusiedOut |
| agentName | dim_agent.agent_name | Direct mapping | Login events |
| agentCount | Average agents per interval | COUNT(DISTINCT agent_key) | Login events |
| agentAnsweredWithin10s% | Individual service level | Agent-specific calculation | Call events |

### Shift Calculation Logic
```sql
-- Define shift as Login to Logout cycle within 24-hour period
WITH agent_shifts AS (
  SELECT 
    agent_key,
    DATE(event_timestamp_local) as shift_date,
    MIN(CASE WHEN event_type = 'Login' THEN event_timestamp END) as shift_start,
    MAX(CASE WHEN event_type = 'Logout' THEN event_timestamp END) as shift_end
  FROM fact_agent_state
  WHERE event_type IN ('Login', 'Logout')
  GROUP BY agent_key, DATE(event_timestamp_local)
  HAVING shift_start IS NOT NULL AND shift_end IS NOT NULL
)
```

### Average Calls Per Shift
```sql
-- Calculate average calls per shift for each agent
SELECT 
  da.agent_name,
  ROUND(AVG(shift_calls.call_count), 1) as avg_calls_per_shift
FROM (
  SELECT 
    fc.agent_key,
    DATE(fc.start_call_time_local) as shift_date,
    COUNT(*) as call_count
  FROM fact_call fc
  WHERE fc.call_answered = true
  GROUP BY fc.agent_key, DATE(fc.start_call_time_local)
) shift_calls
JOIN dim_agent da ON shift_calls.agent_key = da.agent_key
GROUP BY da.agent_name
```

## Report 5: Emergency Agent Performance

### Purpose
Same as Agent Performance but filtered for emergency calls only.

### Field Mappings
Same as Report 4 but with additional filter:
```sql
WHERE fact_call.is_emergency_call = true
```

### Emergency Call Identification
Emergency calls are identified from the existing callsummary table:
- `isEmergencyCall = true`
- `isAdminCall = false`

## Interval Processing Requirements

### Time Granularities Supported
- **5 minutes**: Real-time operational monitoring
- **15 minutes**: Near real-time dashboards  
- **30 minutes**: Operational reporting
- **1 hour**: Primary reporting interval for Power BI

### Interval Aggregation Process
1. **Real-time**: Process agent events as they arrive
2. **5-minute**: Aggregate every 5 minutes via scheduled Lambda
3. **15-minute**: Roll up from 5-minute intervals
4. **30-minute**: Roll up from 15-minute intervals  
5. **1-hour**: Roll up from 30-minute intervals

### Available Time Calculation Detail
Available time is calculated as the sum of time when an agent is:
- Logged in (`is_logged_in = true`)
- Available for calls (`is_available = true`)
- Not in a busied out state
- Includes time on calls and wrap-up time

```sql
-- Available time calculation
SUM(
  CASE 
    WHEN fas.is_logged_in = true 
    AND fas.is_available = true 
    AND (fas.reason_code IS NULL OR fas.reason_code NOT IN ('BREAK', 'TRAINING', 'MEETING'))
    THEN fas.state_duration_seconds 
    ELSE 0 
  END
) as available_time_seconds
```

## Data Quality Considerations

### Event Validation Rules
1. **Login before Logout**: Cannot logout without prior login
2. **ACDLogin requires Login**: Cannot ACD login without system login
3. **State Transitions**: Available/BusiedOut require active login
4. **Duplicate Detection**: Same event type within 1 second window
5. **Temporal Validation**: Events must be chronologically ordered

### Data Reconciliation
- Daily reconciliation between agent events and call data
- Validation of shift calculations against business rules
- Monitoring for missing events or data gaps
- Automated alerts for data quality issues

## Performance Optimization

### Redshift Optimization
- **Distribution Key**: `tenant_key` for multi-tenant isolation
- **Sort Keys**: `event_timestamp`, `agent_key` for time-series queries
- **Compression**: Appropriate encoding for all columns
- **Partitioning**: Monthly partitions for fact tables

### Query Optimization
- **Materialized Views**: Pre-aggregated data for common queries
- **Columnar Storage**: Optimized for analytical workloads
- **Index Strategy**: Covering indexes for report queries
- **Query Caching**: Power BI query result caching

This field mapping document provides the detailed technical specifications needed to implement each report accurately according to the RFP requirements.
