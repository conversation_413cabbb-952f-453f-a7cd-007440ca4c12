{"Logging": {"LogLevel": {"Default": "Debug", "System": "Warning", "Microsoft": "Warning"}, "elasticsearchSettings": {"url": "http://localhost:9200", "userName": "", "password": ""}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "Properties": {"Environment": "Local"}}, "clientSettings": {"clients": "butler-oh,client,danecounty-wi,godzilla,lexus,thurston-wa,wiregrass-al,solacom,sanramon-ca,lethbridge-ab,barrie-on,bonneville-id,ashley-ar,wasilla-ak,pvgt,pvgtlg,strathcona-ab,foothills-ab,woodstock-on,flagler-fl,roanecounty-wv,clinton-oh,brandon-mb,nicholascounty-wv,das-oh", "clientcodeIndexPrefix": {"buoh": "butler-oh", "client": "client", "dcwi": "danecounty-wi", "gz": "godzilla", "thwa": "thurston-wa", "wial": "wiregrass-al", "solacom": "solacom", "srca": "sanramon-ca", "leab": "lethbridge-ab", "baon": "barrie-on", "boid": "bonneville-id", "asar": "ashley-ar", "lexus": "lexus", "waak": "wasilla-ak", "pvgt": "pv-gatineau", "pvlggt": "pv-legacy-gatineau", "foab": "foothills-ab", "woon": "woodstock-on", "flfl": "flagler-fl", "rowv": "roanecounty-wv", "cloh": "clinton-oh", "brmb": "brandon-mb", "niwv": "nicholascounty-wv", "daoh": "das-oh"}, "clientTenantMapping": {"dcwi": {"danewi": "<PERSON><PERSON><PERSON>", "training": "training"}, "wial": {"covington": "covington", "crenshaw": "crenshaw", "geneva": "geneva", "enterprise": "enterprise", "coffee": "coffee", "headland": "headland", "bullock": "bullock", "ozark-dale": "ozark-dale", "houston": "houston", "dothan": "<PERSON>han", "wiregrass": "wiregrass", "butler": "butler", "daleville": "daleville", "abbeville": "abbeville"}, "thwa": {"cresa": "cresa", "tcomm": "tcomm", "rivercom": "rivercom", "wahk": "wahk"}, "buoh": {"9com": "9com", "8com": "8com", "3com": "3com", "7com": "7com", "6com": "6com", "4com": "4com"}, "srca": {"tenantgroupeveryone": "tenantgroupeveryone"}, "leab": {"lethbridgeab": "lethbridgeab"}, "baon": {"barrie": "barrie"}, "boid": {"bcecc": "bcecc", "madison": "madison", "fremont": "fremont", "bearlake": "bearlake", "oneida": "oneida", "jefferson": "<PERSON><PERSON><PERSON><PERSON>", "clark": "clark", "franklin": "franklin", "butte": "butte", "custer": "custer"}, "asar": {"ashley county": "ashleycounty"}, "client": {"gatineau": "gatineau", "ottawa": "ottawa"}, "waak": {"matcom": "matcom", "palmer": "palmer", "matcom, ak": "matcom", "palmer, ak ": "palmer", "wasilla pd": "wasillapd"}, "pvgt": {"tng000": "tng000", "tng001": "tng001", "map": "map"}, "pvlggt": {"tng000": "tng000", "tng001": "tng001", "map": "map"}, "foab": {"foothillsab": "foothillsab"}, "woon": {"woodstock": "woodstock"}, "flfl": {"flaglercountyfl": "flaglercountyfl"}, "rowv": {"roane": "roane"}, "cloh": {"ccso": "ccso", "wpd": "wpd"}, "brmb": {"brandon911": "brandon911"}, "niwv": {"nicholascountywv": "nicholascountywv"}, "daoh": {"ohiodas": "oh<PERSON>das", "monroe co so": "<PERSON><PERSON><PERSON><PERSON>", "harrison so 911": "harrisonso911", "morgan co": "morganco", "columbiana co": "columbianaco", "union co so": "unioncoso", "carroll co": "carroll co", "travis": "travis", "adam": "adam"}}, "clientTimezoneMapping": {"client": "America/New_York", "pvgt": "America/New_York", "pvlggt": "America/New_York", "foab": "America/New_York", "woon": "America/New_York", "flfl": "America/New_York", "rowv": "America/New_York", "cloh": "America/New_York", "brmb": "America/Winnipeg", "niwv": "America/New_York", "daoh": "America/New_York"}, "clientEsiNetSupportedList": "pvgt,brmb,daoh"}, "eventFileLocations": {"bucketPath": ""}, "Database": {"mysql": {"connectionstring.CollectorAPI": "server=localhost;port=3306;user=Collector_User;password=*******;database=CollectorAPI", "connectionstring.InsightsData": "server=localhost;port=3306;user=Collector_User;password=*******;database=InsightsData"}}, "version": ""}