﻿using System;
using System.Xml.Serialization;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    [XmlRoot(ElementName = "EIDD")]
    public class EIDD
    {
        [XmlElement(ElementName = "EIDDBody")]
        public EIDDBody EIDDBody { get; set; }
        [XmlElement(ElementName = "direction")]
        public string Direction { get; set; }

        /// <summary>
        /// Currently a non-supported node. 
        /// </summary>
        /// <remarks>See INFO-1767 for additional information</remarks>
        [XmlElement(ElementName = "EIDDDomain")]
        [XmlIgnore]
        public string EIDDDomain { get; set; }
    }

    [XmlRoot(ElementName = "EIDDBody")]
    public class EIDDBody
    {
        [XmlElement(ElementName = "ReasonForIssueRegistryText")]
        public string ReasonForIssueRegistryText { get; set; }

        [XmlElement(ElementName = "DocumentCreationDate")]
        public DocumentCreationDate DocumentCreationDate { get; set; }

        [XmlElement(ElementName = "IssuingElementIdentification")]
        public string IssuingElementIdentification { get; set; }

        [XmlElement(ElementName = "Agent")]
        public Agent Agent { get; set; }

        [XmlElement(ElementName = "Call")]
        public Call Call { get; set; }

        /// <summary>
        /// Currently a non-supported node. 
        /// </summary>
        /// <remarks>See INFO-1767 for additional information</remarks>
        [XmlElement(ElementName = "IncidentTrackingIdentification")]
        [XmlIgnore]
        public string IncidentTrackingIdentification { get; set; }

        /// <summary>
        /// Currently a non-supported node. 
        /// </summary>
        /// <remarks>See INFO-1767 for additional information</remarks>
        [XmlElement(ElementName = "DocumentIdentification")]
        [XmlIgnore]
        public string DocumentIdentification { get; set; }
    }

    [XmlRoot(ElementName = "DocumentCreationDate")]
    public class DocumentCreationDate
    {
        [XmlElement(ElementName = "DateTime")]
        public DateTime DateTime { get; set; }
    }

    [XmlRoot(ElementName = "Agent")]
    public class Agent
    {
        [XmlElement(ElementName = "AgentIdentification")]
        public string AgentIdentification { get; set; }

        [XmlElement(ElementName = "AgentWorkstationPositionIdentification")]
        public string AgentWorkstationPositionIdentification { get; set; }

        [XmlElement(ElementName = "AgentDeviceRoleRegistryText")]
        public string AgentDeviceRoleRegistryText { get; set; }

    }

    [XmlRoot(ElementName = "Call")]
    public class Call
    {
        [XmlElement(ElementName = "CallIdentifier")]
        public string CallIdentifier { get; set; }

        [XmlElement(ElementName = "CallTypeDescriptionRegistryText")]
        public string CallTypeDescriptionRegistryText { get; set; }

        [XmlElement(ElementName = "ActivityDateRange")]
        public ActivityDateRange ActivityDateRange { get; set; }


        [XmlElement(ElementName = "CallStatusRegistryText")]
        public string CallStatusRegistryText { get; set; }


        [XmlElement(ElementName = "AdditionalData")]
        public AdditionalData AdditionalData { get; set; }

        /// <summary>
        /// Currently a non-supported node. 
        /// </summary>
        /// <remarks>See INFO-1767 for additional information</remarks>
        [XmlElement(ElementName = "Location")]
        [XmlIgnore]
        public string Location { get; set; }

    }

    [XmlRoot(ElementName = "ActivityDateRange")]
    public class ActivityDateRange
    { 
        [XmlElement(ElementName = "StartDate")]
        public DateTime StartDate { get; set; }
    }

    [XmlRoot(ElementName = "AdditionalData")]
    public class AdditionalData
    {
        [XmlElement(ElementName = "Notes")]
        public string Notes { get; set; }

        /// <summary>
        /// Currently a non-supported node. 
        /// </summary>
        /// <remarks>See INFO-1767 for additional information</remarks>
        [XmlElement(ElementName = "AdditionalDataURL")]
        [XmlIgnore]
        public string AdditionalDataURL { get; set; }

        /// <summary>
        /// Currently a non-supported node. 
        /// </summary>
        /// <remarks>See INFO-1767 for additional information</remarks>
        [XmlElement(ElementName = "AdditionalDataDetail")]
        [XmlIgnore]
        public string AdditionalAdditionalDataDetailDataURL { get; set; }
    }
}


