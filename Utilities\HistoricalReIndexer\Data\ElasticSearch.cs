﻿using System;
using System.Collections.Generic;
using System.Text;
using Nest;
using System.Linq;

namespace HistoricalReIndexer.Data
{
    class ElasticSearch : IDisposable
    {
        protected ElasticClient _ElasticClient = null;
        /// <summary>
        /// Defines the size of the data set in the query.  
        /// </summary>
        protected int _QuerySize = 500;
        /// <summary>
        /// If true, query is dumped to the console for some key queries (not all have the output behaviour)
        /// </summary>
        public bool _OutputQuery = false;

        #region Constructor
        public ElasticSearch(ConnectionSettings connectionSettings)
        {
            //var url = "http://localhost:9200"; //Configuration["elasticsearchSettings:url"];
            //var userName = "";//Configuration["elasticsearchSettings:userName"];
            //var password = "";// Configuration["elasticsearchSettings:password"];
            //var settings = new ConnectionSettings(new Uri(url));
            //settings.BasicAuthentication(userName, password);
            //settings.ThrowExceptions(alwaysThrow: true);
            //settings.PrettyJson();

            _ElasticClient = new ElasticClient(connectionSettings);
        }

        #endregion

        #region Destructor

        ~ElasticSearch()
        {
            Dispose();
        }

        public void Dispose()
        {
            GC.SuppressFinalize(this);
        }

        #endregion

        /// <summary>
        /// Main function to define the INDEX names based on the Type passed in.  
        /// </summary>
        /// <param name="indexPostfix">The appended postfix - i.e. butler-oh</param>
        /// <returns></returns>
        /// <remarks>This is a minified version of the mirror call in the main code base</remarks>
        protected string GetIndexName(string indexPostfix)
        {
            string _CallSummaryIndex = "callsummary";

            return $"{_CallSummaryIndex}_{indexPostfix}";
        }

        /// <summary>
        /// Retrieves the distinct PSAP list
        /// </summary>
        /// <param name="index">Index to query against</param>
        /// <param name="filter">Filter criteria for the date range </param>
        /// <returns>Collection of PSAPs with their doc count</returns>
        /// <remarks>It excludes the empty case / no PSAP count.</remarks>
        public Dictionary<string, long> GetDistinctPSAPList(string index, FilterCriteria filter)
        {
            Dictionary<string, long> rtnList = new Dictionary<string, long>();
            SearchDescriptor<CallSummary> searchObj;
            ISearchResponse<CallSummary> searchResults;
            
            searchObj = new SearchDescriptor<CallSummary>()
                .Size(0)
                .Index(index)
                .Query(q => q
                        .DateRange(dt => dt
                                        .Field(field => field.TimeStamp)
                                        .GreaterThanOrEquals(filter.StartDate)
                                        .LessThanOrEquals(filter.EndDate)
                                        ) && q
                        )
                .Aggregations(u => u.Terms("uniquePSAP", up => up.Field("psapName.keyword") ) );

            if (_OutputQuery)
            {
                Console.WriteLine("QUERY:");

                using (System.IO.MemoryStream ms = new System.IO.MemoryStream())
                {
                    _ElasticClient.RequestResponseSerializer.Serialize(searchObj, ms);
                    string rawQuery = Encoding.ASCII.GetString(ms.ToArray());

                    Console.WriteLine(rawQuery);
                }

                Console.WriteLine(":::::::::::::::::::::::::::::::::::::::::::");
            }

            searchResults = _ElasticClient.Search<CallSummary>(s => searchObj);
            TermsAggregate<string> bucketList = searchResults.Aggregations.Terms("uniquePSAP");

            foreach(var item in bucketList.Buckets)
            {
                if (!string.IsNullOrWhiteSpace(item.Key))   //do not add the empty psap case / empty case.
                {
                    rtnList.Add(item.Key.ToLower(), item.DocCount ?? 0);
                }
            }               
                    
            return rtnList;
        }

        /// <summary>
        /// retrieves the Psaps for each CallId record (if any)
        /// </summary>
        /// <param name="index"></param>
        /// <param name="callIdList"></param>
        /// <returns>Dictionary of Callids with the PSAP value</returns>
        public Dictionary<string, string> GetCallIdPSAPList(string index, List<string> callIdList)
        {
            Dictionary<string, string> rtnList = new Dictionary<string, string>();
            SearchDescriptor<CallSummary> searchObj;
            ISearchResponse<CallSummary> searchResults;

            searchObj = new SearchDescriptor<CallSummary>()
                .Size(0)
                .Index(index)
                .Query(q => q
                        .Bool(b => b
                            .Should(callIdList.Select(t => BuildPhraseQueryContainerForCallId(q, t)).ToArray()))
                        )
                .Aggregations(aggs => aggs
                                .Terms("uniqueCallId", uc => uc.Field("callid.keyword")
                                .Size(callIdList.Count)
                                .Aggregations(pu => pu.Terms("uniquePSAP", up => up.Field("psapName.keyword")))
                            )
                    );

            if (_OutputQuery)
            {
                Console.WriteLine("QUERY:");

                using (System.IO.MemoryStream ms = new System.IO.MemoryStream())
                {
                    _ElasticClient.RequestResponseSerializer.Serialize(searchObj, ms);
                    string rawQuery = Encoding.ASCII.GetString(ms.ToArray());

                    Console.WriteLine(rawQuery);
                }

                Console.WriteLine(":::::::::::::::::::::::::::::::::::::::::::");
            }

            searchResults = _ElasticClient.Search<CallSummary>(s => searchObj);
            TermsAggregate<string> bucketList = searchResults.Aggregations.Terms("uniqueCallId");

            string callIdString = string.Empty;
            string callPsapString = string.Empty;
            BucketAggregate psapBucketList;

            foreach (var item in bucketList.Buckets)
            {
                if( string.IsNullOrEmpty(item.Key) || item.Count == 0)  //shouldn't trigger - however, empty subobject check in case edge cases appear.
                {
                    continue;
                }
                callIdString = item.Key;
                psapBucketList = item.First().Value as BucketAggregate;
                foreach(KeyedBucket<object> psapItem in psapBucketList.Items)
                {
                    if( psapItem.Key == null)
                    {
                        continue;
                    }

                    if( !(string.IsNullOrWhiteSpace(psapItem.Key.ToString()) || psapItem.Key.ToString().Contains(",")))    //excluding the comma seperated Root element && empty elements
                    {
                        rtnList.Add(callIdString, psapItem.Key.ToString());
                        break;  //only need to add the first element
                    }
                }
                //Logic is the Call Id will be excluded if no PSAP is found - which will result in no transferring or indexing capacity.
            }

            return rtnList;
        }


        /// <summary>
        /// Retrieves the List of unique Callids for both single PSAP call summaries AND multi-Psap (transfer case) call summaries
        /// </summary>
        /// <param name="index"></param>
        /// <param name="filter"></param>
        /// <param name="callIdList"></param>
        /// <param name="callIdlist_Transfer"></param>
        public void GetCallIdLists(string index, FilterCriteria filter, out List<string> callIdList, out List<string> callIdlist_Transfer)
        {
            //Logic is, grab the distinct Callids that match the filter criteria - 
            //Than fetch the collection of Call Summaries that contain the given Call Id, resulting the full call history
            
            List<CallSummary> rtnList = new List<CallSummary>();

            callIdList = new List<string>();
            callIdlist_Transfer = new List<string>();

            int hitsReturned = 1;   //initializing the list
            int querySize = _QuerySize;
            int currentPosition = 0;
            SearchDescriptor<CallSummary> searchObj;
            ISearchResponse<CallSummary> searchResults;
            while (hitsReturned > 0)
            {
                searchObj = new SearchDescriptor<CallSummary>()
                     .Source(sf => sf
                       .Includes(i => i
                          .Field(f => f.Callid)
                          .Field(f => f.IsTransferred)
                           )
                        )
                   .From(currentPosition)
                   .Size(querySize)
                   .Index(index)
                   .Sort(
                        ss => ss
                        .Ascending(p => p.TimeStamp)
                        .Ascending(p => p.Id.Suffix("keyword"))
                    )
                   .Query(q => q
                           .DateRange(dt => dt
                                           .Field(field => field.TimeStamp)
                                           .GreaterThanOrEquals(filter.StartDate)
                                           .LessThanOrEquals(filter.EndDate)
                                           ) && q
                           .MatchPhrase(bff => bff.Field(x => x.CallDetailsId).Query("00000000-0000-0000-0000-000000000000"))
                           );

                if (_OutputQuery)
                {
                    Console.WriteLine("QUERY:");

                    using (System.IO.MemoryStream ms = new System.IO.MemoryStream())
                    {
                        _ElasticClient.RequestResponseSerializer.Serialize(searchObj, ms);
                        string rawQuery = Encoding.ASCII.GetString(ms.ToArray());

                        Console.WriteLine(rawQuery);
                    }

                    Console.WriteLine(":::::::::::::::::::::::::::::::::::::::::::");
                }

                searchResults = _ElasticClient.Search<CallSummary>(s => searchObj);
                //paging parameters
                hitsReturned = searchResults.Hits.Count;
                currentPosition += hitsReturned;

                List<CallSummary> callSummaryList = searchResults.Documents.ToList();

                foreach( CallSummary callSummary in callSummaryList )
                {
                    //transfer behaviour
                    if(callSummary.IsTransferred == 1 )
                    {
                        callIdlist_Transfer.Add(callSummary.Callid);
                    }
                    else
                    {
                        callIdList.Add(callSummary.Callid);
                    }
                }

            }
            //Next, break distinct the lists, and remove duplicates
            callIdList = callIdList.Except(callIdlist_Transfer).ToList();  //removing the transfers from the main list 
            callIdList = callIdList.Distinct().ToList();

            callIdlist_Transfer = callIdlist_Transfer.Distinct().ToList();
           
        }
                
        /// <summary>
        /// Generates the match query based on a term for CallId matching in *CallSummary index.
        /// </summary>
        /// <param name="qd">Query Container definition</param>
        /// <param name="callidString">Callid to match to</param>
        /// <returns></returns>
        /// <remarks>https://stackoverflow.com/questions/28186041/in-nest-how-do-i-dynamically-build-a-query-from-a-list-of-terms</remarks>
        private QueryContainer BuildPhraseQueryContainerForCallId(QueryContainerDescriptor<CallSummary> qd, string callidString)
        {
            return qd.MatchPhrase(m => m.Field(f => f.Callid).Query(callidString.ToLower()));
        }

        /// <summary>
        /// Retrieves a collection of Call Summary based on the distinct call Id list.
        /// </summary>
        /// <param name="index">Index to retrieve from</param>
        /// <param name="callIdList">List of Call Ids to retrieve from</param>
        /// <returns>Returns collection of Call Summary objects</returns>
        public List<CallSummary> GetCallSummaryList(string index, List<string> callIdList)
        {   
            List<CallSummary> rtnList = new List<CallSummary>();

            if (callIdList.Count == 0)
                return rtnList;

            int hitsReturned = 1;   //initializing the list
            int querySize = _QuerySize;
            int currentPosition = 0;
            SearchDescriptor<CallSummary> searchObj;
            ISearchResponse<CallSummary> searchResults;
            while (hitsReturned > 0)
            {
                searchObj = new SearchDescriptor<CallSummary>()
                  .From(currentPosition)
                  .Size(querySize)
                  .Index(index)
                  .Sort(
                        ss => ss
                        .Ascending(p => p.TimeStamp)
                        .Ascending(p => p.Id.Suffix("keyword"))
                    )
                  .Query(q => q
                        //.MatchPhrase(bff => bff.Field(x => x.Callid).Query(filter.PSAPName)) || q
                        .Bool(b => b
                            .Should(callIdList.Select(t => BuildPhraseQueryContainerForCallId(q, t)).ToArray()))
                        );

                if(_OutputQuery )
                {
                    Console.WriteLine("QUERY:");

                    using (System.IO.MemoryStream ms = new System.IO.MemoryStream())
                    {
                        _ElasticClient.RequestResponseSerializer.Serialize(searchObj, ms);
                        string rawQuery = Encoding.ASCII.GetString(ms.ToArray());

                        Console.WriteLine(rawQuery);
                    }

                    Console.WriteLine(":::::::::::::::::::::::::::::::::::::::::::");
                }

                searchResults = _ElasticClient.Search<CallSummary>(s => searchObj);

                //paging parameters
                hitsReturned = searchResults.Hits.Count;
                currentPosition += hitsReturned;

                rtnList.InsertRange(rtnList.Count, searchResults.Documents.ToList());
            }

            return rtnList;
        }

        /// <summary>
        /// Retrieves a limited list of Call Summary records for data validation logic.
        /// </summary>
        /// <param name="index"></param>
        /// <param name="callIdList"></param>
        /// <returns></returns>
        public Dictionary<string, List<CallSummaryLimited>> GetLimitedCallSummaryByCallId(string index, List<string> callIdList)
        {
            Dictionary<string, List<CallSummaryLimited>> rtnList = new Dictionary<string, List<CallSummaryLimited>>();

            if (callIdList.Count == 0)
                return rtnList;

            int hitsReturned = 1;   //initializing the list
            int querySize = _QuerySize;
            int currentPosition = 0;
            SearchDescriptor<CallSummary> searchObj;
            ISearchResponse<CallSummaryLimited> searchResults;
            while (hitsReturned > 0)
            {
                searchObj = new SearchDescriptor<CallSummary>()
                    .FilterPath("hits.hits._source", "hits.total")
                    .Source(sf => sf
                        .Includes(i => i
                            .Field(f => f.Callid)
                            .Field(f => f.CallDetailsId)
                            .Field(f => f.TimeStamp)
                            .Field(f => f.IsAlternativeRoute)
                            )
                        )
                  .From(currentPosition)
                  .Size(querySize)
                  .Index(index)
                  .Sort(
                        ss => ss
                        .Ascending(p => p.TimeStamp)
                        .Ascending(p => p.Id.Suffix("keyword"))
                    )
                  .Query(q => q
                        .Bool(b => b
                            .Should(callIdList.Select(t => BuildPhraseQueryContainerForCallId(q, t)).ToArray()))
                        );

                if (_OutputQuery)
                {
                    Console.WriteLine("QUERY:");

                    using (System.IO.MemoryStream ms = new System.IO.MemoryStream())
                    {
                        _ElasticClient.RequestResponseSerializer.Serialize(searchObj, ms);
                        string rawQuery = Encoding.ASCII.GetString(ms.ToArray());

                        Console.WriteLine(rawQuery);
                    }

                    Console.WriteLine(":::::::::::::::::::::::::::::::::::::::::::");
                }

                searchResults = _ElasticClient.Search<CallSummaryLimited>(s => searchObj);

                //paging parameters
                hitsReturned = searchResults.Hits.Count;
                currentPosition += hitsReturned;

                List<CallSummaryLimited> csList = searchResults.Documents.ToList();

                foreach(CallSummaryLimited cs in csList )
                {
                    if( !rtnList.ContainsKey(cs.Callid))
                    {
                        rtnList.Add(cs.Callid, new List<CallSummaryLimited>());
                        
                    }

                    rtnList[cs.Callid].Add(cs);
                }
            }

            return rtnList;
        }

        /*
         * Timezone required: ref: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones
         * 
         * UTC
         * EST - -05:00 - America/New_York
         * CST - -06:00 - America/Chicago
         * MST - -07:00 - America/Phoenix
         * PST - -08:00 - America/Los_Angeles
         * AKST- -09:00 - America/Anchorage
         * HAST- -10:00 - America/Adak
         * NST - -03:30 - America/St_Johns
         * AST - -04:00 - America/Puerto_Rico
         */

        /// <summary>
        /// Creates the script snippet that applying the timezone dependent time fields
        /// </summary>
        /// <param name="timeZoneId">Time zone identifier - NOTE: must enter blank to initialize the collection (aka. UTC)</param>
        /// <param name="timeZoneAbbr">Time zone abbrivation that will be the suffix added to the reindexed fields</param>
        /// <returns></returns>
        private string GetScript_TimeZoneSets(string timeZoneId, string timeZoneAbbr)
        {
            string rtnScriptString = "";

            if( string.IsNullOrEmpty(timeZoneId))
            {
                rtnScriptString = "def timeStampTZ = sourceTime;int dayOfWeekIndex = 0; def firstOfMonth = 0; def weekOfMonth = 0;";
                timeZoneId = "+00:00";  //setting the UTC timezone for later firstOfMonth script.
            }
            else
            {
                rtnScriptString = @$"timeStampTZ = sourceTime.withZoneSameInstant(ZoneId.of('{timeZoneId}'));";
            }

            rtnScriptString += @$"ctx._source['hourOfDay{timeZoneAbbr}'] = timeStampTZ.getHour();
                                ctx._source['dateOfMonth{timeZoneAbbr}'] =  timeStampTZ.getDayOfMonth();
                                dayOfWeekIndex = timeStampTZ.getDayOfWeek().getValue();
                                    if (dayOfWeekIndex == 7) {{
                                    ctx._source['dayOfWeek{timeZoneAbbr}'] = '00-Sunday';
                                    }} else if (dayOfWeekIndex == 1) {{
                                    ctx._source['dayOfWeek{timeZoneAbbr}'] = '01-Monday';
                                    }} else if (dayOfWeekIndex == 2) {{
                                    ctx._source['dayOfWeek{timeZoneAbbr}'] = '02-Tuesday';
                                    }} else if (dayOfWeekIndex == 3) {{
                                    ctx._source['dayOfWeek{timeZoneAbbr}'] = '03-Wednesday';
                                    }} else if (dayOfWeekIndex == 4) {{
                                    ctx._source['dayOfWeek{timeZoneAbbr}'] = '04-Thursday';
                                    }} else if (dayOfWeekIndex == 5) {{
                                    ctx._source['dayOfWeek{timeZoneAbbr}'] = '05-Friday';
                                    }} else if (dayOfWeekIndex == 6) {{
                                    ctx._source['dayOfWeek{timeZoneAbbr}'] = '06-Saturday';
                                    }} else {{
                                    ctx._source['dayOfWeek{timeZoneAbbr}'] = dayOfWeekIndex;
                                    }}
                                ctx._source['monthOfYear{timeZoneAbbr}'] =  timeStampTZ.getMonthValue();
                                ctx._source['weekOfYear{timeZoneAbbr}'] = timeStampTZ.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
                                firstOfMonth = ZonedDateTime.of(timeStampTZ.getYear(), timeStampTZ.getMonthValue(), 1, 22, 15, 30, 0, ZoneId.of('{timeZoneId}'));
                                weekOfMonth = timeStampTZ.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR) - firstOfMonth.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR) + 1;
                                if(weekOfMonth < 0){{
                                weekOfMonth = 52 + weekOfMonth;
                                }}
                                ctx._source['weekOfMonth{timeZoneAbbr}'] = weekOfMonth;
                                ";

            return rtnScriptString;
        }

        /// <summary>
        /// Painless script snippet to remove depricated Time Properities on the reindex action.
        /// </summary>
        /// <returns></returns>
        private string GetScript_ClearTimeProperities()
        {
            string rtnScriptString = @" ctx._source.remove('hourOfDay');
                                        ctx._source.remove('dateOfMonth');
                                        ctx._source.remove('dayOfWeek');
                                        ctx._source.remove('monthOfYear');
                                        ctx._source.remove('weekOfYear');
                                        ctx._source.remove('weekOfMonth');
                                        ";

            return rtnScriptString;
        }

        /// <summary>
        /// Reindexes a collection of Call Ids to a specific index.  Specific to Call Summary data.
        /// </summary>
        /// <param name="sourceIndex">The source index to pull data from</param>
        /// <param name="targetIndex">The target index</param>
        /// <param name="callIdList">The list of Ids to filter against / reindex</param>
        /// <returns>Reindex server response</returns>
        /// <remarks>Sets IsInternalTransferCall to default 0 value.</remarks>
        public ReindexOnServerResponse Reindex(string sourceIndex, string targetIndexPrefix, IList<string> callIdList)
        {
            if( callIdList.Count == 0)
            {
                return null;
            }

            string targetIndex = $"{GetIndexName(targetIndexPrefix)}";

            ReindexOnServerResponse reindexResponse = _ElasticClient.ReindexOnServer(r => r
                       .Source(s => s
                           .Index(sourceIndex)
                           .Query<CallSummary>(q => q
                                .Bool(b => b
                                    .Should(callIdList.Select(t => BuildPhraseQueryContainerForCallId(q, t)).ToArray()))
                               )
                           )
                       .Destination(d => d
                           .Index(targetIndex)
                       )
                       .Script( sn => sn
                                .Source(
                                        @$"if (ctx._source != null && ctx._source['isInternalTransferCall'] == null) 
                                        {{
                                            ctx._source['isInternalTransferCall'] = 0;
                                        }}
                                        if (ctx._source['timeStamp'] != '0001-01-01T00:00:00') 
                                        {{
                                            ZonedDateTime sourceTime = ZonedDateTime.parse(ctx._source['timeStamp']);
                                            {this.GetScript_TimeZoneSets("", "UTC")}
                                            {this.GetScript_TimeZoneSets("America/New_York", "EST")}
                                            {this.GetScript_TimeZoneSets("America/Chicago", "CST")}
                                            {this.GetScript_TimeZoneSets("America/Phoenix", "MST")}
                                            {this.GetScript_TimeZoneSets("America/Los_Angeles", "PST")}
                                            {this.GetScript_TimeZoneSets("America/Anchorage", "AKST")}
                                            {this.GetScript_TimeZoneSets("America/Adak", "HAST")}
                                            {this.GetScript_TimeZoneSets("America/St_Johns", "NST")}
                                            {this.GetScript_TimeZoneSets("America/Puerto_Rico", "AST")}
                                            {this.GetScript_ClearTimeProperities()}
                                        }}
                                        "
                                )
                        )
                       .WaitForCompletion()
                    );


            return reindexResponse;
        }

        /// <summary>
        /// Deletes a series of Documents based on a Call Id List
        /// </summary>
        /// <param name="index">Index to delete</param>
        /// <param name="callIdList">List od Distinct Call ids to delete</param>
        /// <returns>Delete Query response.</returns>
        public DeleteByQueryResponse DeleteDocuments(string index, IList<string> callIdList)
        {

            DeleteByQueryResponse deleteResponse = _ElasticClient.DeleteByQuery<CallSummary>(d => d
                           .Index(index)
                           .Query(q => q
                               .Bool(b => b
                                   .Should(callIdList.Select(t => BuildPhraseQueryContainerForCallId(q, t)).ToArray()))
                              )
                           );

            return deleteResponse;
        }


        /// <summary>
        /// Updates / Upsert (insert if not exists) a given Call Summary.
        /// </summary>
        /// <param name="callSummary">Call Summary object</param>
        /// <param name="id">The unique identifier of the document</param>
        /// <param name="indexName">Index to write to</param>
        /// <exception cref="Exception"></exception>
        public string Update(CallSummary callSummary, string id, string indexPrefix)
        {
            var start = DateTime.Now;
            string rtnString = string.Empty;

            string indexName = $"{GetIndexName(indexPrefix)}";
            //Log.Information($"Adding {id} to index {indexName}");

            try
            {
                var response = _ElasticClient.Update<CallSummary>(id, r => r.Doc(callSummary).Index(indexName).DocAsUpsert(true));
                if (response.ServerError != null)
                {
                    //_logger.LogWarning($"ElasticSearchRepository : Update Warning {response.ServerError}");
                    rtnString = $"ElasticSearchRepository : Update Warning {response.ServerError}";
                }
            }

            finally
            {
                //var duration = DateTime.Now - start;
                //_logger.LogDebug($"ElasticSearchRepository : Update in {indexName} took {duration.TotalMilliseconds} ms");
            }

            return rtnString;
        }

        public string InsertMany(List<CallSummary> callSummary, string indexPrefix)
        {
            string rtnString = string.Empty;
            var start = DateTime.Now;
            string indexName = $"{GetIndexName(indexPrefix)}";
            //var indexName = GetIndexName(typeof(T), indexPrefix);
            try
            {
                var response = _ElasticClient.IndexMany(callSummary, indexName);
                if (response.ServerError != null)
                {
                    rtnString += ($"ElasticSearchRepository : Insert Many Warning {response.ServerError} \r\n");
                }
            }
            finally
            {
                //var duration = DateTime.Now - start;
                //_logger.LogDebug($"ElasticSearchRepository : Insert Many in {indexName} took {duration.TotalMilliseconds} ms");
            }

            return rtnString;
        }

    }

   

    /// <summary>
    /// Contains any available filter
    /// </summary>
    class FilterCriteria
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string PSAPName { get; set; }

        public FilterCriteria()
        {
            StartDate = DateTime.MinValue;
            EndDate = DateTime.MinValue;
            PSAPName = "*";
        }
    }
}
