# Agent Reporting System - Executive Summary

## Overview

This document summarizes the technical design for implementing an agent reporting system using AWS serverless architecture to process ACD/agent events and generate 5 specific reports for Power BI consumption.

## Key Requirements Met

### 5 Required Reports
1. **ACD - Detailed Calls by Group**: Call volume, handling metrics, and agent utilization by team
2. **ACD - Call Queue Summary Dashboard**: Queue performance metrics and service levels
3. **ACD - Call Taking Group Overview**: High-level efficiency metrics by ACD group
4. **Agent Performance - Call Distribution**: Individual agent performance with available time tracking
5. **Emergency Agent Performance**: Emergency call specific agent performance metrics

### Technical Requirements
- ✅ AWS serverless architecture (Lambda + Redshift)
- ✅ Real-time agent event processing
- ✅ Star schema design for optimal reporting
- ✅ Multi-tenant support with data isolation
- ✅ Power BI integration with hourly refresh
- ✅ Event-driven processing without existing collector API dependency

## Architecture Summary

### Current Flow
```
S3(per tenant bucket) -> SNS -> SQS -> Lambda(push event to collector) -> Collector API -> MariaDB(Callsummaries)
```

### Enhanced Flow
```
S3(same client bucket) -> SNS -> Lambda processors (agent events) -> Redshift(agent star schema)
                              \-> Lambda(call events) -> Collector -> Redshift(callsummary view)
```

## Agent Events Processed

Based on actual XML event analysis, the system will process these agent events:

1. **Login/Logout**: Track agent Guardian workstation sessions (one event per audio device)
2. **AgentAvailable/AgentBusiedOut**: Track agent availability states with reason codes
3. **ACDLogin/ACDLogout**: Track agent ACD queue-specific sessions

### Available Event Fields (Agent Events Only)
- **Common Fields**: timestamp (UTC), agencyOrElement, agent (username), eventType
- **Agent Fields**: mediaLabel, uri (tel: number), agentRole, tenantGroup, operatorId, workstation, deviceName
- **ACD Fields**: ringGroupName, ringGroupUri
- **State Fields**: busiedOutAction, reason, responseCode
- **Important**: NO call-related fields (callIdentifier, incidentIdentifier) in agent events

## Star Schema Design

### Dimension Tables
- **dim_tenant**: Tenant/customer information with timezone support
- **dim_agent**: Agent master data with SCD Type 2 for attribute changes (role, workstation, etc.)
- **dim_queue**: ACD queue/ring group information
- **dim_date**: Date dimension for time-based reporting
- **dim_time**: Time dimension for hourly/shift reporting

### Fact Tables
- **fact_agent_state**: Raw agent state change events (Login, Logout, Available, BusiedOut)
- **fact_agent_intervals**: Calculated time intervals with timezone conversion
- **fact_acd_session**: Agent sessions within specific ACD queues
- **fact_call**: View of existing callsummary data (separate from agent events)

## Key Calculations Explained

### Available Time Calculation
Sum of all intervals where agent is in "Available" state, calculated from AgentAvailable/AgentBusiedOut events.

### Service Level Calculations
Percentage of calls answered within specified time thresholds (10s, 15s, 20s, 40s) using conditional aggregation.

### Group Utilization
Ratio of available time to logged-in time, showing team productivity.

### Average Calls Per Shift
Total calls divided by number of shifts (login-to-logout periods within 24-hour windows).

## Field Mappings Verification

All required fields for the 5 reports have been mapped to available event data:

### Report 1 - ACD Detailed Calls by Group
- ✅ Ring Group: `ringGroupName` from ACDLogin events
- ✅ Calls Answered: Count from call events linked to ACD sessions
- ✅ Staffed Time: Calculated from Login/Logout intervals
- ✅ Available Time: Calculated from Available/BusiedOut intervals
- ✅ Service Level: Calculated from call timing data
- ✅ Reason Codes: `busiedOutAction` from BusiedOut events

### Report 2 - Call Queue Summary Dashboard
- ✅ Queue Name: `ringGroupName` from ACDLogin events
- ✅ Calls Answered in 10s%: Service level calculation
- ✅ Group Utilization%: Available time / logged in time ratio

### Report 3 - Call Taking Group Overview
- ✅ ACD Group: `ringGroupName` from ACDLogin events
- ✅ Service level breakdowns: Call timing calculations
- ✅ Team utilization metrics: Interval calculations

### Report 4 & 5 - Agent Performance Reports
- ✅ Agent Name: `operatorId` and agent identification from events
- ✅ Available Time: Calculated from agent state intervals
- ✅ Average Calls: Call count divided by shift count
- ✅ Individual service levels: Agent-specific calculations

## Implementation Approach

### Phase 1: Foundation (Weeks 1-2)
- AWS infrastructure setup
- Star schema creation in Redshift
- Basic Lambda functions

### Phase 2: Core Processing (Weeks 3-4)
- Agent state tracking logic
- Interval calculation algorithms
- Data pipeline implementation

### Phase 3: Reporting (Weeks 5-6)
- Redshift views for all reports
- Power BI integration
- Multi-tenant configuration

### Phase 4: Testing & Optimization (Weeks 7-8)
- End-to-end testing
- Performance optimization
- Data quality validation

### Phase 5: Production (Week 9)
- Production deployment
- Monitoring setup
- Go-live support

## Risk Mitigation

### Technical Risks
- **Agent state complexity**: Comprehensive unit testing and validation
- **Data latency**: DynamoDB for real-time state, batch for historical
- **Redshift performance**: Proper indexing and compression

### Operational Risks
- **Multi-tenant isolation**: Row-level security implementation
- **Event ordering**: Timestamp-based processing with late data handling
- **Power BI failures**: Retry logic and fallback mechanisms

## Success Criteria

1. **Functional**: All 5 reports generate accurate data per RFP specifications
2. **Performance**: Process 100,000+ events/hour, reports load <30 seconds
3. **Reliability**: 99.9% availability, <0.1% data loss
4. **Data Quality**: >99.5% agent state consistency, <1% calculation variance

## Next Steps

1. **Immediate**: Begin Phase 1 infrastructure setup
2. **Week 1**: Create detailed Lambda function specifications
3. **Week 2**: Implement agent state tracking algorithms
4. **Week 3**: Begin Redshift schema implementation
5. **Week 4**: Start Power BI report development

## Questions Answered

1. **Timezone Handling**: ✅ Use tenant-specific timezones with conversion from UTC
2. **Shift Definition**: ✅ Implement SCD Type 2 for agent dimension to handle multiple daily sessions
3. **Late Data**: ✅ Near real-time processing for agent events, Power BI import mode with hourly refresh
4. **Data Retention**: How long should detailed agent state data be retained in Redshift?
5. **Emergency vs Admin**: Are there specific queue configurations that determine emergency vs admin call classification?

## Conclusion

The proposed solution comprehensively addresses all RFP requirements using proven AWS serverless technologies. The star schema design enables efficient reporting, and the event-driven architecture ensures real-time processing capabilities. All required report fields have been mapped to available event data, ensuring complete functionality.

The 9-week implementation timeline provides adequate time for development, testing, and deployment while maintaining quality and reliability standards.
