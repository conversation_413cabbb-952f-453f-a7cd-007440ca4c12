{"comment": "This file is used to help set default values when using the dotnet CLI extension Amazon.ElasticBeanstalk.Tools. For more information run \"dotnet eb --help\" from the project root.", "profile": "solacomaws", "region": "us-west-1", "application": "DEV-CollectorAPI", "environment": "DEV-CollectorAPI", "iis-website": "Default Web Site", "app-path": "/", "enable-xray": false, "cname": "solacominfohubeventreceiverwebapi-dev", "solution-stack": "64bit Windows Server Core 2016 v2.5.7 running IIS 10.0", "environment-type": "SingleInstance", "instance-profile": "aws-elasticbeanstalk-ec2-role", "service-role": "aws-elasticbeanstalk-service-role", "health-check-url": "/api/ping", "instance-type": "t2.small", "key-pair": "solacom_kibana"}