# ---------------------------------------------------------------------------------------------------------------------
# General Variables
# ---------------------------------------------------------------------------------------------------------------------


variable "aws_region" {
  description = "AWS resource region"
  type        = "map"
}


variable "ebs_application_name" {
  description = "Elastic Beanstalk application name"
  type        = "string"
}


variable "ebs_application_description" {
  description = "Elastic Beanstalk application description"
  type        = "string"
}


variable "db_instance_identifier" {
  description = "The name of the RDS instance"
  type        = "string"
}


variable "db_instance_name" {
  description = "The name of the database to create when the DB instance is created"
  type        = "string"
}


variable "db_instance_username" {
  description = "Username for the master DB user"
  type        = "string"
}


variable "db_instance_password" {
  description = "Password for the master DB user"
  type        = "string"
}


variable "db_instance_parameter_group_name" {
  description = "parameter_group_name"
  type        = "string"
}


variable "db_instance_vpc_security_group_ids" {
  description = "List of VPC security groups to associate"
  type        = "string"
}


variable "db_instance_tag_name" {
  description = "Tag name"
  type        = "string"
}
