﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Solacom.InfoHub.EventReceiver.Entities;
using Solacom.InfoHub.EventReceiver.MariaDb.Context.Interfaces;
using Solacom.InfoHub.EventReceiver.MariaDb.Context.Models;
using Microsoft.Extensions.Configuration;
using Solacom.InfoHub.EventReceiver.Entities.Security;

namespace Solacom.InfoHub.EventReceiver.MariaDb.Context
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly ILogger<UnitOfWork> _logger;
        private readonly IDataProvider _dataProvider;

        public UnitOfWork(IServiceProvider services, ILogger<UnitOfWork> logger, IConfiguration configuration)
        {
            _logger = logger;
            _dataProvider = new MySQLProvider( Encryption.Decrypt(configuration["Database:mysql:connectionstring.CollectorAPI"]));
        }

        public async Task<AgentSessionRecord> GetAgentSession(string mediaLabel, string clientCode)
        {
            var start = DateTime.Now;
            try
            {
                return await _dataProvider.GetAgentSession(mediaLabel, clientCode);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : GetAgentSession took {duration.TotalMilliseconds} ms");
            }
        }

        public async Task<int> AddEvent(string clientCode, EventLog eventLog)
        {
            var start = DateTime.Now;
            try
            {
                return await _dataProvider.AddEvent(clientCode, eventLog);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : UpdateEvents took {duration.TotalMilliseconds} ms for {eventLog.callIdentifier}");
            }
        }

        public async Task<(Dictionary<string, int>, int, int)> GetEventTypeCount(string callIdentifier, string clientId, int maxEventId)
        {
            var start = DateTime.Now;
            try
            {
                return await _dataProvider.GetEventTypeCount(callIdentifier, clientId, maxEventId);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : GetEventTypeCount took {duration.TotalMilliseconds} ms for {callIdentifier}");
            }
        }

        
        public async Task<IList<CallInstance>> GetExpiredEventsCallId(int olderthanHours)
        {
            var start = DateTime.Now;
            try
            {
                return await _dataProvider.GetExpiredEventsCallId(olderthanHours);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : GetExpiredEventsCallId took {duration.TotalMilliseconds} ms.");
            }
        }

        public async Task<long> SetEventProcessed(string callIdentifier, string clientId, int maxEventId)
        {
            var start = DateTime.Now;
            long eventsUpdateCount = 0;
            try
            {
                eventsUpdateCount = await _dataProvider.SetEventProcessed(callIdentifier, clientId, maxEventId);
                return eventsUpdateCount;
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : SetEventProcessed took {duration.TotalMilliseconds} ms, updating {eventsUpdateCount} rows for {callIdentifier}.");
            }
        }

        public async Task<long> SetErrorEventState(string callIdentifier, string clientId, int maxEventId)
        {
            var start = DateTime.Now;
            long eventsUpdateCount = 0;
            try
            {
                eventsUpdateCount = await _dataProvider.SetErrorEventState(callIdentifier, clientId, maxEventId);
                return eventsUpdateCount;
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : SetErrorEventState took {duration.TotalMilliseconds} ms, updating {eventsUpdateCount} rows for {callIdentifier}.");
            }
        }

        public async Task<long> SetExpiredEventState(string callIdentifier, string clientId, int maxEventId)
        {
            var start = DateTime.Now;
            long eventsUpdateCount = 0;
            try
            {
                eventsUpdateCount = await _dataProvider.SetExpiredEventState(callIdentifier, clientId, maxEventId);
                return eventsUpdateCount;
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : SetExpiredEventState took {duration.TotalMilliseconds} ms, updating {eventsUpdateCount} rows for {callIdentifier}.");
            }
        }
        

        public async Task SetProcessQueue(string callIdentifier, string clientId)
        {
            var start = DateTime.Now;
            try
            {
                await _dataProvider.SetProcessQueue(callIdentifier, clientId);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : SetProcessQueue took {duration.TotalMilliseconds} ms for {callIdentifier}.");
            }
        }
                
        public async Task<IList<CallInstance>> GetProcessQueueOlderThan(int olderthanMinutes)
        {
            var start = DateTime.Now;
            try
            {
                return await _dataProvider.GetProcessQueueOlderThan(olderthanMinutes);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : GetProcessQueueOlderThan took {duration.TotalMilliseconds} ms.");
            }
        }

        public async Task DeleteProcessQueue(string callIdentifier, string clientId)
        {
            var start = DateTime.Now;
            try
            {
                await _dataProvider.DeleteProcessQueue(callIdentifier, clientId);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : DeleteProcessQueue took {duration.TotalMilliseconds} ms for {callIdentifier}.");
            }
        }

        public async Task LogError(string clientCode, string callId)
        {
            var start = DateTime.Now;
            try
            {
                await _dataProvider.LogError(clientCode, callId);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : LogError took {duration.TotalMilliseconds} ms for {callId}");
            }
        }

        public async Task DeleteErrorEvent(string data)
        {
            var start = DateTime.Now;
            try
            {
                await _dataProvider.DeleteErrorEvent(data);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : DeleteErrorEvent took {duration.TotalMilliseconds} ms");
            }
        }

        public async Task<List<string>> GetErrorEvents()
        {
            var start = DateTime.Now;
            try
            {
              return await _dataProvider.GetErrorEvents();
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : GetErrorEvents took {duration.TotalMilliseconds} ms");
            }
        }

        public async Task<List<EventLog>> GetEventsForProcessing(string callIdentifier, string clientId, int maxEventId)
        {
            var start = DateTime.Now;
            try
            {
                return await _dataProvider.GetEventsForProcessing(callIdentifier, clientId, maxEventId);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : GetEventsForProcessing took {duration.TotalMilliseconds} ms for {callIdentifier}");
            }
        }
             
        public async Task<Eventhash> GetHashEvent(string hashedEvent, string clientCode)
        {
            var start = DateTime.Now;
            try
            {
                return await _dataProvider.GetHashEvent(hashedEvent, clientCode);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : GetHashEvent took {duration.TotalMilliseconds} ms");
            }
        }

        public async Task UpdateHashEvent(Eventhash eventHash)
        {
            var start = DateTime.Now;
            try
            {
                await _dataProvider.UpdateHashEvent(eventHash);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : UpdateHashEvent took {duration.TotalMilliseconds} ms for {eventHash.Callid}");
            }
        }

        public async Task DeleteHashEvent(string hashedContent, string clientCode)
        {
            await _dataProvider.DeleteHashEvent(hashedContent, clientCode);
        }

        public async Task UpdateAgentSession(AgentSessionRecord agentSessionRecord, string clientCode)
        {
            var start = DateTime.Now;
            try
            {
                await _dataProvider.UpdateAgentSession(agentSessionRecord, clientCode);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : UpdateAgentSession took {duration.TotalMilliseconds} ms");
            }
        }

        /// <summary>
        /// Deletes data from the identified tables based on the Olderthan parameters.  Excludes any records in ErrorState (StateId=4)
        /// </summary>
        /// <param name="olderThan">Older than number of hours</param>
        /// <exception cref="exception"></exception>
        public async Task CleanUpTablesHashEvents(int olderThan)
        {
            var start = DateTime.Now;
            try
            {
                await _dataProvider.CleanUpTablesHashEvents(olderThan);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : CleanupTablesHashEvents took {duration.TotalMilliseconds} ms");
            }
        }
        /// <summary>
        /// Deletes data from the identified tables based on the Olderthan parameters.  Excludes any records in ErrorState (StateId=4)
        /// </summary>
        /// <param name="olderThan">Older than number of hours</param>
        /// <exception cref="exception"></exception>
        public async Task CleanUpTablesEvents(int olderThan)
        {
            var start = DateTime.Now;
            try
            {
                await _dataProvider.CleanUpTablesEvents(olderThan);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : CleanUpTablesEvents took {duration.TotalMilliseconds} ms");
            }
        }
      
        /// <summary>
        /// Deletes data from the identified tables based on the Olderthan parameters.  Excludes any records in ErrorState (StateId=4)
        /// </summary>
        /// <param name="olderThan">Older than number of hours</param>
        /// <exception cref="exception"></exception>
        public async Task CleanUpTablesAgentSession(int olderThan)
        {
            var start = DateTime.Now;
            try
            {
                await _dataProvider.CleanUpTablesAgentSession(olderThan);
            }
            finally
            {
                var duration = DateTime.Now - start;
                _logger.LogDebug($"UnitOfWork : CleanUpTablesAgentSession took {duration.TotalMilliseconds} ms");
            }
        }


        private bool _disposed = false;

        private void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                }
            }
            _disposed = true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}