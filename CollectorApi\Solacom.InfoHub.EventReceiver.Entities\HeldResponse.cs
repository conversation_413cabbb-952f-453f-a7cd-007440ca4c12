﻿using System;
using System.Xml.Serialization;
using Nest;
using Newtonsoft.Json;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    /// <summary>
    /// Currently not being used in process logic - HELD sub node is being excluded at this time.
    /// </summary>
    [XmlRoot(ElementName = "heldResponse")]
    public class HeldResponse
    {
        [XmlElement(ElementName = "mediaLabel")]
        public string MediaLabel { get; set; }
        [XmlElement(ElementName = "heldDomain")]
        public string HeldDomain { get; set; }
        [XmlElement(ElementName = "responseCode")]
        public string ResponseCode { get; set; }
        /// <summary>
        /// Removed from XML parsing, ref: INFO-1699 for original reason
        /// </summary>
        [XmlElement(ElementName = "held")]
        [Ignore]
        [XmlIgnore]
        //[JsonIgnore] <- new held code requires this to be disabled as DB/Root can have object.
        public Held Held { get; set; }

        public LocationData LocationData { get; set; }
    }

    [XmlRoot(ElementName = "held", Namespace = "http://solacom.com/Logging")]
    public class Held
    {
        [XmlElement(ElementName = "locationResponse", Namespace = "urn:ietf:params:xml:ns:geopriv:held")]
        [XmlElement(ElementName = "urn:locationResponse", Namespace = "urn:ietf:params:xml:ns:geopriv:held")]
        [XmlElement(ElementName = "held:locationResponse", Namespace = "urn:ietf:params:xml:ns:geopriv:held")]
        [XmlIgnore]
        public LocationResponse LocationResponse { get; set; }

        /// <summary>
        /// Detected from a test system - not a supported node nor expected to be parsed.
        /// </summary>
        [XmlElement(ElementName = "svc_result")]
        [XmlIgnore]
        [Ignore]
        public string svc_result { get; set; }
    }

    [XmlRoot(ElementName = "locationResponse", Namespace = "urn:ietf:params:xml:ns:geopriv:held")]
    public class LocationResponse
    {
        [XmlElement(ElementName = "presence", Namespace = "urn:ietf:params:xml:ns:pidf")]
        public Presence Presence { get; set; }
        [XmlAttribute(AttributeName = "basicPolicy", Namespace = "http://www.w3.org/2000/xmlns/")]
        public string BasicPolicy { get; set; }
        [XmlAttribute(AttributeName = "conf", Namespace = "http://www.w3.org/2000/xmlns/")]
        public string Conf { get; set; }
        [XmlAttribute(AttributeName = "geopriv10", Namespace = "http://www.w3.org/2000/xmlns/")]
        public string Geopriv10 { get; set; }
        [XmlAttribute(AttributeName = "gml", Namespace = "http://www.w3.org/2000/xmlns/")]
        public string Gml { get; set; }
        [XmlAttribute(AttributeName = "held", Namespace = "http://www.w3.org/2000/xmlns/")]
        public string Held { get; set; }
        [XmlAttribute(AttributeName = "pidf", Namespace = "http://www.w3.org/2000/xmlns/")]
        public string Pidf { get; set; }
        [XmlAttribute(AttributeName = "pidflo", Namespace = "http://www.w3.org/2000/xmlns/")]
        public string Pidflo { get; set; }
    }

    [XmlRoot(ElementName = "presence", Namespace = "urn:ietf:params:xml:ns:pidf")]
    public class Presence
    {
        [XmlElement(ElementName = "tuple", Namespace = "urn:ietf:params:xml:ns:pidf")]
        public Tuple Tuple { get; set; }
        [XmlAttribute(AttributeName = "entity")]
        public string Entity { get; set; }
        [XmlAttribute(AttributeName = "gml", Namespace = "http://www.w3.org/2000/xmlns/")]
        public string Gml { get; set; }
        [XmlAttribute(AttributeName = "bdp", Namespace = "http://www.w3.org/2000/xmlns/")]
        public string Bdp { get; set; }
        [XmlAttribute(AttributeName = "gs", Namespace = "http://www.w3.org/2000/xmlns/")]
        public string Gs { get; set; }
        [XmlAttribute(AttributeName = "xmlns")]
        public string Xmlns { get; set; }
        [XmlAttribute(AttributeName = "gp", Namespace = "http://www.w3.org/2000/xmlns/")]
        public string Gp { get; set; }
    }

    [XmlRoot(ElementName = "tuple", Namespace = "urn:ietf:params:xml:ns:pidf")]
    public class Tuple
    {
        [XmlElement(ElementName = "status", Namespace = "urn:ietf:params:xml:ns:pidf")]
        public Status Status { get; set; }
        [XmlElement(ElementName = "timestamp", Namespace = "urn:ietf:params:xml:ns:pidf")]
        public string Timestamp { get; set; }
        [XmlAttribute(AttributeName = "id")]
        public string Id { get; set; }
    }

    [XmlRoot(ElementName = "status", Namespace = "urn:ietf:params:xml:ns:pidf")]
    public class Status
    {
        [XmlElement(ElementName = "geopriv", Namespace = "urn:ietf:params:xml:ns:pidf:geopriv10")]
        public Geopriv Geopriv { get; set; }
    }

    [XmlRoot(ElementName = "geopriv", Namespace = "urn:ietf:params:xml:ns:pidf:geopriv10")]
    public class Geopriv
    {
        [XmlElement(ElementName = "location-info", Namespace = "urn:ietf:params:xml:ns:pidf:geopriv10")]
        public Locationinfo Locationinfo { get; set; }
        [XmlElement(ElementName = "usage-rules", Namespace = "urn:ietf:params:xml:ns:pidf:geopriv10")]
        public Usagerules Usagerules { get; set; }
    }

    [XmlRoot(ElementName = "location-info", Namespace = "urn:ietf:params:xml:ns:pidf:geopriv10")]
    public class Locationinfo
    {
        //[XmlElement(ElementName = "Circle", Namespace = "http://www.opengis.net/pidflo/1.0")]
        //public Circle Circle { get; set; }
        [XmlElement(ElementName = "confidence", Namespace = "urn:ietf:params:xml:ns:geopriv:conf")]
        public Confidence Confidence { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <remarks>
    ///     for startcall.location.presence usage, the namespace is different [XmlElement(ElementName = "retransmission-allowed", Namespace = "urn:ietf:params:xml:ns:pidf:geopriv10")] See INFO-1692 for full context notes.
    /// </remarks>
    [XmlRoot(ElementName = "usage-rules", Namespace = "urn:ietf:params:xml:ns:pidf:geopriv10")]
    public class Usagerules
    {
        [XmlElement(ElementName = "retransmission-allowed", Namespace = "urn:ietf:params:xml:ns:pidf:geopriv10:basicPolicy")]
        public string Retransmissionallowed { get; set; }
    }

    //[XmlRoot(ElementName = "Circle", Namespace = "http://www.opengis.net/pidflo/1.0")]
    //public class Circle
    //{
    //    [XmlElement(ElementName = "pos", Namespace = "http://www.opengis.net/gml")]
    //    public string Pos { get; set; }
    //    [XmlElement(ElementName = "radius", Namespace = "http://www.opengis.net/pidflo/1.0")]
    //    public Radius Radius { get; set; }
    //    [XmlAttribute(AttributeName = "srsName")]
    //    public string SrsName { get; set; }
    //}

    [XmlRoot(ElementName = "confidence", Namespace = "urn:ietf:params:xml:ns:geopriv:conf")]
    public class Confidence
    {
        [XmlAttribute(AttributeName = "pdf")]
        public string Pdf { get; set; }
        [XmlText]
        public string Text { get; set; }

        /// <summary>
        /// tracking where the location information came from for auditing purposes.  
        /// </summary>
        [XmlIgnore]
        public BusinessLogic.Enums.LocationSource LocationSource { get; set; }
    }

    [XmlRoot(ElementName = "radius", Namespace = "http://www.opengis.net/pidflo/1.0")]
    public class Radius
    {
        [XmlAttribute(AttributeName = "uom")]
        public string Uom { get; set; }
        [XmlText]
        public string Text { get; set; }
    }
}
