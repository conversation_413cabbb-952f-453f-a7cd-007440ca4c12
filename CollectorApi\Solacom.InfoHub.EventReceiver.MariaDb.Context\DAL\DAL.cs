﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using MySqlConnector;

namespace Solacom.InfoHub.EventReceiver.MariaDb.Context.DAL
{
    public partial class DAL : IDisposable
    {
        //Dispose flag
        private bool _AlreadyDisposed = false;
        //SQL Connection String
        protected string _SQLConnectionString = string.Empty;
        //Default Connection Object
        protected MySqlConnection _SQLConnection = null;
        //Default SQL Command Object
        protected MySqlCommand _SQLCommand = null;
        //Default Transaction
        protected MySqlTransaction _SQLTransaction = null;

        #region Constructor

        public DAL(string connectionString)
        {
            string defaultConnectionString = connectionString;
            _SQLConnectionString = defaultConnectionString;
        }

        #endregion

        #region Destructor

        ~DAL()
        {
            Dispose(false);
        }

        #endregion

        #region Init SQL Connection Objects

        private void InitSQLConnectionObjects()
        {
            _SQLConnection = new MySqlConnection(_SQLConnectionString);

            //release the used one
            if (_SQLCommand != null)
            {
                _SQLCommand.Dispose();
            }

            //create the new one
            _SQLCommand = new MySqlCommand();
            _SQLCommand.Connection = _SQLConnection;

            if (_SQLTransaction != null)
            {
                _SQLCommand.Transaction = _SQLTransaction;
            }
        }

        #endregion

        #region Open SQL Connection

        private void OpenSQLConnection()
        {
            if (_SQLConnection != null)
            {
                if (_SQLConnection.State == System.Data.ConnectionState.Open)
                {
                    return;
                }
                else
                {
                    _SQLConnection.Open();
                }
            }
        }

        private void OpenSQLConnectionWithTransaction()
        {
            // Open connection
            OpenSQLConnection();

            // Set transcation
            _SQLTransaction = _SQLConnection.BeginTransaction();
            _SQLCommand.Transaction = _SQLTransaction;
        }

        /// <summary>
        /// Retrieves the given DB version
        /// </summary>
        /// <returns>Version string. Empty if none found.</returns>
        public string GetVersion()
        {
            string versionString = string.Empty;

            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "SELECT VERSION();";
                _SQLCommand.CommandType = System.Data.CommandType.Text;

                OpenSQLConnection();

                MySqlDataReader dr = _SQLCommand.ExecuteReader();

                if (dr.Read())
                {
                    versionString = (string)dr[0];
                }

                dr.Close();
                dr = null;
            }
            finally
            {
                CleanupSQLResources();
            }

            return versionString;
        }

        #endregion

        #region PrepareConnection

        protected void PrepareConnection(string spName)
        {
            InitSQLConnectionObjects();

            _SQLCommand.CommandText = spName;
            _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
        }

        #endregion

        #region Close SQL Connection

        private void CleanupSQLResources()
        {
            if (_SQLConnection != null)
            {
                try
                {
                    _SQLConnection.Close();
                }
                catch (Exception ex)
                {
                    //Logger.Log(ex);
                    // Do nothing when fail to close connection
                }

                //For SqlConnection objects, 'closing' a connection simply returns it to the connection pool - the connection is not really closed.  
                //This behavior is desirable.  Opening and closing connections is a processor intensive task, which is why connection pooling is so 
                //important for writing scalable applications.  If you call Dispose() on a connection object, it is first returned to the connection 
                //pool.  This is because the Dispose() method includes a call to the Close() method.  Once the connection object is back in the pool, 
                //it releases all unmanaged resources (i.e., the database handle).  By releasing its database handle, the connection is rendered 
                //useless, and is simply kicked out of the connection pool.

                //Calling Dispose() effectively destroys the purpose of connection pooling, which is to promote connection reuse.  
                //It is hard to imagine a valid reason for calling Dispose() at all.  If you really don't want to use connection pooling, 
                //you can simply disable it in your connection string (easier to turn it back on from here if you change your mind later).  
                //If you want to cleanup after you're done using a connection, simply call the Close() method.  The connection is returned 
                //to the connection pool, and will be available for reuse next time you application calls the Open() method.
            }

            if (_SQLCommand != null)
            {
                _SQLCommand.Dispose();
            }

            if (_SQLTransaction != null)
            {
                _SQLTransaction.Dispose();
            }
        }

        #endregion

        #region Commit Transaction

        private void CommitTransaction()
        {
            _SQLTransaction.Commit();
        }

        #endregion

        #region Rollback Transaction

        private void RollBackTransaction()
        {
            _SQLTransaction.Rollback();
        }

        #endregion

        #region IDisposable Members

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool isDisposing)
        {
            //released check
            if (_AlreadyDisposed)
            {
                return;
            }
            if (isDisposing)
            {
                //release managed source
                CleanupSQLResources();
                _SQLCommand = null;
                _SQLTransaction = null;
                _SQLConnection = null;
            }
            // release unmanaged source

            // set disposed flag
            _AlreadyDisposed = true;
        }

        #endregion
    }
}
