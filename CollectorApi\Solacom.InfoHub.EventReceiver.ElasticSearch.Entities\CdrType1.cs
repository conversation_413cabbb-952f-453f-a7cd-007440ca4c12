﻿namespace Solacom.InfoHub.EventReceiver.ElasticSearch.Entities
{
    public class CdrType1 : EventLog
    {
        public string StartTime { get; set; }
        public string OperatorId { get; set; }
        public string Ani { get; set; }
        public string PresentedTime { get; set; }
        public string AnsweredTime { get; set; }
        public string JobNumber { get; set; }
        public string TransferTime { get; set; }
        public string TransferAnswerTime { get; set; }
        public string DisassociatedTime { get; set; }
        public string TransferTargetType { get; set; }
        public string TransferTargetName { get; set; }
        public string TransferTarget { get; set; }
        public string DisconnectReason { get; set; }
        public string IvrOutcome { get; set; }
        public string ExternalTransferAttempts { get; set; }
        public string Dnis { get; set; }
        public string EndTime { get; set; }
    }
}