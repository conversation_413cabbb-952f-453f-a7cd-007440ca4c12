﻿
namespace Solacom.InfoHub.EventReceiver.BusinessLogic.Enums
{
    /// <summary>
    /// Defines the list of ONLY the EventTypes the code processes.
    /// </summary>
    public enum EventType
    {
        StartCall,
        Route,
        TransferCall,
        Answer,
        EndMedia,
        EndCall,
        ALIresponse,
        OutboundCall,
        Media,
        Message,
        ALIquery,
        Hold,
        HoldRetrieved,
        CDRtype1
    }

    /// <summary>
    /// Defines the list of Event Types that are not directly serialized and will be processed in other ways (i.e. XPATH)
    /// </summary>
    public enum EventType_NonSerialized
    {
        HELDquery,
        HELDresponse,
        EIDD
    }

    /// <summary>
    /// Defines the Agent related Event Types
    /// </summary>
    /// <remarks>Though we don't have full support for <PERSON>, syncing it to match the new enum usage standards</remarks>
    public enum EventType_AgentAudit
    {
        Login, 
        Logout, 
        AgentBusiedOut, 
        AgentAvailable
    }

    /// <summary>
    /// Defines the available Message Types
    /// </summary>
    public enum MessageType
    { 
        RTT, 
        TDD, 
        SMS
    }

    /// <summary>
    /// Defines Location data event sources
    /// </summary>
    public enum LocationEventSource
    {
        HELD,
        EIDD
    }

    /// <summary>
    /// defines the source of the specific Location data came from
    /// </summary>
    public enum LocationSource
    {
        device,
        tuple,
        person,
        undefined
    }
}
