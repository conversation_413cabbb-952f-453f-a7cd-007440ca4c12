﻿namespace Solacom.InfoHub.EventReceiver.ElasticSearch.Entities
{
    public class Route : EventLog
    {
        public string Uri { get; set; }
        public string Rule { get; set; }
        public string Reason { get; set; }
        public string MediaLabel { get; set; }
        public string Attempt { get; set; }
        public string Priority { get; set; }
        public string Ani { get; set; }
        public string AniDomain { get; set; }
        public string Dnis { get; set; }
        public string Pani { get; set; }
        public string Esrn { get; set; }
        public string CallerName { get; set; }
        public string AniTranslated { get; set; }
        public string DnisTranslated { get; set; }
        public string CallerNameTranslated { get; set; }
    }
}