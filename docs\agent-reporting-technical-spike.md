# Agent Reporting System - Technical Spike

## Executive Summary

This document provides a comprehensive technical design for implementing an agent reporting system using AWS serverless architecture. The system will process agent events in real-time and generate 5 specific ACD/Agent reports using a star schema in Redshift, with Power BI integration for visualization.

## Current vs. Proposed Architecture

### Current Flow
```
S3(per tenant bucket) -> SNS -> SQS -> Lambda(push event to collector) -> Collector API -> MariaDB(Callsummaries)
```

### Proposed Enhanced Flow
```
S3(same client bucket) -> SNS -> Lambda processors (agent events) -> Redshift(agent star schema)
                              \-> Lambda(call events) -> Collector -> Redshift(callsummary view)
```

## Architecture Overview

### High-Level Components

1. **Event Ingestion Layer**
   - S3 buckets (per tenant)
   - SNS topics for event routing
   - SQS queues for reliable processing

2. **Processing Layer**
   - Lambda functions for agent event processing
   - Lambda functions for call event processing
   - Event transformation and validation

3. **Data Storage Layer**
   - Redshift cluster with star schema
   - S3 for intermediate data storage
   - Redshift Spectrum for external table access

4. **Reporting Layer**
   - Power BI for visualization
   - Pre-calculated views for performance
   - Hourly refresh cycles

## Agent Events Analysis

Based on the codebase analysis, the following agent events are available:

### Core Agent Events

1. **Login Event**
   - Fields: mediaLabel, uri, agentRole, tenantGroup, operatorId, workstation, deviceName, reason
   - Purpose: Track when agents log into the system

2. **Logout Event**
   - Fields: mediaLabel, uri, agentRole, tenantGroup, operatorId, workstation, deviceName, reason, responseCode
   - Purpose: Track when agents log out of the system

3. **AgentAvailable Event**
   - Fields: mediaLabel, uri, agentRole, tenantGroup, operatorId, workstation, busiedOutAction
   - Purpose: Track when agents become available for calls

4. **AgentBusiedOut Event**
   - Fields: mediaLabel, uri, agentRole, tenantGroup, operatorId, workstation, busiedOutAction
   - Purpose: Track when agents become unavailable with reason codes

5. **ACDLogin Event**
   - Fields: mediaLabel, agentUri, agentRole, tenantGroup, operatorId, workstation, deviceName, ringGroupName, ringGroupUri
   - Purpose: Track when agents log into specific ACD queues

6. **ACDLogout Event**
   - Fields: mediaLabel, agentUri, agentRole, tenantGroup, operatorId, workstation, deviceName, ringGroupName, ringGroupUri
   - Purpose: Track when agents log out of specific ACD queues

7. **QueueStateChange Event**
   - Fields: stateChangeNotificationContents, queueId, queueName, direction, count
   - Purpose: Track queue state changes

### Common Event Fields (from EventLog base class)
- timestamp (UTC)
- timestampToLocal (local timezone)
- agencyOrElement (tenant identifier)
- agent
- callIdentifier
- incidentIdentifier
- eventType

## Star Schema Design

### Dimension Tables

#### dim_tenant
```sql
CREATE TABLE dim_tenant (
    tenant_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    tenant_name VARCHAR(100) NOT NULL,
    agency_or_element VARCHAR(200),
    tenant_group VARCHAR(200),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### dim_agent
```sql
CREATE TABLE dim_agent (
    agent_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_name VARCHAR(100),
    operator_id VARCHAR(50),
    agent_role VARCHAR(50),
    uri VARCHAR(200),
    workstation VARCHAR(200),
    device_name VARCHAR(200),
    tenant_key BIGINT REFERENCES dim_tenant(tenant_key),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### dim_queue
```sql
CREATE TABLE dim_queue (
    queue_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    ring_group_name VARCHAR(200),
    ring_group_uri VARCHAR(500),
    queue_id VARCHAR(100),
    queue_name VARCHAR(200),
    tenant_key BIGINT REFERENCES dim_tenant(tenant_key),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### dim_date
```sql
CREATE TABLE dim_date (
    date_key INT PRIMARY KEY,
    full_date DATE,
    year INT,
    quarter INT,
    month INT,
    day INT,
    day_of_week INT,
    day_name VARCHAR(10),
    month_name VARCHAR(10),
    is_weekend BOOLEAN,
    is_holiday BOOLEAN
);
```

#### dim_time
```sql
CREATE TABLE dim_time (
    time_key INT PRIMARY KEY,
    hour INT,
    minute INT,
    second INT,
    time_of_day VARCHAR(8),
    hour_name VARCHAR(10),
    shift VARCHAR(20)
);
```

### Fact Tables

#### fact_agent_state
```sql
CREATE TABLE fact_agent_state (
    state_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_key BIGINT REFERENCES dim_agent(agent_key),
    tenant_key BIGINT REFERENCES dim_tenant(tenant_key),
    date_key INT REFERENCES dim_date(date_key),
    time_key INT REFERENCES dim_time(time_key),
    event_timestamp TIMESTAMP,
    event_type VARCHAR(50), -- Login, Logout, Available, BusiedOut
    state_duration_seconds INT,
    reason_code VARCHAR(100),
    busied_out_action VARCHAR(100),
    media_label VARCHAR(200),
    call_identifier VARCHAR(200),
    incident_identifier VARCHAR(200)
);
```

#### fact_agent_intervals
```sql
CREATE TABLE fact_agent_intervals (
    interval_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_key BIGINT REFERENCES dim_agent(agent_key),
    tenant_key BIGINT REFERENCES dim_tenant(tenant_key),
    date_key INT REFERENCES dim_date(date_key),
    start_time_key INT REFERENCES dim_time(time_key),
    end_time_key INT REFERENCES dim_time(time_key),
    interval_start_timestamp TIMESTAMP,
    interval_end_timestamp TIMESTAMP,
    interval_type VARCHAR(50), -- LoggedIn, Available, BusiedOut
    duration_seconds INT,
    reason_code VARCHAR(100),
    shift_identifier VARCHAR(50)
);
```

#### fact_acd_session
```sql
CREATE TABLE fact_acd_session (
    session_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_key BIGINT REFERENCES dim_agent(agent_key),
    queue_key BIGINT REFERENCES dim_queue(queue_key),
    tenant_key BIGINT REFERENCES dim_tenant(tenant_key),
    date_key INT REFERENCES dim_date(date_key),
    login_time_key INT REFERENCES dim_time(time_key),
    logout_time_key INT REFERENCES dim_time(time_key),
    acd_login_timestamp TIMESTAMP,
    acd_logout_timestamp TIMESTAMP,
    session_duration_seconds INT,
    media_label VARCHAR(200)
);
```

#### fact_call (Enhanced view of existing callsummary)
```sql
CREATE VIEW fact_call AS
SELECT 
    cs.callIdentifier as call_identifier,
    cs.agentName as agent_name,
    cs.tenantPsapName as tenant_name,
    cs.startCallTime as call_start_time,
    cs.endCallTime as call_end_time,
    cs.callAnswered as call_answered_time,
    cs.agentTimeToAnswerInSeconds as agent_time_to_answer_seconds,
    cs.talkTimeInSeconds as talk_time_seconds,
    cs.totalCallTimeInSeconds as total_call_time_seconds,
    cs.isEmergencyCall as is_emergency,
    cs.isAdminCall as is_admin,
    cs.isTransferred as is_transferred,
    cs.isAbandoned as is_abandoned,
    CASE 
        WHEN cs.agentTimeToAnswerInSeconds <= 10 THEN 1 ELSE 0 
    END as answered_within_10s,
    CASE 
        WHEN cs.agentTimeToAnswerInSeconds <= 15 THEN 1 ELSE 0 
    END as answered_within_15s,
    CASE 
        WHEN cs.agentTimeToAnswerInSeconds <= 20 THEN 1 ELSE 0 
    END as answered_within_20s,
    CASE 
        WHEN cs.agentTimeToAnswerInSeconds <= 40 THEN 1 ELSE 0 
    END as answered_within_40s
FROM callsummary cs;
```

## Event Processing Logic

### Agent State Tracking Algorithm

1. **Login Processing**
   - Create new agent session in fact_agent_intervals
   - Update dim_agent if new agent
   - Start logged_in interval

2. **Logout Processing**
   - End current logged_in interval
   - Calculate total session duration
   - End any open available/busied intervals

3. **Available Processing**
   - End current busied interval (if exists)
   - Start new available interval

4. **BusiedOut Processing**
   - End current available interval (if exists)
   - Start new busied interval with reason code

5. **ACD Login/Logout Processing**
   - Track queue-specific sessions
   - Link to agent sessions

### Interval Calculation Logic

```python
def calculate_agent_intervals(events):
    """
    Process agent events to calculate time intervals
    """
    sessions = {}
    
    for event in sorted(events, key=lambda x: x.timestamp):
        agent_id = event.operatorId
        
        if event.eventType == 'Login':
            sessions[agent_id] = {
                'login_time': event.timestamp,
                'current_state': 'logged_in',
                'intervals': []
            }
            
        elif event.eventType == 'Logout':
            if agent_id in sessions:
                session = sessions[agent_id]
                session['logout_time'] = event.timestamp
                session['total_duration'] = (
                    event.timestamp - session['login_time']
                ).total_seconds()
                
        elif event.eventType == 'AgentAvailable':
            if agent_id in sessions:
                session = sessions[agent_id]
                if session['current_state'] == 'busied_out':
                    # End busied interval
                    end_interval(session, event.timestamp, 'busied_out')
                
                # Start available interval
                start_interval(session, event.timestamp, 'available')
                session['current_state'] = 'available'
                
        elif event.eventType == 'AgentBusiedOut':
            if agent_id in sessions:
                session = sessions[agent_id]
                if session['current_state'] == 'available':
                    # End available interval
                    end_interval(session, event.timestamp, 'available')
                
                # Start busied interval
                start_interval(session, event.timestamp, 'busied_out', 
                             event.busiedOutAction)
                session['current_state'] = 'busied_out'
    
    return sessions
```

## Report Field Mappings and Calculations

### Report 1: ACD - Detailed Calls by Group

**Purpose**: Provide detailed call volume and handling metrics by ACD group/team.

| RFP Requirement | Friendly Name | Database Field | Calculation | Source Events |
|----------------|---------------|----------------|-------------|---------------|
| Queue Data | Ring Group | dim_queue.ring_group_name | Direct mapping | ACDLogin events |
| Queue calls answered | Answered | COUNT(fact_call.call_identifier) | WHERE call_answered_time IS NOT NULL | Call events + ACD sessions |
| Calls transferred in | Transferred In | COUNT(fact_call.call_identifier) | WHERE transfer_from IS NOT NULL | Call events |
| Calls transferred out | Transferred Out | COUNT(fact_call.call_identifier) | WHERE transfer_to IS NOT NULL | Call events |
| Staffed time | Staffed Time | SUM(fact_agent_intervals.duration_seconds) | WHERE interval_type = 'LoggedIn' | Login/Logout events |
| Available time | Available Time | SUM(fact_agent_intervals.duration_seconds) | WHERE interval_type = 'Available' | Available/BusiedOut events |
| Total clerical time | Wrap-up Time | SUM(fact_call.wrapup_time_seconds) | From call events | Call events |
| Total conversation time | Talk Time | SUM(fact_call.talk_time_seconds) | From call events | Call events |
| Average call holding time | Avg Talk Time | AVG(fact_call.talk_time_seconds) | Average per call | Call events |
| Average handling time | Handling Time | AVG(fact_call.talk_time_seconds + fact_call.wrapup_time_seconds) | Talk + wrap-up time | Call events |
| Reason Codes | Reason Code | fact_agent_intervals.reason_code | Direct mapping | BusiedOut events |
| List agents logged in | Agents Logged In | STRING_AGG(dim_agent.agent_name) | Distinct agents in period | Login events |
| Average agents logged in | Avg Agents | AVG(agent_count) | Average concurrent agents | Login/Logout events |
| Service Level | Calls Answered in 10s% | (SUM(answered_within_10s) / COUNT(*)) * 100 | Service level calculation | Call events |

### Report 2: ACD - Call Queue Summary Dashboard

**Purpose**: Dashboard view of queue performance metrics.

| Friendly Name | Database Field | Calculation | Source Events |
|---------------|----------------|-------------|---------------|
| Queue Name | dim_queue.ring_group_name | Direct mapping | ACDLogin events |
| Calls | COUNT(fact_call.call_identifier) | Total calls per queue | Call events |
| Calls Answered in 10s% | (SUM(answered_within_10s) / COUNT(*)) * 100 | Service level | Call events |
| Abandoned | COUNT(fact_call.call_identifier) | WHERE is_abandoned = true | Call events |
| Logged in Time | SUM(fact_agent_intervals.duration_seconds) | WHERE interval_type = 'LoggedIn' | Login/Logout events |
| Agents Logged In | COUNT(DISTINCT agent_key) | Unique agents per period | Login events |
| Group Utilization% | (available_time / logged_in_time) * 100 | Utilization calculation | Available/Login events |

### Report 3: ACD - Call Taking Group Overview

**Purpose**: High-level efficiency metrics by ACD group.

| Database Field | Calculation | Source Events |
|----------------|-------------|---------------|
| acdGroup | dim_queue.ring_group_name | ACDLogin events |
| calls | COUNT(fact_call.call_identifier) | Call events |
| callsAnsweredWithin10s/15s/20s/40s | Service level breakdowns | Call timing data |
| callsAbandoned | COUNT WHERE is_abandoned = true | Call events |
| callsTransferred | COUNT WHERE is_transferred = true | Call events |
| loggedInTime | SUM(logged_in_duration_seconds) | Login/Logout events |
| availableTime | SUM(available_duration_seconds) | Available/BusiedOut events |
| groupUtilization% | (availableTime / loggedInTime) * 100 | Calculated from above |

### Report 4: Agent Performance - Call Distribution

**Purpose**: Individual agent performance with available time tracking.

| RFP Field | Database Field | Calculation | Source Events |
|-----------|----------------|-------------|---------------|
| acdGroup | dim_queue.ring_group_name | From ACD sessions | ACDLogin |
| calls | Average calls per shift | Total calls / number of shifts | Call events |
| availableTime | Available time per shift | SUM(available_time_seconds) per shift | Available/BusiedOut |
| agentName | dim_agent.agent_name | Direct mapping | Login events |
| agentCount | Average agents per interval | COUNT(DISTINCT agent_key) | Login events |
| agentAnsweredWithin10s% | Individual service level | Agent-specific calculation | Call events |
| agentAnsweredWithin15s% | Individual service level | Agent-specific calculation | Call events |
| agentAnsweredWithin20s% | Individual service level | Agent-specific calculation | Call events |
| agentAnsweredWithin40s% | Individual service level | Agent-specific calculation | Call events |
| agentAnsweredGreater40s% | Individual service level | Agent-specific calculation | Call events |

### Report 5: Emergency Agent Performance

**Purpose**: Emergency call specific agent performance metrics.

| RFP Field | Database Field | Calculation | Source Events |
|-----------|----------------|-------------|---------------|
| acdGroup | dim_queue.ring_group_name | From ACD sessions | ACDLogin |
| calls | Average emergency calls per shift | Emergency calls / shifts | Call events (emergency only) |
| availableTime | Available time per shift | SUM(available_time_seconds) per shift | Available/BusiedOut |
| agentName | dim_agent.agent_name | Direct mapping | Login events |
| agentAnsweredWithin10s% | Emergency service level | WHERE is_emergency = true | Call events |
| agentAnsweredWithin15s% | Emergency service level | WHERE is_emergency = true | Call events |
| agentAnsweredWithin20s% | Emergency service level | WHERE is_emergency = true | Call events |
| agentAnsweredWithin40s% | Emergency service level | WHERE is_emergency = true | Call events |
| agentAnsweredGreater40s% | Emergency service level | WHERE is_emergency = true | Call events |

## Key Calculations Explained

### Available Time Calculation
Available time is calculated as the sum of all intervals where an agent is in "Available" state:

```sql
SELECT
    agent_key,
    DATE(interval_start_timestamp) as shift_date,
    SUM(duration_seconds) as available_time_seconds
FROM fact_agent_intervals
WHERE interval_type = 'Available'
GROUP BY agent_key, DATE(interval_start_timestamp);
```

### Service Level Calculations
Service levels are calculated using conditional aggregation:

```sql
SELECT
    queue_name,
    COUNT(*) as total_calls,
    SUM(CASE WHEN agent_time_to_answer_seconds <= 10 THEN 1 ELSE 0 END) as answered_within_10s,
    (SUM(CASE WHEN agent_time_to_answer_seconds <= 10 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) as service_level_10s
FROM fact_call fc
JOIN dim_queue dq ON fc.queue_key = dq.queue_key
WHERE call_answered_time IS NOT NULL
GROUP BY queue_name;
```

### Group Utilization Calculation
Group utilization compares available time to logged-in time:

```sql
WITH agent_times AS (
    SELECT
        queue_key,
        DATE(interval_start_timestamp) as report_date,
        SUM(CASE WHEN interval_type = 'LoggedIn' THEN duration_seconds ELSE 0 END) as logged_in_seconds,
        SUM(CASE WHEN interval_type = 'Available' THEN duration_seconds ELSE 0 END) as available_seconds
    FROM fact_agent_intervals fai
    JOIN fact_acd_session fas ON fai.agent_key = fas.agent_key
    GROUP BY queue_key, DATE(interval_start_timestamp)
)
SELECT
    queue_key,
    report_date,
    logged_in_seconds,
    available_seconds,
    (available_seconds * 100.0 / NULLIF(logged_in_seconds, 0)) as utilization_percentage
FROM agent_times;
```

### Average Calls Per Shift Calculation
Shifts are defined as login-to-logout periods within a 24-hour window:

```sql
WITH agent_shifts AS (
    SELECT
        agent_key,
        DATE(interval_start_timestamp) as shift_date,
        COUNT(DISTINCT interval_key) as shift_count
    FROM fact_agent_intervals
    WHERE interval_type = 'LoggedIn'
    GROUP BY agent_key, DATE(interval_start_timestamp)
),
agent_calls AS (
    SELECT
        da.agent_key,
        DATE(fc.call_start_time) as call_date,
        COUNT(*) as total_calls
    FROM fact_call fc
    JOIN dim_agent da ON fc.agent_name = da.agent_name
    GROUP BY da.agent_key, DATE(fc.call_start_time)
)
SELECT
    ac.agent_key,
    ac.call_date,
    ac.total_calls,
    COALESCE(ash.shift_count, 1) as shift_count,
    (ac.total_calls * 1.0 / COALESCE(ash.shift_count, 1)) as average_calls_per_shift
FROM agent_calls ac
LEFT JOIN agent_shifts ash ON ac.agent_key = ash.agent_key AND ac.call_date = ash.shift_date;
```

## AWS Architecture Implementation

### Component Diagram

```mermaid
graph TB
    subgraph "Data Ingestion"
        S3[S3 Bucket<br/>Per Tenant]
        SNS[SNS Topic<br/>Event Router]
    end

    subgraph "Event Processing"
        SQS1[SQS Queue<br/>Agent Events]
        SQS2[SQS Queue<br/>Call Events]
        Lambda1[Lambda<br/>Agent Processor]
        Lambda2[Lambda<br/>Call Processor]
    end

    subgraph "Data Storage"
        S3Staging[S3 Staging<br/>Processed Data]
        Redshift[Redshift Cluster<br/>Star Schema]
    end

    subgraph "Reporting"
        PowerBI[Power BI<br/>Reports & Dashboards]
        Views[Redshift Views<br/>Report Calculations]
    end

    S3 --> SNS
    SNS --> SQS1
    SNS --> SQS2
    SQS1 --> Lambda1
    SQS2 --> Lambda2
    Lambda1 --> S3Staging
    Lambda2 --> S3Staging
    S3Staging --> Redshift
    Redshift --> Views
    Views --> PowerBI
```

### Lambda Function Architecture

#### Agent Event Processor Lambda

**Purpose**: Process agent state events and calculate intervals

**Trigger**: SQS messages from agent events
**Runtime**: Python 3.9
**Memory**: 512 MB
**Timeout**: 5 minutes

**Key Functions**:
1. Parse agent events (Login, Logout, Available, BusiedOut, ACDLogin, ACDLogout)
2. Maintain agent state in DynamoDB for real-time processing
3. Calculate time intervals
4. Transform data for Redshift format
5. Write to S3 staging area

**Environment Variables**:
- `REDSHIFT_CLUSTER_ENDPOINT`
- `S3_STAGING_BUCKET`
- `DYNAMODB_AGENT_STATE_TABLE`
- `TENANT_CONFIG_TABLE`

#### Call Event Processor Lambda

**Purpose**: Process call events and link to agent data

**Trigger**: SQS messages from call events
**Runtime**: Python 3.9
**Memory**: 256 MB
**Timeout**: 3 minutes

**Key Functions**:
1. Parse call events
2. Enrich with agent information
3. Calculate service level metrics
4. Transform for Redshift format
5. Write to S3 staging area

### Data Flow Sequence

```mermaid
sequenceDiagram
    participant S3 as S3 Bucket
    participant SNS as SNS Topic
    participant SQS as SQS Queue
    participant Lambda as Agent Processor
    participant DDB as DynamoDB
    participant S3Stage as S3 Staging
    participant RS as Redshift

    S3->>SNS: Event file uploaded
    SNS->>SQS: Route agent events
    SQS->>Lambda: Trigger processing
    Lambda->>DDB: Get current agent state
    Lambda->>Lambda: Process event & calculate intervals
    Lambda->>DDB: Update agent state
    Lambda->>S3Stage: Write processed data
    S3Stage->>RS: COPY command (scheduled)
    RS->>RS: Update star schema tables
```

### DynamoDB Agent State Table

**Purpose**: Maintain current agent state for real-time interval calculations

```json
{
    "TableName": "agent-state",
    "KeySchema": [
        {
            "AttributeName": "tenant_agent_key",
            "KeyType": "HASH"
        }
    ],
    "AttributeDefinitions": [
        {
            "AttributeName": "tenant_agent_key",
            "AttributeType": "S"
        }
    ],
    "BillingMode": "PAY_PER_REQUEST",
    "StreamSpecification": {
        "StreamEnabled": true,
        "StreamViewType": "NEW_AND_OLD_IMAGES"
    }
}
```

**Sample Record**:
```json
{
    "tenant_agent_key": "peoriacounty-il#001",
    "agent_name": "John Doe",
    "operator_id": "001",
    "current_state": "available",
    "login_time": "2025-02-17T06:00:00Z",
    "last_state_change": "2025-02-17T06:15:00Z",
    "current_acd_queues": ["Police", "Fire"],
    "open_intervals": [
        {
            "interval_type": "logged_in",
            "start_time": "2025-02-17T06:00:00Z"
        },
        {
            "interval_type": "available",
            "start_time": "2025-02-17T06:15:00Z"
        }
    ]
}
```

### Redshift Data Loading Strategy

#### Staging Tables
Create staging tables that match the structure of fact tables but without constraints:

```sql
CREATE TABLE staging_agent_intervals (
    agent_key BIGINT,
    tenant_key BIGINT,
    date_key INT,
    start_time_key INT,
    end_time_key INT,
    interval_start_timestamp TIMESTAMP,
    interval_end_timestamp TIMESTAMP,
    interval_type VARCHAR(50),
    duration_seconds INT,
    reason_code VARCHAR(100),
    shift_identifier VARCHAR(50),
    load_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### COPY Commands
Use Redshift COPY commands for efficient bulk loading:

```sql
COPY staging_agent_intervals
FROM 's3://agent-reporting-staging/tenant=peoriacounty-il/year=2025/month=02/day=17/agent_intervals/'
IAM_ROLE 'arn:aws:iam::account:role/RedshiftCopyRole'
FORMAT AS PARQUET
TIMEFORMAT 'auto';
```

#### Merge Strategy
Use MERGE or INSERT/UPDATE pattern to handle late-arriving data:

```sql
-- Insert new records
INSERT INTO fact_agent_intervals
SELECT * FROM staging_agent_intervals s
WHERE NOT EXISTS (
    SELECT 1 FROM fact_agent_intervals f
    WHERE f.agent_key = s.agent_key
    AND f.interval_start_timestamp = s.interval_start_timestamp
);

-- Update existing records if needed
UPDATE fact_agent_intervals
SET
    interval_end_timestamp = s.interval_end_timestamp,
    duration_seconds = s.duration_seconds
FROM staging_agent_intervals s
WHERE fact_agent_intervals.agent_key = s.agent_key
AND fact_agent_intervals.interval_start_timestamp = s.interval_start_timestamp
AND s.interval_end_timestamp IS NOT NULL;
```

### Multi-Tenant Architecture

#### Tenant Isolation Strategy

1. **S3 Bucket Structure**:
   ```
   s3://agent-reporting-data/
   ├── tenant=peoriacounty-il/
   │   ├── year=2025/month=02/day=17/
   │   │   ├── agent_events/
   │   │   └── call_events/
   ├── tenant=chicago-il/
   │   ├── year=2025/month=02/day=17/
   │   │   ├── agent_events/
   │   │   └── call_events/
   ```

2. **Redshift Schema Design**:
   - All tables include `tenant_key` for row-level security
   - Use Redshift's Row-Level Security (RLS) for tenant isolation
   - Create tenant-specific views for Power BI

3. **Lambda Function Routing**:
   - Use SNS message attributes to route by tenant
   - Separate SQS queues per tenant for isolation
   - Environment variables for tenant-specific configuration

#### Tenant Configuration

```json
{
    "tenant_configs": {
        "peoriacounty-il": {
            "redshift_schema": "peoria_reporting",
            "s3_prefix": "tenant=peoriacounty-il",
            "timezone": "America/Chicago",
            "service_level_thresholds": {
                "emergency": 10,
                "admin": 15
            }
        },
        "chicago-il": {
            "redshift_schema": "chicago_reporting",
            "s3_prefix": "tenant=chicago-il",
            "timezone": "America/Chicago",
            "service_level_thresholds": {
                "emergency": 8,
                "admin": 12
            }
        }
    }
}
```

## Power BI Integration

### Connection Strategy

**Import Mode with Hourly Refresh**
- Use Power BI Import mode for better performance
- Schedule hourly refreshes to meet near real-time requirements
- Pre-aggregate data in Redshift views for faster imports

### Report Views in Redshift

#### ACD Detailed Calls by Group View
```sql
CREATE VIEW vw_acd_detailed_calls_by_group AS
SELECT
    dq.ring_group_name as acd_ring_group,
    DATE(fc.call_start_time) as report_date,
    EXTRACT(HOUR FROM fc.call_start_time) as report_hour,
    COUNT(fc.call_identifier) as calls_answered,
    COUNT(CASE WHEN fc.transfer_from IS NOT NULL THEN 1 END) as transferred_in,
    COUNT(CASE WHEN fc.transfer_to IS NOT NULL THEN 1 END) as transferred_out,
    SUM(logged_in_seconds) / 3600.0 as staffed_time_hours,
    SUM(available_seconds) / 3600.0 as available_time_hours,
    SUM(fc.talk_time_seconds) / 3600.0 as talk_time_hours,
    AVG(fc.talk_time_seconds) as avg_talk_time_seconds,
    STRING_AGG(DISTINCT da.agent_name, ', ') as agents_logged_in,
    AVG(concurrent_agents) as avg_agents_logged_in,
    (SUM(fc.answered_within_10s) * 100.0 / COUNT(fc.call_identifier)) as service_level_10s
FROM fact_call fc
JOIN dim_agent da ON fc.agent_name = da.agent_name
JOIN fact_acd_session fas ON da.agent_key = fas.agent_key
JOIN dim_queue dq ON fas.queue_key = dq.queue_key
LEFT JOIN (
    SELECT
        queue_key,
        DATE(interval_start_timestamp) as interval_date,
        EXTRACT(HOUR FROM interval_start_timestamp) as interval_hour,
        SUM(CASE WHEN interval_type = 'LoggedIn' THEN duration_seconds ELSE 0 END) as logged_in_seconds,
        SUM(CASE WHEN interval_type = 'Available' THEN duration_seconds ELSE 0 END) as available_seconds,
        COUNT(DISTINCT agent_key) as concurrent_agents
    FROM fact_agent_intervals fai
    JOIN fact_acd_session fas2 ON fai.agent_key = fas2.agent_key
    GROUP BY queue_key, DATE(interval_start_timestamp), EXTRACT(HOUR FROM interval_start_timestamp)
) agent_stats ON dq.queue_key = agent_stats.queue_key
    AND DATE(fc.call_start_time) = agent_stats.interval_date
    AND EXTRACT(HOUR FROM fc.call_start_time) = agent_stats.interval_hour
WHERE fc.call_answered_time IS NOT NULL
GROUP BY dq.ring_group_name, DATE(fc.call_start_time), EXTRACT(HOUR FROM fc.call_start_time);
```

#### Call Queue Summary Dashboard View
```sql
CREATE VIEW vw_call_queue_summary AS
SELECT
    dq.ring_group_name as queue_name,
    DATE(fc.call_start_time) as report_date,
    COUNT(fc.call_identifier) as total_calls,
    (SUM(fc.answered_within_10s) * 100.0 / COUNT(fc.call_identifier)) as calls_answered_in_10s_percent,
    COUNT(CASE WHEN fc.is_abandoned = true THEN 1 END) as calls_abandoned,
    SUM(logged_in_seconds) / 3600.0 as logged_in_time_hours,
    COUNT(DISTINCT da.agent_key) as agents_logged_in,
    (SUM(available_seconds) * 100.0 / NULLIF(SUM(logged_in_seconds), 0)) as group_utilization_percent
FROM fact_call fc
JOIN dim_agent da ON fc.agent_name = da.agent_name
JOIN fact_acd_session fas ON da.agent_key = fas.agent_key
JOIN dim_queue dq ON fas.queue_key = dq.queue_key
LEFT JOIN (
    SELECT
        queue_key,
        DATE(interval_start_timestamp) as interval_date,
        SUM(CASE WHEN interval_type = 'LoggedIn' THEN duration_seconds ELSE 0 END) as logged_in_seconds,
        SUM(CASE WHEN interval_type = 'Available' THEN duration_seconds ELSE 0 END) as available_seconds
    FROM fact_agent_intervals fai
    JOIN fact_acd_session fas2 ON fai.agent_key = fas2.agent_key
    GROUP BY queue_key, DATE(interval_start_timestamp)
) agent_stats ON dq.queue_key = agent_stats.queue_key
    AND DATE(fc.call_start_time) = agent_stats.interval_date
GROUP BY dq.ring_group_name, DATE(fc.call_start_time);
```

#### Agent Performance View
```sql
CREATE VIEW vw_agent_performance AS
WITH agent_shifts AS (
    SELECT
        agent_key,
        DATE(interval_start_timestamp) as shift_date,
        COUNT(*) as shift_count,
        SUM(CASE WHEN interval_type = 'Available' THEN duration_seconds ELSE 0 END) as available_seconds
    FROM fact_agent_intervals
    WHERE interval_type IN ('LoggedIn', 'Available')
    GROUP BY agent_key, DATE(interval_start_timestamp)
),
agent_calls AS (
    SELECT
        da.agent_key,
        dq.ring_group_name as acd_group,
        DATE(fc.call_start_time) as call_date,
        COUNT(*) as total_calls,
        SUM(fc.answered_within_10s) as answered_within_10s,
        SUM(fc.answered_within_15s) as answered_within_15s,
        SUM(fc.answered_within_20s) as answered_within_20s,
        SUM(fc.answered_within_40s) as answered_within_40s,
        COUNT(*) - SUM(fc.answered_within_40s) as answered_greater_40s
    FROM fact_call fc
    JOIN dim_agent da ON fc.agent_name = da.agent_name
    JOIN fact_acd_session fas ON da.agent_key = fas.agent_key
    JOIN dim_queue dq ON fas.queue_key = dq.queue_key
    GROUP BY da.agent_key, dq.ring_group_name, DATE(fc.call_start_time)
)
SELECT
    da.agent_name,
    ac.acd_group,
    ac.call_date,
    (ac.total_calls * 1.0 / COALESCE(ash.shift_count, 1)) as average_calls_per_shift,
    ash.available_seconds / 3600.0 as available_time_hours,
    (ac.answered_within_10s * 100.0 / ac.total_calls) as answered_within_10s_percent,
    (ac.answered_within_15s * 100.0 / ac.total_calls) as answered_within_15s_percent,
    (ac.answered_within_20s * 100.0 / ac.total_calls) as answered_within_20s_percent,
    (ac.answered_within_40s * 100.0 / ac.total_calls) as answered_within_40s_percent,
    (ac.answered_greater_40s * 100.0 / ac.total_calls) as answered_greater_40s_percent
FROM agent_calls ac
JOIN dim_agent da ON ac.agent_key = da.agent_key
LEFT JOIN agent_shifts ash ON ac.agent_key = ash.agent_key AND ac.call_date = ash.shift_date;
```

### Power BI Data Model

**Relationships**:
- Date dimension connected to all fact tables
- Agent dimension connected to performance views
- Queue dimension connected to call summary views
- Tenant dimension for multi-tenant filtering

**Measures**:
```dax
Service Level 10s =
DIVIDE(
    SUM(vw_call_queue_summary[calls_answered_within_10s]),
    SUM(vw_call_queue_summary[total_calls])
) * 100

Group Utilization =
DIVIDE(
    SUM(vw_call_queue_summary[available_time_hours]),
    SUM(vw_call_queue_summary[logged_in_time_hours])
) * 100

Average Calls Per Shift =
AVERAGE(vw_agent_performance[average_calls_per_shift])
```

## Monitoring and Alerting

### CloudWatch Metrics

**Lambda Function Metrics**:
- Invocation count and duration
- Error rate and throttling
- Memory utilization

**Custom Metrics**:
- Events processed per minute
- Data latency (event timestamp to Redshift insertion)
- Agent state inconsistencies

**Redshift Metrics**:
- Query performance
- Storage utilization
- Connection count

### Alerting Strategy

**Critical Alerts**:
- Lambda function failures > 5% error rate
- Data pipeline delays > 30 minutes
- Redshift cluster unavailability

**Warning Alerts**:
- High Lambda memory utilization > 80%
- Slow query performance > 30 seconds
- Agent state data gaps > 15 minutes

### Data Quality Monitoring

**Automated Checks**:
1. **Completeness**: Verify all expected events are processed
2. **Consistency**: Check agent state transitions are logical
3. **Timeliness**: Monitor data freshness
4. **Accuracy**: Validate calculated metrics against known values

**Sample Data Quality Query**:
```sql
-- Check for agent state inconsistencies
SELECT
    agent_key,
    event_timestamp,
    event_type,
    LAG(event_type) OVER (PARTITION BY agent_key ORDER BY event_timestamp) as previous_event
FROM fact_agent_state
WHERE (event_type = 'Available' AND LAG(event_type) OVER (PARTITION BY agent_key ORDER BY event_timestamp) = 'Available')
   OR (event_type = 'BusiedOut' AND LAG(event_type) OVER (PARTITION BY agent_key ORDER BY event_timestamp) = 'BusiedOut');
```

## Implementation Timeline

### Phase 1: Foundation (Weeks 1-2)
- [ ] Set up AWS infrastructure (Lambda, SQS, S3, Redshift)
- [ ] Create star schema tables in Redshift
- [ ] Implement basic agent event processor Lambda
- [ ] Set up DynamoDB agent state table

### Phase 2: Core Processing (Weeks 3-4)
- [ ] Implement agent state tracking logic
- [ ] Create interval calculation algorithms
- [ ] Build call event processor Lambda
- [ ] Implement data loading pipeline to Redshift

### Phase 3: Reporting (Weeks 5-6)
- [ ] Create Redshift views for all 5 reports
- [ ] Build Power BI data model and reports
- [ ] Implement tenant-specific filtering
- [ ] Set up automated refresh schedules

### Phase 4: Testing & Optimization (Weeks 7-8)
- [ ] End-to-end testing with sample data
- [ ] Performance optimization
- [ ] Data quality validation
- [ ] User acceptance testing

### Phase 5: Production Deployment (Week 9)
- [ ] Production environment setup
- [ ] Data migration and backfill
- [ ] Monitoring and alerting configuration
- [ ] Go-live and support

## Risk Assessment and Mitigation

### Technical Risks

**Risk**: Agent state calculation complexity
**Mitigation**: Implement comprehensive unit tests and validation logic

**Risk**: Data latency affecting real-time requirements
**Mitigation**: Use DynamoDB for real-time state, batch processing for historical data

**Risk**: Redshift performance with large data volumes
**Mitigation**: Implement proper distribution keys, sort keys, and data compression

### Operational Risks

**Risk**: Multi-tenant data isolation
**Mitigation**: Implement row-level security and thorough testing

**Risk**: Power BI refresh failures
**Mitigation**: Implement retry logic and fallback data sources

**Risk**: Event ordering and late arrivals
**Mitigation**: Use event timestamps for ordering, implement late data handling

## Success Criteria

1. **Functional Requirements**:
   - All 5 reports generate accurate data matching RFP specifications
   - Real-time agent state tracking with < 5 minute latency
   - Multi-tenant support with proper data isolation

2. **Performance Requirements**:
   - Process 100,000+ events per hour per tenant
   - Power BI reports load within 30 seconds
   - 99.9% system availability

3. **Data Quality Requirements**:
   - < 0.1% data loss or corruption
   - Agent state consistency > 99.5%
   - Report calculations accurate within 1%

## Conclusion

This technical spike provides a comprehensive design for implementing an agent reporting system using AWS serverless architecture. The solution addresses all RFP requirements while providing scalability, multi-tenant support, and real-time processing capabilities. The star schema design enables efficient querying for Power BI reports, and the event-driven architecture ensures data freshness and system responsiveness.
