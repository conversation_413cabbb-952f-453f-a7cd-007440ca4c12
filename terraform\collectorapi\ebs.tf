# Beanstalk instance profile
resource "aws_iam_instance_profile" "infohub_beanstalk_ec2" {
  name  = "infohub-beanstalk-ec2-user"
  roles = ["${aws_iam_role.infohub_beanstalk_ec2.name}"]
}


resource "aws_iam_role" "infohub_beanstalk_ec2" {
  name = "infohub-beanstalk-ec2-role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "ec2.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF

  tags  = {
    Environment = "${terraform.workspace}"
    Name        = "CollectorAPI-${terraform.workspace}"
  }
}


# Beanstalk EC2 Policy
# Overriding default policy since Elastic Beanstalk does not have a permission to access ECR
resource "aws_iam_role_policy" "infohub_beanstalk_ec2_policy" {
  name = "infohub_beanstalk_ec2_policy_with_ECR"
  role = "${aws_iam_role.infohub_beanstalk_ec2.id}"

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": [
        "cloudwatch:PutMetricData",
        "ds:CreateComputer",
        "ds:DescribeDirectories",
        "ec2:DescribeInstanceStatus",
        "logs:*",
        "ssm:*",
        "ec2messages:*",
        "ecr:GetAuthorizationToken",
        "ecr:BatchCheckLayerAvailability",
        "ecr:GetDownloadUrlForLayer",
        "ecr:GetRepositoryPolicy",
        "ecr:DescribeRepositories",
        "ecr:ListImages",
        "ecr:DescribeImages",
        "ecr:BatchGetImage",
        "s3:*"
      ],
      "Effect": "Allow",
      "Resource": "*"
    }
  ]
}
EOF
}


# Beanstalk Application
resource "aws_elastic_beanstalk_application" "infohub_beanstalk_application" {
  name        = "${var.ebs_application_name}-${terraform.workspace}"
  description = "${terraform.workspace} ${var.ebs_application_description}"

  tags  = {
    Environment = "${terraform.workspace}"
    Name        = "CollectorAPI-${terraform.workspace}"
  }  
}


# Beanstalk Environment
# solution_stack_name -> https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/concepts.platforms.html
resource "aws_elastic_beanstalk_environment" "infohub_beanstalk_environment" {
  name                = "${var.ebs_application_name}-${terraform.workspace}"
  application         = "${aws_elastic_beanstalk_application.infohub_beanstalk_application.name}"
  solution_stack_name = "64bit Amazon Linux 2018.03 v2.12.16 running Docker 18.06.1-ce"
  tier                = "WebServer"

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "InstanceType"

    # Todo: As Variable
    value = "t2.micro"
  }

  setting {
    namespace = "aws:autoscaling:asg"
    name      = "MaxSize"

    # Todo: As Variable
    value = "2"
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "IamInstanceProfile"
    value     = "${aws_iam_instance_profile.infohub_beanstalk_ec2.name}"
  }

  tags  = {
    Environment = "${terraform.workspace}"
    Name        = "CollectorAPI-${terraform.workspace}"
  }  
}


output "ebs_region" {
  description = "Elastic Beanstalk region"
  value       = "${lookup(var.aws_region, terraform.workspace)}"
}


output "ebs_application_name" {
  description = "Elastic Beanstalk application name"
  value       = "${aws_elastic_beanstalk_application.infohub_beanstalk_application.name}"
}


output "ebs_environment_name" {
  description = "Elastic Beanstalk environment name"
  value       = "${aws_elastic_beanstalk_environment.infohub_beanstalk_environment.name}"
}


output "ebs_url" {
  description = "Elastic Beanstalk URL"
  value       = "${aws_elastic_beanstalk_environment.infohub_beanstalk_environment.cname}"
}
