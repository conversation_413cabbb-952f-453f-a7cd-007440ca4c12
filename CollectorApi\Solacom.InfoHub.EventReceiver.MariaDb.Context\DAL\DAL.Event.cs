﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MySqlConnector;
using Newtonsoft.Json;
using Solacom.InfoHub.EventReceiver.Entities;
using Solacom.InfoHub.EventReceiver.MariaDb.Context.Models;

namespace Solacom.InfoHub.EventReceiver.MariaDb.Context.DAL
{
    public partial class DAL
    {
        /// <summary>
        /// Retrieves a given Hash event
        /// </summary>
        /// <param name="hashedKey">Hashed key to search on</param>
        /// <param name="clientId">Unique Client idendifier</param>
        /// <returns>Event Hash object if found, empty string if not</returns>
        /// <exception cref="Exception"></exception>
        public string GetHashedEvent(string hashedKey, string clientId)
        {
            string rtnJsonStr = string.Empty;

            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "GetHashedEvent";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_hashed_key", hashedKey);
                _SQLCommand.Parameters.AddWithValue("p_client_id", clientId);

                OpenSQLConnection();

                MySqlDataReader dr = _SQLCommand.ExecuteReader();

                if (dr.Read())
                {
                    rtnJsonStr = (string)dr["hashed_data"];
                }

                dr.Close();
                dr = null;
            }
            finally
            {
                CleanupSQLResources();
            }

            return rtnJsonStr;
        }

        /// <summary>
        /// Retrieves a agent session
        /// </summary>
        /// <param name="mediaLabel">Media label</param>
        /// <param name="clientId">Unique Client idendifier</param>
        /// <returns>Agent Session Json string, empty string if not found.</returns>
        /// <exception cref="Exception"></exception>
        public string GetAgentSession(string mediaLabel, string clientId)
        {
            string rtnJsonStr = string.Empty;

            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "GetAgentSession";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_medialabel", mediaLabel);
                _SQLCommand.Parameters.AddWithValue("p_client_id", clientId);

                OpenSQLConnection();

                MySqlDataReader dr = _SQLCommand.ExecuteReader();

                if (dr.Read())
                {
                    rtnJsonStr = (string)dr["agent_data"];
                }

                dr.Close();
                dr = null;
            }
            finally
            {
                CleanupSQLResources();
            }

            return rtnJsonStr;
        }
        /// <summary>
        /// retrievs all events for a given Call identifier for processing up to a Maxiumum id.  Setting these events in processing state.
        /// </summary>
        /// <param name="callIdentifier">Unique call identifier</param>
        /// <param name="maxEventId">Maximum Event Id to base the data set from</param>
        /// <returns>collection of events, empty list if none found.</returns>
        /// <exception cref="Exception"></exception>
        public List<EventLog> GetEventsForProcessing(string callIdentifier, string clientId, int maxEventId)
        {
            List<EventLog> rtnList = new List<EventLog>();

            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "GetEventsForProcessing";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_callIdentifier", callIdentifier);
                _SQLCommand.Parameters.AddWithValue("p_clientId", clientId);                
                _SQLCommand.Parameters.AddWithValue("p_maxEventId", maxEventId);

                OpenSQLConnection();

                MySqlDataReader dr = _SQLCommand.ExecuteReader();

                EventLog eventLog;

                while (dr.Read())
                {
                    eventLog = JsonConvert.DeserializeObject<EventLog>((string)dr["Event_Data"]);
                    eventLog.DatabaseStateId = (int)dr["State_Id"];

                    rtnList.Add(eventLog);
                }

                dr.Close();
                dr = null;
            }
            finally
            {
                CleanupSQLResources();
            }

            return rtnList;
        }

        /// <summary>
        /// Retrieves the count for each unique Eventtype from the given call.  
        /// </summary>
        /// <param name="callIdentifier">unique call identifier</param>
        /// <returns>Lookup of unique Events and the counts of each, with the maximum state and id for processing awareness</returns>
        /// <exception cref="Exception"></exception>
        public (Dictionary<string, int>, int, int) GetEventTypeCount(string callIdentifier, string clientId, int maxEventId)
        {
            Dictionary<string, int> rtnEventCountLookup = new Dictionary<string, int>();
            int maxStateId = 0;
            int rtnMaxEventId = 0;

            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "GetEventTypeCount";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_callIdentifier", callIdentifier);
                _SQLCommand.Parameters.AddWithValue("p_clientId", clientId);
                _SQLCommand.Parameters.AddWithValue("p_maxEventId", maxEventId);

                OpenSQLConnection();

                MySqlDataReader dr = _SQLCommand.ExecuteReader();

                while (dr.Read())
                {
                    maxStateId = Math.Max(maxStateId, (int)dr["MaxStateId"]);
                    rtnMaxEventId = Math.Max(maxEventId, (int)dr["MaxEventId"]);
                    rtnEventCountLookup.Add(((string)dr["EventType"]).ToLower(), (int)(long)dr["EventCount"]);
                }

                dr.Close();
                dr = null;
            }
            finally
            {
                CleanupSQLResources();
            }

            return (rtnEventCountLookup, maxStateId, rtnMaxEventId);
        }

        /// <summary>
        /// Retrieves Call Identifiers for any Events that are in NEW state that have not been processed older than a number of hours
        /// </summary>
        /// <param name="olderthanHours">Older than number of hours</param>
        /// <returns>Collection of Call ids and associated Client Ids (CallIdentifier, ClientId)</returns>
        /// <exception cref="Exception"></exception>
        public IList<CallInstance> GetExpiredEventsCallId(int olderthanHours)
        {
            IList<CallInstance> rtnCallLookup = new List<CallInstance>();
            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "GetExpiredEventsCallId";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_olderthan_hours", olderthanHours);

                OpenSQLConnection();

                MySqlDataReader dr = _SQLCommand.ExecuteReader();

                while (dr.Read())
                {
                    rtnCallLookup.Add(new CallInstance()
                    {
                        CallIdentifier = (string)dr["Call_Identifier"],
                        ClientId = (string)dr["Client_Id"],
                        Date = (DateTime)dr["MaxDateCreated"]
                    });
                }

                dr.Close();
                dr = null;
            }
            finally
            {
                CleanupSQLResources();
            }

            return rtnCallLookup;
        }

        /// <summary>
        /// Sets Events state for a given Call identifier up to a Maxiumum id.
        /// </summary>
        /// <param name="callIdentifier">Unique call identifier</param>
        /// <param name="clientId">Unique Client id</param>
        /// <param name="maxEventId">Maximum Event Id to base the data set from</param>
        /// <param name="processStateId">Mapped state id to process state table</param>
        /// <returns>Count of records updated for tracing</returns>
        /// <remarks>This function is meant to be called by helper functions that are specific a State is set, not open ended.</remarks>
        /// <exception cref="Exception"></exception>
        public long SetEventState(string callIdentifier, string clientId, int maxEventId, int processStateId)
        {
            long eventsUpdateCount = 0;
            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "SetEventState";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_callIdentifier", callIdentifier);
                _SQLCommand.Parameters.AddWithValue("p_clientId", clientId);
                _SQLCommand.Parameters.AddWithValue("p_maxEventId", maxEventId);
                _SQLCommand.Parameters.AddWithValue("p_stateId", processStateId);

                OpenSQLConnection();

                MySqlDataReader dr = _SQLCommand.ExecuteReader();

                if (dr.Read())
                {
                    eventsUpdateCount = (long)dr["EventsUpdatedCount"];
                }

                dr.Close();
                dr = null;
            }
            finally
            {
                CleanupSQLResources();
            }

            return eventsUpdateCount;
        }

        /// <summary>
        /// Sets Call Identifier to the Process Queue
        /// </summary>
        /// <param name="callIdentifier">Unique call identifier</param>
        /// <param name="clientId">Unique Client id</param>
        /// <remarks>The underlying Procedure updates the date if already present in the queue</remarks>
        /// <exception cref="Exception"></exception>
        public void SetProcessQueue(string callIdentifier, string clientId)
        {
            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "SetProcessQueue";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_callIdentifier", callIdentifier);
                _SQLCommand.Parameters.AddWithValue("p_clientId", clientId);

                OpenSQLConnection();

                _SQLCommand.ExecuteNonQuery();
            }
            finally
            {
                CleanupSQLResources();
            }
        }

        /// <summary>
        /// Retrieves Call Identifiers for any calls that are in the process queue awaiting processing based on time span passed
        /// </summary>
        /// <param name="olderthanMinutes">Older than number of minutes</param>
        /// <returns>Collection of Call ids and associated Client Ids (CallIdentifier, ClientId)</returns>
        /// <exception cref="Exception"></exception>
        public IList<CallInstance> GetProcessQueueOlderThan(int olderthanMinutes)
        {
            IList<CallInstance> rtnCallLookup = new List<CallInstance>();

            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "GetProcessQueueOlderThan";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_olderthan_minutes", olderthanMinutes);

                OpenSQLConnection();

                MySqlDataReader dr = _SQLCommand.ExecuteReader();

                while (dr.Read())
                {
                    rtnCallLookup.Add(new CallInstance()
                    {
                        CallIdentifier = (string)dr["callIdentifier"],
                        ClientId = (string)dr["clientId"]
                    });
                }

                dr.Close();
                dr = null;
            }
            finally
            {
                CleanupSQLResources();
            }

            return rtnCallLookup;
        }

        /// <summary>
        /// Deletes Call Identifier in the Process Queue
        /// </summary>
        /// <param name="callIdentifier">Unique call identifier</param>
        /// <param name="clientId">Unique Client id</param>
        /// <exception cref="Exception"></exception>
        public void DeleteProcessQueue(string callIdentifier, string clientId)
        {
            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "DeleteProcessQueue";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_callIdentifier", callIdentifier);
                _SQLCommand.Parameters.AddWithValue("p_clientId", clientId);

                OpenSQLConnection();

                _SQLCommand.ExecuteNonQuery();
            }
            finally
            {
                CleanupSQLResources();
            }
        }

        /// <summary>
        /// Delete a specific hash record
        /// </summary>
        /// <param name="hasedKey">Hashed key to delete</param>
        /// <param name="clientId">Unique Client idendifier</param>
        /// <exception cref="Exception"></exception>
        public void DeleteHashedEvent(string hashedKey, string clientId)
        {
            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "DeleteHashedEvent";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_hashed_key", hashedKey);
                _SQLCommand.Parameters.AddWithValue("p_client_id", clientId);

                OpenSQLConnection();

                _SQLCommand.ExecuteNonQuery();
            }
            finally
            {
                CleanupSQLResources();
            }
        }

        /// <summary>
        /// Adds a Event. 
        /// </summary>
        /// <param name="callIdentifier">Unique call identifier</param>
        /// <param name="clientId">Unique Client idendifier</param>
        /// <param name="eventType">Type of Event as string</param>
        /// <param name="eventData">Event Data Json string</param>
        /// <returns>Inserted record id</returns>
        /// <exception cref="Exception"></exception>
        public int AddEvent(string callIdentifier, string clientId, string eventType, string eventData, int stateId)
        {
            int rtnEventId = 0;

            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "AddEvent";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_call_identifier", callIdentifier);
                _SQLCommand.Parameters.AddWithValue("p_client_id", clientId);
                _SQLCommand.Parameters.AddWithValue("p_eventType", eventType);
                _SQLCommand.Parameters.AddWithValue("p_event_data", eventData);
                _SQLCommand.Parameters.AddWithValue("p_state_id", stateId);

                OpenSQLConnection();

                MySqlDataReader dr = _SQLCommand.ExecuteReader();

                if (dr.Read())
                {
                    rtnEventId = (int)(ulong)dr["eventId"];
                }

                dr.Close();
                dr = null;
            }
            finally
            {
                CleanupSQLResources();
            }

            return rtnEventId;
        }

        /// <summary>
        /// Adds a hashed record to the system.  If the hashed key already exists, no action is taken. 
        /// </summary>
        /// <param name="clientId">Unique Client idendifier</param>
        /// <param name="hashedKey">Hashed lookup key</param>
        /// <param name="hashedData">Full event data</param>
        /// <exception cref="Exception"></exception>
        public void AddHashEvent(string clientId, string hashedKey, string hashedData)
        {
            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "AddHashEvent";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_client_id", clientId);
                _SQLCommand.Parameters.AddWithValue("p_hashed_key", hashedKey);
                _SQLCommand.Parameters.AddWithValue("p_hashed_data", hashedData);

                OpenSQLConnection();

                _SQLCommand.ExecuteNonQuery();
            }
            finally
            {
                CleanupSQLResources();
            }
        }

        /// <summary>
        /// Adds a Agent Session
        /// </summary>
        /// <param name="clientId">Unique Client idendifier</param>
        /// <param name="mediaLabel">Unique Media label</param>
        /// <param name="agentData">Json string Agent data </param>
        /// <exception cref="Exception"></exception>
        public void AddAgentSession(string clientId, string mediaLabel, string agentData)
        {
            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "AddAgentSession";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_client_id", clientId);
                _SQLCommand.Parameters.AddWithValue("p_medialabel", mediaLabel);
                _SQLCommand.Parameters.AddWithValue("p_agent_data", agentData);

                OpenSQLConnection();

                _SQLCommand.ExecuteNonQuery();
            }
            finally
            {
                CleanupSQLResources();
            }
        }

        /// <summary>
        /// Stores the basic information of a error source
        /// </summary>
        /// <param name="errorMessage">Error message</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        /// <remarks>Stores the Client and Call Id - no other data?- can be refactored to include more informative data elements.
        ///</remarks>
        public void AddProcessError(string errorMessage)
        {
            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "AddProcessError";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_call_identifier", -1);
                _SQLCommand.Parameters.AddWithValue("p_client_id", -1);
                _SQLCommand.Parameters.AddWithValue("p_error_message", errorMessage);
                _SQLCommand.Parameters.AddWithValue("p_state_id", 1);

                OpenSQLConnection();

                _SQLCommand.ExecuteNonQuery();
            }
            finally
            {
                CleanupSQLResources();
            }
        }

        /// <summary>
        /// Deletes a error record 
        /// </summary>
        /// <param name="data">Error record identifier</param>
        /// <exception cref="Exception"></exception>
        public void DeleteProcessError(string errorMessage)
        {
            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "DeleteProcessError";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_error_message", errorMessage);

                OpenSQLConnection();

                _SQLCommand.ExecuteNonQuery();
            }
            finally
            {
                CleanupSQLResources();
            }
        }

        /// <summary>
        /// Retrieves all error events available
        /// </summary>
        /// <returns>Collection of error event identifier strings</returns>
        /// <exception cref="Exception"></exception>
        public List<string> GetProcessErrorList()
        {
            List<string> rtnList = new List<string>();

            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "GetProcessErrorList";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;

                OpenSQLConnection();

                MySqlDataReader dr = _SQLCommand.ExecuteReader();

                while (dr.Read())
                {
                    rtnList.Add((string)dr["Error_Message"]);
                }

                dr.Close();
                dr = null;
            }
            finally
            {
                CleanupSQLResources();
            }

            return rtnList;
        }
          
        /// <summary>
        /// Deletes data from the identified tables based on the Olderthan parameters.  Excludes any records in ErrorState (StateId=4)
        /// </summary>
        /// <param name="olderThan">Older than number of hours</param>
        /// <exception cref="exception"></exception>
        public void CleanUpTablesHashedEvents(int olderThan)
        {
            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "CleanUpTables_HashedEvents";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_olderthan_hours", olderThan);

                OpenSQLConnection();

                _SQLCommand.ExecuteNonQuery();
            }
            finally
            {
                CleanupSQLResources();
            }
        }

        /// <summary>
        /// Deletes data from the identified tables based on the Olderthan parameters.  Excludes any records in ErrorState (StateId=4)
        /// </summary>
        /// <param name="olderThan">Older than number of hours</param>
        /// <exception cref="exception"></exception>
        public void CleanUpTablesEvents(int olderThan)
        {
            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "CleanUpTables_Events";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_olderthan_hours", olderThan);

                OpenSQLConnection();

                _SQLCommand.ExecuteNonQuery();
            }
            finally
            {
                CleanupSQLResources();
            }
        }

        /// <summary>
        /// Deletes data from the identified tables based on the Olderthan parameters.  Excludes any records in ErrorState (StateId=4)
        /// </summary>
        /// <param name="olderThan">Older than number of hours</param>
        /// <exception cref="exception"></exception>
        public void CleanUpTablesAgentSession(int olderThan)
        {
            try
            {
                InitSQLConnectionObjects();

                _SQLCommand.CommandText = "CleanUpTables_AgentSession";
                _SQLCommand.CommandType = System.Data.CommandType.StoredProcedure;
                _SQLCommand.Parameters.AddWithValue("p_olderthan_hours", olderThan);

                OpenSQLConnection();

                _SQLCommand.ExecuteNonQuery();
            }
            finally
            {
                CleanupSQLResources();
            }
        }


    }
}
