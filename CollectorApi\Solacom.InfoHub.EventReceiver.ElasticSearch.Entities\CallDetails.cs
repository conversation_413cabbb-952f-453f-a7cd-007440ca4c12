﻿using System;

namespace Solacom.InfoHub.EventReceiver.ElasticSearch.Entities
{
//    public class CallDetails
//    {
//        public Guid Id { get; set; }
//        public Guid CallSummaryId { get; set; }
//        public string PsapName { get; set; }
//        public string LoginId { get; set; }
//        public DateTime? CallArrived { get; set; }
//        public string AgentName { get; set; }
//        public DateTime? CallPresented { get; set; }
//        public DateTime? CallAnswered { get; set; }
//        public DateTime? CallReleased { get; set; }
//        public bool? AnsweredBySystem { get; set; }
//        public DateTime TimeStamp { get; set; }
//    }
}