# Collector API AWS Terraform Configuration Files

The purpose of this repository is to document and simplify the setup process of the Collector API applications on AWS.


### Prerequisities
- AWS IAM Role with access to IAM, Elastic Beanstalk & Elastic Container Registry
- [Configuration of AWS Credential Files](https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-files.html)
- [HashiCorp Terraform](https://www.terraform.io/intro/getting-started/install.html)


### Iniitalize Terraform

The following is the initial setup for Terraform to save the state files:

In the global directory :

```
C:\terraform init

C:\terraform workspace new QA

C:\terraform workspace select QA

C:\terraform validate

C:\terraform plan -var-file="..\tfvars\secret.tfvars" -out "planfile"

C:\terraform apply -input=false "planfile"
```


### Deploy

The following is a deployment using QA as the environment:

In the collectorapi directory:

```
C:\terraform init

C:\terraform workspace new QA

C:\terraform workspace select QA

C:\terraform validate

C:\terraform plan -var-file="..\tfvars\secret.tfvars" -out "planfile"

C:\terraform apply -input=false "planfile"
```

### Destroy
```
C:\terraform destroy -var-file="..\tfvars\secret.tfvars"
```

## Variables
| Name | Purpose |
| --- | --- |
| db_instance_identifier | The name of the RDS instance |
| db_instance_name | The name of the database to create |
| db_instance_username | The Maria database user name |
| db_instance_password | The Maria database password |
| db_instance_tag_name | The Maria database tag name |
| db_instance_vpc_security_group_ids | List of VPC security groups to associate |
| ebs_application_name | The Elastic BeanStalk application name |
| ebs_application_description | The Elastic BeanStalk application description |


## Outputs

| Name | Purpose |
| --- | --- |
| ebs_application_name | The Elastic BeanStalk application name | 
| ebs_environment_name | The Elastic BeanStalk environment name |
| ebs_region | The region for the Elastic BeanStalk service | 
| ebs_url | The Elastic BeanStalk service URL | 
| ecr_repository_name | The Elastic Container Repository name | 
| ecr_repository_url | The Elastic Container Repository service URL| 
| mariadb_endpoint | The Maria database endpoint | 
| mariadb_password | The Maria database password | 
| mariadb_port | The Maria database port | 
| mariadb_username | The Maria database user name |  


### Deploy Sample Output

```
ebs_application_name = CollectorAPI-QA
ebs_environment_name = CollectorAPI-QA
ebs_region = us-west-1
ebs_url = CollectorAPI-QA.v8ppkzfrjt.us-west-1.elasticbeanstalk.com
ecr_repository_name = infohub/api
ecr_repository_url = 421986843000.dkr.ecr.us-west-1.amazonaws.com/infohub/api
mariadb_endpoint = mis-free-tier.cbkkrn1b9cb1.us-west-1.rds.amazonaws.com:3306
mariadb_password = Welcome123!
mariadb_port = 3306
mariadb_username = admin
````
