﻿using System.Collections.Generic;


namespace Solacom.InfoHub.EventReceiver.Entities
{
    /// <summary>
    /// Stores any Location data associated to the Raw Event data 
    /// </summary>
    /// <remarks>Populated based on HELDresponse and EIDD data.  Gets transformed to referenced ES.Entities.LocationData object in later processing steps.
    ///         This also stores ADR related information (serviceInfo, deviceInfo, providerInfo) that can be populated based on the Location information 
    ///         - this matches the way the data flow is; where given response of location can have the ADR data attached. 
    /// </remarks>
    /// <seealso cref="Solacom.InfoHub.EventReceiver.ElasticSearch.Entities.LocationData"/>
    public class LocationData
    {
        /// <summary>
        /// Required for serialization support 
        /// </summary>
        public LocationData()
        {

        }

        /// <summary>
        /// Main contructor, setting source of the Location information
        /// </summary>
        /// <param name="locationSource"></param>
        public LocationData(BusinessLogic.Enums.LocationEventSource locationEventSource)
        {
            LocationEventSource = locationEventSource;
        }

        public string Method { get; set; }

        public ServiceInfo ServiceInfo { get; set; }
        public DeviceInfo DeviceInfo { get; set; }
        public ProviderInfo ProviderInfo { get; set; }
        public EmergencyCallDataComment EmergencyCallDataComment { get; set; }


        public BusinessLogic.Enums.LocationEventSource LocationEventSource { get; set; }
        public CivicAddress CivicAddress { get; set; }
        public Ellipsoid Ellipsoid { get; set; }
        public Point Point { get; set; }
        public Circle Circle { get; set; }
        public Ellipse Ellipse { get; set; }
        public ArcBand ArcBand { get; set; }
        public Prism Prism { get; set; }
        public Sphere Sphere { get; set; }
        public Polygon Polygon { get; set; }
        public Confidence Confidence { get; set; }
    }

    /// <summary>
    /// Defines the Provider information (ADR data set)
    /// </summary>
    /// <remarks>https://www.rfc-editor.org/rfc/rfc7852#section-4.2.4</remarks>
    public class ProviderInfo
    {
        /// <summary>
        /// Data Provider reference
        /// </summary>
        public string DataProviderReference { get; set; }
        /// <summary>
        /// Name of the service provider
        /// </summary>
        /// <remarks>it SHOULD use the value of the contact header field in the SIP INVITE</remarks>
        public string DataProviderString { get; set; }
        /// <summary>
        /// Provider identifier
        /// </summary>
        /// <remarks>for US: Expected to be NENA (National Emergency Number Association) Company ID, else can be a IP address or fully qualified name</remarks>
        public string ProviderID { get; set; }
        /// <summary>
        /// Identifies the issuer of the ProviderID 
        /// </summary>
        /// <remarks>i.e. NENA for US</remarks>
        public string ProviderIDSeries { get; set; }
        /// <summary>
        /// Type of provider supplying data.  
        /// </summary>
        /// <remarks>based on fixed list registry - see linked spec above</remarks>
        public string TypeOfProvider { get; set; }
        /// <summary>
        /// Device or network provider contact information
        /// </summary>
        public string ContactURI { get; set; }
        /// <summary>
        /// Provider short code language
        /// </summary>
        public string Language { get; set; }
    }

    /// <summary>
    /// Defines the Device information (ADR data set)
    /// </summary>
    /// <remarks>rfc7852</remarks>
    public class DeviceInfo
    {
        /// <summary>
        /// Data provider reference
        /// </summary>
        public string DataProviderReference { get; set; }
        /// <summary>
        /// Classification 
        /// </summary>
        public string DeviceClassification { get; set; }
        /// <summary>
        /// Manifacture
        /// </summary>
        public string DeviceMfgr { get; set; }
        /// <summary>
        /// Model Number
        /// </summary>
        public string DeviceModelNr { get; set; }
        /// <summary>
        /// Attribute of Unique Device Id, designating the type
        /// </summary>
        /// <remarks>
        /// Available device types based on standard.
        /// | MEID   | Mobile Equipment Identifier(CDMA)     
        /// | ESN    | Electronic Serial Number(GSM)         
        /// | MAC    | Media Access Control Address(IEEE)    
        /// | WiMAX  | Device Certificate Unique ID          
        /// | IMEI   | International Mobile Equipment ID(GSM)
        /// | IMSI   | International Mobile Subscriber ID(GSM)
        /// | UDI    | Unique Device Identifier               
        /// | RFID   | Radio Frequency Identification         
        /// | SN     | Manufacturer Serial Number             
        /// </remarks>
        public string TypeOfDeviceID { get; set; }
        /// <summary>
        /// Unique device id
        /// </summary>
        public string UniqueDeviceID { get; set; }
    }

    /// <summary>
    /// Defines the Service Information related to the given geo Point information (ADR data set)
    /// </summary>
    /// <remarks>Main data point used for Class Of Service (COS) calculations.</remarks>
    public class ServiceInfo
    {
        /// <summary>
        /// Service type of the request (POTS/VOIP/wireless...)
        /// </summary>
        public string ServiceType { get; set; }
        /// <summary>
        /// Environment (residence,business, ...)
        /// </summary>
        public string ServiceEnvironment { get; set; }
        /// <summary>
        /// The mobiliity type (fixed, unknown, mobile, ...)
        /// </summary>
        public string ServiceMobility { get; set; }
        /// <summary>
        /// tracks the legacy COS
        /// </summary>
        public string Legacy_Class_Of_service { get; set; }
    }

    /// <summary>
    /// Defines the Specific ADR comment from the available data - populated as an additional data point from CHE
    /// </summary>
    public class EmergencyCallDataComment
    {
        /// <summary>
        /// Data Provider reference
        /// </summary>
        public string DataProviderReference { get; set; }
        /// <summary>
        /// Comment string
        /// </summary>
        /// <remarks>leveraged for UNI COS calc</remarks>
        public string Comment { get; set; }
    }


    /// <summary>
    /// Static COS object to enable single calculation point
    /// </summary>
    public static class ClassOfService
    {
        /// <summary>
        /// Stores the avialable COS definitions.
        /// </summary>
        /// <remarks>referenced from IQi3_GUI_and_SurveillanceServer/AlipsServer/Objects/ADRClassOfServices.cs
        private static List<ADRClassOfService> COSList = new List<ADRClassOfService>()
        {
            new ADRClassOfService("Manual", "POTS", "Residence", "Fixed", "Residence", "Residential", "Caller"),
            new ADRClassOfService("Manual", "POTS", "Business", "Fixed", "Business", "Business", "Caller"),
            new ADRClassOfService("Manual", "MLTS-local", "Residence", "Fixed", "Residence PBX", "Residential", "Caller"),
            new ADRClassOfService("Manual", "MLTS-local", "Business", "Fixed", "Business PBX", "Business", "Caller"),
            new ADRClassOfService("Manual", "MLTS-hosted", "Business", "Fixed", "Centrex", "Business", "Caller"),
            new ADRClassOfService("Manual", "coin;one-way", "Business", "Fixed", "Coin 1 way out", "Public", "Caller"),
            new ADRClassOfService("Manual", "coin", "Business", "Fixed", "Coin 2 way", "Public", "Caller"),
            new ADRClassOfService("Manual", "wireless", string.Empty, "Mobile", "WPH0", "Mobile", "TowerLocation"),
            new ADRClassOfService("Manual", "POTS;remote", "Residence", "Fixed", "Residence OPX", "Residential", "Caller"),
            new ADRClassOfService("Manual", "POTS;remote", "Business", "Fixed", "Business OPX", "Business", "Caller"),
            new ADRClassOfService("Manual", "Coin", "Business", "Fixed", "Customer owned Coin", "Public", "Caller"),
            new ADRClassOfService("Manual", "VOIP", "Residence", "Unknown", "VoIP Residence", "Residential", "Caller"),
            new ADRClassOfService("Manual", "VOIP", "Business", "Unknown", "VoIP Business", "Business", "Caller"),
            new ADRClassOfService("Manual", "VOIP;coin", "Business", "Unknown", "VoIP Coin or Pay Phone", "Public", "Caller"),
            new ADRClassOfService("Manual", "VOIP;wireless", string.Empty, "Mobile", "VoIP Wireless", "Mobile", "Caller"),
            new ADRClassOfService("Manual", "VOIP", string.Empty, "Nomadic", "VoIP Nomadic", "Residential", "Caller"),
            new ADRClassOfService("Manual", "VOIP", "Business", "Unknown", "VoIP Enterprise", "Business", "Caller"),
            new ADRClassOfService("Cell", "wireless", string.Empty, "Mobile", "WPH1", "Mobile", "TowerLocation"),
            new ADRClassOfService(string.Empty, "wireless", string.Empty, "Mobile", "WPH2", "Mobile", "Caller"),
            new ADRClassOfService("Manual", "VOIP", string.Empty, "Unknown", "VoIP", "Residential", "Caller"),
            new ADRClassOfService("Manual", "POTS;OPX", "Residence", "Fixed", "Residence OPX", "Residential", "Caller"),
            new ADRClassOfService("Manual", "POTS;OPX", "Business", "Fixed", "Business OPX", "Business", "Caller"),
            new ADRClassOfService("Manual", "OTT", "Residence", "Unknown", "VoIP Residence", "Residential", "Caller"),
            new ADRClassOfService("Manual", "digital", "Residence", "Unknown", "VoIP Residence", "Residential", "Caller"),
            new ADRClassOfService("Manual", "OTT", "Business", "Unknown", "VoIP Business", "Business", "Caller"),
            new ADRClassOfService("Manual", "digital", "Business", "Unknown", "VoIP Business", "Business", "Caller"),
            new ADRClassOfService("Manual", "digital;coin", "Business", "Unknown", "VoIP Coin or Pay Phone", "Public", "Caller"),
            new ADRClassOfService("Manual", "OTT", string.Empty, "Mobile", "VoIP Wireless", "Mobile", "Caller"),
            new ADRClassOfService("Manual", "digital;wireless", string.Empty, "Mobile",  "VoIP Wireless", "Mobile", "Caller"),
            new ADRClassOfService("Manual", "OTT", string.Empty, "Nomadic", "VoIP Nomadic", "Residential", "Caller"),
            new ADRClassOfService("Manual", "digital", string.Empty, "Nomadic", "VoIP Nomadic", "Residential", "Caller"),
            new ADRClassOfService("Manual", "OTT", string.Empty, "Unknown", "VoIP", "Residential", "Caller"),
            new ADRClassOfService("Manual", "OTT", "Unknown", "Unknown", "VOIP", "Residential", "Caller"),
            new ADRClassOfService("Manual", "digital", string.Empty, "Unknown", "VoIP", "Residential", "Caller")
        };

        /// <summary>
        /// Stores the available COS definitions from Bell spec
        /// </summary>
        /// <remarks>referenced from IQi3_GUI_and_SurveillanceServer/AlipsServer/Objects/ADRClassOfServices.cs - BellCosList.Add*</remarks>
        private static List<ADRClassOfService> BellCOSList = new List<ADRClassOfService>()
        {
            new ADRClassOfService("AFF", "Business",    "Caller"),
            new ADRClassOfService("BOP", "Business",    "Caller"),
            new ADRClassOfService("BUS", "Business",    "Caller"),
            new ADRClassOfService("CCC", "Business",    "Caller"),
            new ADRClassOfService("CCX", "Business",    "Caller"),
            new ADRClassOfService("CEL", "Mobile",      "TowerLocation"),
            new ADRClassOfService("CIP", "Residential", "Caller"),
            new ADRClassOfService("COM", "Business",    "Caller"),
            new ADRClassOfService("CPB", "Business",    "Caller"),
            new ADRClassOfService("CTX", "Business",    "Caller"),
            new ADRClassOfService("DAT", "Business",    "Caller"),
            new ADRClassOfService("DEA", "Residential", "Caller"),
            new ADRClassOfService("DID", "Business",    "Caller"),
            new ADRClassOfService("DRT", "Mobile",      "Caller"),
            new ADRClassOfService("FEX", "Business",    "Caller"),
            new ADRClassOfService("HDC", "Residential", "Caller"),
            new ADRClassOfService("LRR", "Residential", "Caller"),
            new ADRClassOfService("MIP", "Business",    "Caller"),
            new ADRClassOfService("MUL", "Business",    "Caller"),
            new ADRClassOfService("OUT", "Business",    "Caller"),
            new ADRClassOfService("PCS", "Mobile",      "Caller"),
            new ADRClassOfService("PUB", "Business",    "Caller"),
            new ADRClassOfService("PUC", "Business",    "Caller"),
            new ADRClassOfService("RES", "Residential", "Caller"),
            new ADRClassOfService("REV", "Business",    "Caller"),
            new ADRClassOfService("ROP", "Residential", "Caller"),
            new ADRClassOfService("SPP", "Business",    "Caller"),
            new ADRClassOfService("VEL", "Business",    "Caller"),
            new ADRClassOfService("VIP", "Business",    "Caller"),
            //Bell COS for Manitoba    
            new ADRClassOfService("CCRS", "Business",    "Caller"),
            new ADRClassOfService("RESD", "Residential", "Caller"),
            new ADRClassOfService("BUSN", "Business",    "Caller"),
            new ADRClassOfService("BPBX", "Business",    "Caller"),
            new ADRClassOfService("RPBX", "Residential", "Caller"),
            new ADRClassOfService("CTRX", "Business",    "Caller"),
            new ADRClassOfService("PAY$", "Business",    "Caller"),
            new ADRClassOfService("COIN", "Business",    "Caller"),
            new ADRClassOfService("CELL", "Mobile",      "TowerLocation"),
            new ADRClassOfService("CCTX", "Business",    "Caller"),
            new ADRClassOfService("WATS", "Business",    "Caller"),
            new ADRClassOfService("ANON", "Residential", "Caller"),
            new ADRClassOfService("HDCP", "Residential", "Caller"),
            new ADRClassOfService("OUTO", "Business"   , "Caller"),
            new ADRClassOfService("ROPX", "Residential", "Caller"),
            new ADRClassOfService("BOPX", "Business",    "Caller"),
            // Bell COS common to both Manitoba and non Manitoba
            new ADRClassOfService("WL2", "Mobile", "Caller"),
            new ADRClassOfService("TXE", "Mobile", "Caller"),
            new ADRClassOfService("TXF", "Mobile", "Caller")
        };

        /// <summary>
        /// Helper function that retrieves just the core COS string from the core adrCOS information
        /// </summary>
        /// <param name="pidfloMethod"></param>
        /// <returns>Class of service string value</returns>
        public static string GetCOSValue(string pidfloMethod, string serviceType, string serviceEnvironment, string serviceMobility)
        {
            ADRClassOfService cos = GetCOS(pidfloMethod, serviceType, serviceEnvironment, serviceMobility);

            return (cos != null ? cos.StringValue : null);
        }

        /// <summary>
        /// Based on the ServiceType, as mapped in the COSList - determines what call Mobility matches.
        /// </summary>
        /// <param name="serviceType">string service type</param>
        /// <returns>string mobility type</returns>
        /// <remarks>Logic is based on the available COS - as this list extends, this logic would have to expand.</remarks>
        public static string GetCallMobilityType(string serviceType)
        {
            string rtnCallMobilityType = string.Empty;
            
            switch (serviceType.ToLower())
            {
                case "pots":
                case "opx":
                case "mlts-local":
                case "mlts-hosted":
                case "coin;one-way":
                case "coin":
                case "pots:remote":
                    rtnCallMobilityType = "Landline";
                    break;
                case "wireless":
                case "voip;wireless":
                    rtnCallMobilityType = "Wireless";
                    break;
                case "voip":
                case "voip;coin":
                case "digital":
                    rtnCallMobilityType = "Voip";
                    break;
                default:
                    break;
            }

            return rtnCallMobilityType;
        }

        /// <summary>
        /// Based on parameters, searches the core COSList for matching COS.  
        /// </summary>
        /// <param name="pidfloMethod"></param>
        /// <param name="serviceType"></param>
        /// <param name="serviceEnvironment"></param>
        /// <param name="serviceMobility"></param>
        /// <returns>COS object</returns>
        /// <remarks>referenced from IQi3_GUI_and_SurveillanceServer/AlipsServer/Objects/ADRClassOfServices.cs
        /// modified for slight optimization in find query
        /// </remarks>
        private static ADRClassOfService GetCOS(string pidfloMethod, string serviceType, string serviceEnvironment, string serviceMobility)
        {
            //Simple sanity check for empty values - only service Type and mobility can not be empty at this time. 
            if (string.IsNullOrEmpty(serviceType) && string.IsNullOrEmpty(serviceMobility))
            {
                return null;
            }
            //edge case conditions if the data in was not defined in source event.
            if( pidfloMethod == null)   
            {
                pidfloMethod = string.Empty;
            }
            if( serviceEnvironment == null)
            {
                serviceEnvironment = string.Empty;
            }

            ADRClassOfService cos;

            // Check special case for Wireless Phase 2
            if (serviceType.ToLower() == "wireless" && serviceMobility.ToLower() == "mobile" && (pidfloMethod.ToLower() != "manual" && pidfloMethod.ToLower() != "cell"))
                cos = COSList.Find(c => c.Method.Equals(string.Empty, System.StringComparison.OrdinalIgnoreCase) &&
                                        c.ServiceType.Equals(serviceType, System.StringComparison.OrdinalIgnoreCase) &&
                                        c.ServiceEnvironment.Equals(string.Empty, System.StringComparison.OrdinalIgnoreCase) &&
                                        c.ServiceMobility.Equals(serviceMobility, System.StringComparison.OrdinalIgnoreCase));
            else
                cos = COSList.Find(c => c.Method.Equals(pidfloMethod, System.StringComparison.OrdinalIgnoreCase) &&
                                        c.ServiceType.Equals(serviceType, System.StringComparison.OrdinalIgnoreCase) &&
                                        c.ServiceEnvironment.Equals(serviceEnvironment, System.StringComparison.OrdinalIgnoreCase) &&
                                        c.ServiceMobility.Equals(serviceMobility, System.StringComparison.OrdinalIgnoreCase));

            return cos;
        }

        /// <summary>
        /// Validates a passed String COS value, to confirm if it is a expected defined COS string.
        /// Returns back "Unkown" if not found.
        /// </summary>
        /// <param name="cosString">string COS value</param>
        /// <returns>confirmed COS value if found, else, "Uknown"</returns>
        public static string ValidateCOSValue(string cosString)
        {
            ADRClassOfService findCos = BellCOSList.Find(c => c.StringValue.Equals(cosString, System.StringComparison.OrdinalIgnoreCase));
            if (findCos != null)
            {
                return findCos.StringValue;
            }
            else
            {
                return "Unknown";
            }

        }


        /// <summary>
        /// Main COS object defintion - sub object as reference usage is only in the scope of the Service Info object. 
        /// </summary>
        /// <remarks>referenced from IQi3_GUI_and_SurveillanceServer/AlipsServer/Objects/ADRClassOfServices.cs</remarks>
        protected class ADRClassOfService
        {
            internal string Method = string.Empty;
            internal string ServiceType = string.Empty;
            internal string ServiceEnvironment = string.Empty;
            internal string ServiceMobility = string.Empty;
            public string StringValue = string.Empty;
            public string MapCOSEnumStringValue;
            public string EIDOLocationType = string.Empty;

            public ADRClassOfService(string pidfloMethod, string serviceType, string serviceEnvironment, string serviceMobility, string stringValue, string ImapStringValue, string eidoLocationType)
            {
                Method = pidfloMethod;
                ServiceType = serviceType;
                ServiceEnvironment = serviceEnvironment;
                ServiceMobility = serviceMobility;
                StringValue = stringValue;
                MapCOSEnumStringValue = ImapStringValue;
                EIDOLocationType = eidoLocationType;
            }

            public ADRClassOfService(string stringValue, string ImapStringValue, string eidoLocationType)
            {
                StringValue = stringValue;
                MapCOSEnumStringValue = ImapStringValue;
                EIDOLocationType = eidoLocationType;
            }


            public override string ToString()
            {
                return $"Method:{this.Method} - Type:{ServiceType} - Env:{ServiceEnvironment} - Mobility:{ServiceMobility} -> Value:{StringValue} / {MapCOSEnumStringValue}";
            }
        }

    }
        
    public class CivicAddress
    {
        /// <summary>
        /// Main contructor, setting source of the Location information
        /// </summary>
        /// <param name="locationEventSource"></param>
        public CivicAddress(BusinessLogic.Enums.LocationSource locationSource)
        {
            LocationSource = locationSource;
        }
        /// <summary>
        /// Constructure - defaulting to UNDEFINED source.
        /// </summary>
        public CivicAddress()
        {
            LocationSource = BusinessLogic.Enums.LocationSource.undefined;
        }

        /// <summary>
        /// tracking where the location information came from for auditing purposes.  
        /// </summary>
        public BusinessLogic.Enums.LocationSource LocationSource { get; set; }

        public string Country { get; set; }
        public string A1 { get; set; }
        public string A2 { get; set; }
        public string A3 { get; set; }
        public string A4 { get; set; }
        public string A5 { get; set; }
        public string A6 { get; set; }
        public string PRM { get; set; }
        public string PRD { get; set; }
        public string RD { get; set; }
        public string STS { get; set; }
        public string POD { get; set; }
        public string POM { get; set; }
        public string RDSEC { get; set; }
        public string RDSUBBR { get; set; }
        public string HNO { get; set; }
        public string HNS { get; set; }
        public string LMK { get; set; }
        public string LOC { get; set; }
        public string FLR { get; set; }
        public string NAM { get; set; }
        public string PC { get; set; }
        public string BLD { get; set; }
        public string UNIT { get; set; }
        public string ROOM { get; set; }
        public string SEAT { get; set; }
        public string PLC { get; set; }
        public string PCN { get; set; }
        public string POBOX { get; set; }
        public string ADDCODE { get; set; }
    }
    
    public class GML
    {
        public string POS { get; set; }

        /// <summary>
        /// Constructor to default the source to UNDEFINED.
        /// </summary>
        public GML()
        {
            LocationSource = BusinessLogic.Enums.LocationSource.undefined;
        }

        /// <summary>
        /// tracking where the location information came from for auditing purposes.  
        /// </summary>
        public BusinessLogic.Enums.LocationSource LocationSource { get; set; }
    }

    public class Ellipse : GML
    {
        public string SemiMajorAxis { get; set; }
        public string SemiMinorAxis { get; set; }
        public string Orientation { get; set; }
    }
    public class Ellipsoid : Ellipse
    {
        public string VerticalAxis { get; set; }
    }
    
    public class ArcBand : GML
    {
        public string InnerRadius { get; set; }
        public string OuterRadius { get; set; }
        public string StartAngle { get; set; }
        public string OpeningAngle { get; set; }
    }

    public class Circle : GML
    {
        public string Radius { get; set; }
    }
    public class Sphere : Circle
    {
    }

    public class Point : GML
    {
        //inherits key properities from GML   
    }

    public class Polygon : GML
    {
        /// <summary>
        /// Contains the collection of POS points that define the polyon dimensions
        /// </summary>
        /// <remarks>collapsed node from Prism::/exterior/linearRing/posList/pos* and Polyon/exterior/linearRing/pos*</remarks>
        public List<string> ExteriorPointCollection { get; set; }

        public Polygon()
        {
            ExteriorPointCollection = new List<string>();
        }
    }
    public class Prism : Polygon
    {
        public string Height { get; set; }
    }
}
