﻿using System;
using System.Security.Cryptography;
using Microsoft.AspNetCore.Identity;

namespace Solacom.InfoHub.EventReciever.Security
{
    public class UserKeyManager : IUserKeyManager
    {
        private int _length = 24;
        public UserKeyData Generate()
        {
            var result = new UserKeyData();
            result.UserId = Guid.NewGuid().ToString("N");
            using (RNGCryptoServiceProvider cryptRNG = new RNGCryptoServiceProvider())
            {
                byte[] tokenBuffer = new byte[_length];
                cryptRNG.GetBytes(tokenBuffer);
                var password = Convert.ToBase64String(tokenBuffer);

                result.RawApiKey = password;
            }

            var hasher = new PasswordHasher<string>();

            result.HashedApiKey = hasher.HashPassword(result.UserId, result.RawApiKey);

            return result;
        }

        public bool Match(UserKeyData data)
        {
            var hasher = new PasswordHasher<string>();
            var result = hasher.VerifyHashedPassword(data.UserId, data.HashedApiKey, data.RawApiKey);

            return result == PasswordVerificationResult.Success;
        }
    }
}
