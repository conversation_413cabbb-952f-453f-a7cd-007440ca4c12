# Creates an Elastic Container Repository called infohub/api 
resource "aws_ecr_repository" "infohub_ecr_repository" {
  name = "infohub/api"

  tags  = {
    Environment = "${terraform.workspace}"
    Name        = "CollectorAPI-${terraform.workspace}"
  }
}


output "ecr_repository_name" {
  description = "Elastic Container Registry repository name"
  value       = "${aws_ecr_repository.infohub_ecr_repository.name}"
}


output "ecr_repository_url" {
  description = "Elastic Container Registry URL"
  value = "${aws_ecr_repository.infohub_ecr_repository.repository_url}"
}
