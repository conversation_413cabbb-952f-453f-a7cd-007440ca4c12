﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.29102.190
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Solacom.InfoHub.EventReceiver.Web.API", "Solacom.InfoHub.EventReceiver.Web.API\Solacom.InfoHub.EventReceiver.Web.API.csproj", "{E61493CF-51DE-418A-B8C4-FFCB41FB6709}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Solacom.InfoHub.EventReceiver.Web.Dtos", "Solacom.InfoHub.EventReceiver.Web.Dtos\Solacom.InfoHub.EventReceiver.Web.Dtos.csproj", "{99CFCB26-C9CE-46AC-87A5-BE341E76F3F1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Solacom.InfoHub.EventReciever.UnitTests", "Solacom.InfoHub.EventReciever.UnitTests\Solacom.InfoHub.EventReciever.UnitTests.csproj", "{6BAFE034-1AEF-4296-973E-E04B72AF4FB6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Solacom.InfoHub.EventReceiver.Exceptions", "Solacom.InfoHub.EventReceiver.Exceptions\Solacom.InfoHub.EventReceiver.Exceptions.csproj", "{D51884E7-3BFF-4F3E-9A4C-A0FF774518B1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Solacom.InfoHub.EventReceiver.Entities", "Solacom.InfoHub.EventReceiver.Entities\Solacom.InfoHub.EventReceiver.Entities.csproj", "{05D8B84D-D918-4ACC-9A36-41FA695E54A7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Solacom.InfoHub.EventReceiver.AppService", "Solacom.InfoHub.EventReceiver.AppService\Solacom.InfoHub.EventReceiver.AppService.csproj", "{0D2068DF-1423-4A43-AC0D-27477DC0FFF7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Solacom.InfoHub.EventReceiver.BusinessLogic", "Solacom.InfoHub.EventReceiver.BusinessLogic\Solacom.InfoHub.EventReceiver.BusinessLogic.csproj", "{1FB4434C-4DEE-4892-AD77-E7339D686CF4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Solacom.InfoHub.EventReceiver.IntegrationTests", "Solacom.InfoHub.EventReceiver.IntegrationTests\Solacom.InfoHub.EventReceiver.IntegrationTests.csproj", "{9549260A-AD81-4AC9-B617-4D13D90375B6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Solacom.InfoHub.EventReceiver.ElasticSearch.Entities", "Solacom.InfoHub.EventReceiver.ElasticSearch.Entities\Solacom.InfoHub.EventReceiver.ElasticSearch.Entities.csproj", "{3EF9ABD5-2E6F-45BD-BE03-B2FAD3510AC8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Solacom.InfoHub.EventReceiver.MariaDb.Context", "Solacom.InfoHub.EventReceiver.MariaDb.Context\Solacom.InfoHub.EventReceiver.MariaDb.Context.csproj", "{D1DFA21B-56A3-4A78-B7EE-A6A9360FC6DF}"
EndProject
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{943BC821-23D6-4335-BA46-FAC85DDD57B3}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E61493CF-51DE-418A-B8C4-FFCB41FB6709}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E61493CF-51DE-418A-B8C4-FFCB41FB6709}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E61493CF-51DE-418A-B8C4-FFCB41FB6709}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E61493CF-51DE-418A-B8C4-FFCB41FB6709}.Release|Any CPU.Build.0 = Release|Any CPU
		{99CFCB26-C9CE-46AC-87A5-BE341E76F3F1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{99CFCB26-C9CE-46AC-87A5-BE341E76F3F1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{99CFCB26-C9CE-46AC-87A5-BE341E76F3F1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{99CFCB26-C9CE-46AC-87A5-BE341E76F3F1}.Release|Any CPU.Build.0 = Release|Any CPU
		{6BAFE034-1AEF-4296-973E-E04B72AF4FB6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6BAFE034-1AEF-4296-973E-E04B72AF4FB6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6BAFE034-1AEF-4296-973E-E04B72AF4FB6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6BAFE034-1AEF-4296-973E-E04B72AF4FB6}.Release|Any CPU.Build.0 = Release|Any CPU
		{D51884E7-3BFF-4F3E-9A4C-A0FF774518B1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D51884E7-3BFF-4F3E-9A4C-A0FF774518B1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D51884E7-3BFF-4F3E-9A4C-A0FF774518B1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D51884E7-3BFF-4F3E-9A4C-A0FF774518B1}.Release|Any CPU.Build.0 = Release|Any CPU
		{05D8B84D-D918-4ACC-9A36-41FA695E54A7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{05D8B84D-D918-4ACC-9A36-41FA695E54A7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{05D8B84D-D918-4ACC-9A36-41FA695E54A7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{05D8B84D-D918-4ACC-9A36-41FA695E54A7}.Release|Any CPU.Build.0 = Release|Any CPU
		{0D2068DF-1423-4A43-AC0D-27477DC0FFF7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0D2068DF-1423-4A43-AC0D-27477DC0FFF7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0D2068DF-1423-4A43-AC0D-27477DC0FFF7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0D2068DF-1423-4A43-AC0D-27477DC0FFF7}.Release|Any CPU.Build.0 = Release|Any CPU
		{1FB4434C-4DEE-4892-AD77-E7339D686CF4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1FB4434C-4DEE-4892-AD77-E7339D686CF4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1FB4434C-4DEE-4892-AD77-E7339D686CF4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1FB4434C-4DEE-4892-AD77-E7339D686CF4}.Release|Any CPU.Build.0 = Release|Any CPU
		{9549260A-AD81-4AC9-B617-4D13D90375B6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9549260A-AD81-4AC9-B617-4D13D90375B6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9549260A-AD81-4AC9-B617-4D13D90375B6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9549260A-AD81-4AC9-B617-4D13D90375B6}.Release|Any CPU.Build.0 = Release|Any CPU
		{3EF9ABD5-2E6F-45BD-BE03-B2FAD3510AC8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3EF9ABD5-2E6F-45BD-BE03-B2FAD3510AC8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3EF9ABD5-2E6F-45BD-BE03-B2FAD3510AC8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3EF9ABD5-2E6F-45BD-BE03-B2FAD3510AC8}.Release|Any CPU.Build.0 = Release|Any CPU
		{D1DFA21B-56A3-4A78-B7EE-A6A9360FC6DF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D1DFA21B-56A3-4A78-B7EE-A6A9360FC6DF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D1DFA21B-56A3-4A78-B7EE-A6A9360FC6DF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D1DFA21B-56A3-4A78-B7EE-A6A9360FC6DF}.Release|Any CPU.Build.0 = Release|Any CPU
		{943BC821-23D6-4335-BA46-FAC85DDD57B3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{943BC821-23D6-4335-BA46-FAC85DDD57B3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{943BC821-23D6-4335-BA46-FAC85DDD57B3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{943BC821-23D6-4335-BA46-FAC85DDD57B3}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {4BD42726-5688-4E1A-B612-DB27A2EF2E86}
	EndGlobalSection
EndGlobal
