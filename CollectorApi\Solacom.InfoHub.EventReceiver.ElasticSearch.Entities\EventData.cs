﻿using System;

namespace Solacom.InfoHub.EventReceiver.ElasticSearch.Entities
{
    public class EventData
    {
        public string Content { get; set; }
        public string Hashedcontent { get; set; }
        public DateTime Eventreceived { get; set; }
        public string Clientcode { get; set; }
        public string Message { get; set; }
        public bool? Islastsavesuccessful { get; set; }
        public Solacom.InfoHub.EventReceiver.Entities.EventLog EventLog { get; set; }
        public int DbId { get; set; }
    }
}