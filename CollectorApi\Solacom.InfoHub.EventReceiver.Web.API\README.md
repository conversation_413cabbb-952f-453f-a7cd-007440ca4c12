﻿# CollectorAPI

Guardian Insights business logic application API

## How To Use ##
TODO: Update information here

## How To Deploy ##
- Update the appsettings.Production.json file
- Create a docker image
- Push docker image to AWS Elastic Container Registery (ECR)
- Deploy image onto AWS Elastic BeanStalk (EBS)

See confluence document [Deployment (Manual) of C# Application - Collector Focused](https://solacomtech.atlassian.net/wiki/spaces/IN/pages/2285961228/Deployment+Manual+of+C+Application+-+Collector+Focused)

### Configuration Syntax & Parameters ###
These are the setting that a user could update for the following environments.

#### Solution Environment Variables ####
The following are required to be set based on usage of the solution.  If any of these are blank or not defined properly, the code will still use the local appsettings.json settings (or ALI Mappings)

| **Environment Variable** | **Description** | **Example** | 
| AWS_REGION | Used to define the region  the AWS Secret is pulled from | us-east-1 or us-west-1 ... etc |
| AWS_ENVIRONMENT | Used for the path to the Secret, defines the deployed environment. | dev or prod or stage ... etc |
| AWS_COUNTRY | Used to for the path to the Secret, defines the country | us or cnd ... etc |
| ASPNETCORE_ENVIRONMENT | Used to define the compile environment, which appsettings to leverage on build.  Default to production/should not be updated except for local development | Production or Local ... etc|


#### Cloud - Secret Manager ####
Default appsettings.json

Cloud based deployment works based on the Environment settings (see above).
Hierarchy being:
 + AWS Secret Manager 
 +--- appsetting.<ASPNETCORE_ENVIRONMENT>.json 
 +---+---- appsetting.json

 Majority of settings are defined solely in the appsetting.*.json files.  Where the AWS Secret Manager can host the Client specific (clientSettings node) and ALI mapping information. 

 Please reference online documentation for full details on managing the AWS secret manager.


#### On Premise ####

### Example ###
The following are parameters that requires to be set prior to deployment:

| **Parameter** | **Description** | **Data Type** | **Default Value** | **Encryptable** | **Required** |
| --- | --- | --- | --- | --- | --- |
| clients | Comma delimited clients | string | "" | no | yes |
| clientcodeIndexPrefix | Dictionary of clients ->  <shorten customer code>: "<customer>-<shorten state>" | dictionary | {"": ""} | no | yes |
| clientTenantMapping | Dictionary of dictionaries tenant mappings ->  "<shorten customer code>": "<tenant>-<tenant>" | dictionary of dictionaries | {"": {"": ""}} |no | yes |
| enablesha256Fingerprint | Toggle SSL or 256 Fingerprint encrytion for Elasticsearch | string | "true" |no | yes |
| enableSSL | Toggle SSL for Serilog | string | "false" |no |  yes |
| version | CollectorAPI version | string | "<GIT BRANCH HASH> - <SHORT DESCRIPTION>" |no | yes |
| Instance | Instance Name | string | "" |no |  no |
| sha256Fingerprint | SHA-256 fingerprint to use for Elasticsearch | string | "" | no | no |

appsettings.json snippet

```json
{
  "version": "<GIT BRANCH HASH> - <SHORT DESCRIPTION>",
    "elasticsearchSettings": {
      "serilog": {
        "enableSSL": "false",
      }
    }
  },
  "Serilog": {
    "Properties": {
      "Instance": ""
    }
  },
  "elasticsearchSettings": {
    "clients": "lexus-on",
    "enablesha256Fingerprint": "true",
    "sha256Fingerprint": "94:BF:DA:ED:89:9E:F0:17:EC:4E:33:B7:CC:8F:01:12:9A:BC:69:B0:A5:05:0A:FF:A4:B5:74:3D:CB:BE:A4:BE",
    "clientcodeIndexPrefix": {
      "leon": "lexus-on"
    },
    "clientTenantMapping": {
      "leon": {
        "tenantname": "tenantname"
      }
    }
  }
}
```

NOTE: To manually get the sha256Fingerprint, invoke the following command on the Insights server:
```
docker exec -it solacom-es01-1 openssl x509 -fingerprint -sha256 -in /usr/share/elasticsearch/config/certs/es01/es01.crt
```


| **Parameter** | **Description** | **Data Type** | **Default Value** | **Encryptable** | **Required** |
| --- | --- | --- | --- | --- | --- |
| connectionstring | Connection string to database | string | "" | yes | yes |
| Environment | Enviroment the application is running upon | string | "" | no | yes |
| password | Password to Elasticsearch | string | "" | yes | yes |
| url | URL to the Elasticsearch including schema and port | string | "" | yes | yes |
| userName | User name to Elasticsearch | string | "" | yes | yes |
| version | Version the application is running | string | "" | no | yes |

appsettings.Production.json snippet

```json
{
  "Logging": {
    "elasticsearchSettings": {
      "url": "https://localhost:9200",
      "userName": "elastic",
      "password": "changeme"
    }
  },
  "Serilog": {
    "Properties": {
      "Environment": "On Premise"
    }
  },
  "elasticsearchSettings": {
    "url": "https://localhost:9200",
    "userName": "elastic",
    "password": "elastic"
  },
  "Database": {
    "mysql": {
      "connectionstring": "server=localhost;user=root;password=elastic;database=GuardianInsights"
    }
  },
  "version": "Alpha"

}
```

## Encryption of Secrets
* To Encrypt
    * Leverage the POST API end point with the text as the payload to create the encrypted text.  
    * Returned infromation is the resulting hashed information to copy in the given field.
    > ie. ```curl -X POST /encrypt -d SecretText```
