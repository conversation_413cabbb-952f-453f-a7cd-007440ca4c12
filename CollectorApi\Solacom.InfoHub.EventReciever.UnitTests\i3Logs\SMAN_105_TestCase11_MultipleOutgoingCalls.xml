<LogEvents>
    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:03.435Z</timestamp>
        <agencyOrElement>tng001_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>StartCall</eventType>
        <startCall>
            <header>INVITE sip:0002@**************;user=phone SIP/2.0  Via: SIP/2.0/UDP **************:5060;branch=z9hG4bK6138.7060cd55.0  From: &quot;911-P2&quot; &lt;sip:934564@*************&gt;;tag=3408fc701babed6027ca08a1f637fd5c-5e7f  CSeq: 2 INVITE  Call-ID: B2B.468.6227790.1701291658  Max-Forwards: 70  Content-Length: 235  User-Agent: OpenSIPS (2.4.6 (x86_64/linux))  Content-Type: application/sdp  Supported: em,100rel,timer,replaces,path,resource-priority,sdp-anat  Allow: REGISTER,OPTIONS,INVITE,ACK,CANCEL,BYE,NOTIFY,PRACK,REFER,INFO,SUBSCRIBE,UPDATE  Initial-CallID: 17552421032911202313593@*************  Contact: &lt;sip:**************:5060&gt;  To: sip:0002@**************;user=phone</header>
            <mediaLabel>_ML_TESTCASE_11@tng001</mediaLabel>
            <incomingCallPolicy>13_Primary2</incomingCallPolicy>
            <callType>SR911</callType>
            <signallingType>VOIP</signallingType>
            <circuit>30/01/00/0062</circuit>
            <circuitId>125960254</circuitId>
            <trunkGroupId>300</trunkGroupId>
            <ani>934564</ani>
            <aniDomain>**************</aniDomain>
            <dnis>0002</dnis>
            <dnisDomain>**************</dnisDomain>
            <pani>934564</pani>
            <esrn>.</esrn>
            <callerName>911-P2</callerName>
            <concurrentCalls>1</concurrentCalls>
        </startCall>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:03.435Z</timestamp>
        <agencyOrElement>tng001_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Media</eventType>
        <media>
            <udp>v=0  o=AudiocodesGW 1755227668 1755227667 IN IP4 *************  s=Phone-Call  c=IN IP4 *************  t=0 0  m=audio 6030 RTP/AVP 0 101  a=ptime:20  a=sendrecv  a=rtpmap:0 PCMU/8000  a=rtpmap:101 telephone-event/8000  a=fmtp:101 0-15  </udp>
            <mediaLabel>_ML_TESTCASE_11@tng001</mediaLabel></media>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:03.442Z</timestamp>
        <agencyOrElement>tng001</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Route</eventType>
        <route>
            <uri>4035553000</uri>
            <rule>rule #4</rule>
            <reason>normal</reason>
            <mediaLabel>.</mediaLabel>
            <attempt>1</attempt>
            <priority>0</priority>
            <ani>934564</ani>
            <aniDomain>**************</aniDomain>
            <dnis>4035553000</dnis>
            <pani>934564</pani>
            <esrn>4035553000</esrn>
            <callerName>911-P2</callerName>
            <aniTranslated>934564</aniTranslated>
            <dnisTranslated>4035553000</dnisTranslated>
            <callerNameTranslated>911-P2</callerNameTranslated>
            <circuit>.</circuit>
            <circuitId>.</circuitId>
            <ruleName>911</ruleName>
            <concurrentCalls>-1</concurrentCalls>
            <trunkGroupId>800</trunkGroupId></route>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:03.46Z</timestamp>
        <agencyOrElement>tng001</agencyOrElement>
        <agent>student1</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Route</eventType>
        <route>
            <uri>4035553003</uri>
            <rule>rule #4</rule>
            <reason>normal</reason>
            <mediaLabel>_ML_TESTCASE11_G@tng001</mediaLabel>
            <attempt>1</attempt>
            <priority>0</priority>
            <ani>934564</ani>
            <aniDomain>**************</aniDomain>
            <dnis>4035553003</dnis>
            <pani>934564</pani>
            <esrn>4035553000</esrn>
            <callerName>911-P2</callerName>
            <aniTranslated>934564</aniTranslated>
            <dnisTranslated>4035553003</dnisTranslated>
            <callerNameTranslated>911-P2</callerNameTranslated>
            <circuit>30/01/00/0077</circuit>
            <circuitId>125960269</circuitId>
            <ruleName>911</ruleName>
            <concurrentCalls>-1</concurrentCalls>
            <trunkGroupId>800</trunkGroupId></route>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:03.462Z</timestamp>
        <agencyOrElement>tng001</agencyOrElement>
        <agent>luchiam</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Route</eventType>
        <route>
            <uri>4035553001</uri>
            <rule>rule #4</rule>
            <reason>normal</reason>
            <mediaLabel>_ML_TESTCASE11_F@tng001</mediaLabel>
            <attempt>1</attempt>
            <priority>0</priority>
            <ani>934564</ani>
            <aniDomain>**************</aniDomain>
            <dnis>4035553001</dnis>
            <pani>934564</pani>
            <esrn>4035553000</esrn>
            <callerName>911-P2</callerName>
            <aniTranslated>934564</aniTranslated>
            <dnisTranslated>4035553001</dnisTranslated>
            <callerNameTranslated>911-P2</callerNameTranslated>
            <circuit>30/01/00/0092</circuit>
            <circuitId>125960284</circuitId>
            <ruleName>911</ruleName>
            <concurrentCalls>-1</concurrentCalls>
            <trunkGroupId>800</trunkGroupId></route>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:03.463Z</timestamp>
        <agencyOrElement>tng001</agencyOrElement>
        <agent>aikensc</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Route</eventType>
        <route>
            <uri>4035553002</uri>
            <rule>rule #4</rule>
            <reason>normal</reason>
            <mediaLabel>_ML_TESTCASE11_C@tng001</mediaLabel>
            <attempt>1</attempt>
            <priority>0</priority>
            <ani>934564</ani>
            <aniDomain>**************</aniDomain>
            <dnis>4035553002</dnis>
            <pani>934564</pani>
            <esrn>4035553000</esrn>
            <callerName>911-P2</callerName>
            <aniTranslated>934564</aniTranslated>
            <dnisTranslated>4035553002</dnisTranslated>
            <callerNameTranslated>911-P2</callerNameTranslated>
            <circuit>30/01/00/0093</circuit>
            <circuitId>125960285</circuitId>
            <ruleName>911</ruleName>
            <concurrentCalls>-1</concurrentCalls>
            <trunkGroupId>800</trunkGroupId></route>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:03.464Z</timestamp>
        <agencyOrElement>tng001</agencyOrElement>
        <agent>student1</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Media</eventType>
        <media>
            <udp>v=0  o=AudiocodesGW 1755227668 1755227667 IN IP4 *************  s=Phone-Call  c=IN IP4 *************  t=0 0  m=audio 6030 RTP/AVP 0 101  a=ptime:20  a=sendrecv  a=rtpmap:0 PCMU/8000  a=rtpmap:101 telephone-event/8000  a=fmtp:101 0-15  </udp>
            <mediaLabel>_ML_TESTCASE11_G@tng001</mediaLabel></media>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:03.465Z</timestamp>
        <agencyOrElement>tng001</agencyOrElement>
        <agent>luchiam</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Media</eventType>
        <media>
            <udp>v=0  o=AudiocodesGW 1755227668 1755227667 IN IP4 *************  s=Phone-Call  c=IN IP4 *************  t=0 0  m=audio 6030 RTP/AVP 0 101  a=ptime:20  a=sendrecv  a=rtpmap:0 PCMU/8000  a=rtpmap:101 telephone-event/8000  a=fmtp:101 0-15  </udp>
            <mediaLabel>_ML_TESTCASE11_F@tng001</mediaLabel></media>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:03.465Z</timestamp>
        <agencyOrElement>tng001</agencyOrElement>
        <agent>aikensc</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Media</eventType>
        <media>
            <udp>v=0  o=AudiocodesGW 1755227668 1755227667 IN IP4 *************  s=Phone-Call  c=IN IP4 *************  t=0 0  m=audio 6030 RTP/AVP 0 101  a=ptime:20  a=sendrecv  a=rtpmap:0 PCMU/8000  a=rtpmap:101 telephone-event/8000  a=fmtp:101 0-15  </udp>
            <mediaLabel>_ML_TESTCASE11_C@tng001</mediaLabel></media>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:03.467Z</timestamp>
        <agencyOrElement>tng001_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Answer</eventType>
        <answer>
            <mediaLabel>_ML_TESTCASE_11@tng001</mediaLabel>
            <uri>tel:+934564</uri>
            <agentRole>.</agentRole>
            <tenantGroup>.</tenantGroup>
            <operatorId>-1</operatorId>
            <workstation>.</workstation></answer>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:04.896Z</timestamp>
        <agencyOrElement>tng001</agencyOrElement>
        <agent>aikensc</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Answer</eventType>
        <answer>
            <mediaLabel>_ML_TESTCASE11_C@tng001</mediaLabel>
            <uri>tel:+4035553002</uri>
            <agentRole>Call Takers</agentRole>
            <tenantGroup>tng001</tenantGroup>
            <operatorId>00002</operatorId>
            <workstation>PSAP1-OP2</workstation></answer>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:04.906Z</timestamp>
        <agencyOrElement>tng001</agencyOrElement>
        <agent>student1</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>EndMedia</eventType>
        <endMedia>
            <mediaLabel>_ML_TESTCASE11_G@tng001</mediaLabel>
            <responseCode>18</responseCode>
            <disconnectReason/>
            <voiceQOS>
                <mediaIpSourceAddr>.</mediaIpSourceAddr>
                <mediaIpDestAddr>.</mediaIpDestAddr>
                <mediaUdpRtpSourcePort>-1</mediaUdpRtpSourcePort>
                <mediaUdpRtpDestPort>-1</mediaUdpRtpDestPort>
                <mediaNumOfIpPktRxed>-1</mediaNumOfIpPktRxed>
                <mediaNumOfIpPktTxed>-1</mediaNumOfIpPktTxed>
                <mediaNumOfIpErroredPktRxed>-1</mediaNumOfIpErroredPktRxed>
                <mediaNumOfRtpPktRxed>-1</mediaNumOfRtpPktRxed>
                <mediaNumOfRtpPktTxed>-1</mediaNumOfRtpPktTxed>
                <mediaNumOfRtpPktLost>-1</mediaNumOfRtpPktLost>
                <mediaNumOfRtpPktDiscarded>-1</mediaNumOfRtpPktDiscarded>
                <mediaRtpJitter>-1</mediaRtpJitter>
                <mediaRtpLatency>-1</mediaRtpLatency>
                <mediaNumOfRtcpPktRxed>-1</mediaNumOfRtcpPktRxed>
                <mediaNumOfRtcpPktTxed>-1</mediaNumOfRtcpPktTxed>
                <mediaFarEndPacketLostPercentage>-1</mediaFarEndPacketLostPercentage>
                <mediaFarEndCumulativePacketLost>-1</mediaFarEndCumulativePacketLost>
                <mediaFarEndInterarrivalJitter>-1</mediaFarEndInterarrivalJitter></voiceQOS>
        </endMedia>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:04.907Z</timestamp>
        <agencyOrElement>tng001</agencyOrElement>
        <agent>luchiam</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>EndMedia</eventType>
        <endMedia>
            <mediaLabel>_ML_TESTCASE11_F@tng001</mediaLabel>
            <responseCode>18</responseCode>
            <disconnectReason/>
            <voiceQOS>
                <mediaIpSourceAddr>.</mediaIpSourceAddr>
                <mediaIpDestAddr>.</mediaIpDestAddr>
                <mediaUdpRtpSourcePort>-1</mediaUdpRtpSourcePort>
                <mediaUdpRtpDestPort>-1</mediaUdpRtpDestPort>
                <mediaNumOfIpPktRxed>-1</mediaNumOfIpPktRxed>
                <mediaNumOfIpPktTxed>-1</mediaNumOfIpPktTxed>
                <mediaNumOfIpErroredPktRxed>-1</mediaNumOfIpErroredPktRxed>
                <mediaNumOfRtpPktRxed>-1</mediaNumOfRtpPktRxed>
                <mediaNumOfRtpPktTxed>-1</mediaNumOfRtpPktTxed>
                <mediaNumOfRtpPktLost>-1</mediaNumOfRtpPktLost>
                <mediaNumOfRtpPktDiscarded>-1</mediaNumOfRtpPktDiscarded>
                <mediaRtpJitter>-1</mediaRtpJitter>
                <mediaRtpLatency>-1</mediaRtpLatency>
                <mediaNumOfRtcpPktRxed>-1</mediaNumOfRtcpPktRxed>
                <mediaNumOfRtcpPktTxed>-1</mediaNumOfRtcpPktTxed>
                <mediaFarEndPacketLostPercentage>-1</mediaFarEndPacketLostPercentage>
                <mediaFarEndCumulativePacketLost>-1</mediaFarEndCumulativePacketLost>
                <mediaFarEndInterarrivalJitter>-1</mediaFarEndInterarrivalJitter></voiceQOS>
        </endMedia>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:04.91Z</timestamp>
        <agencyOrElement>tng001_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Route</eventType>
        <route>
            <uri>00002@*************</uri>
            <rule>.</rule>
            <reason>recording</reason>
            <mediaLabel>_ML_TESTCASE11_E@tng001</mediaLabel>
            <attempt>1</attempt>
            <priority>0</priority>
            <ani>934564</ani>
            <aniDomain>**************</aniDomain>
            <dnis>00002@*************</dnis>
            <pani>.</pani>
            <esrn>.</esrn>
            <callerName>911</callerName>
            <aniTranslated>.</aniTranslated>
            <dnisTranslated>.</dnisTranslated>
            <callerNameTranslated>.</callerNameTranslated>
            <circuit>00/00/03/3148</circuit>
            <circuitId>2147499084</circuitId>
            <ruleName>.</ruleName>
            <concurrentCalls>-1</concurrentCalls>
            <trunkGroupId>.</trunkGroupId></route>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:04.91Z</timestamp>
        <agencyOrElement>tng001_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Media</eventType>
        <media>
            <udp>.</udp>
            <mediaLabel>_ML_TESTCASE11_E@tng001</mediaLabel></media>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:04.997Z</timestamp>
        <agencyOrElement>tng001_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Answer</eventType>
        <answer>
            <mediaLabel>_ML_TESTCASE11_E@tng001</mediaLabel>
            <uri>00002@*************</uri>
            <agentRole>.</agentRole>
            <tenantGroup>.</tenantGroup>
            <operatorId>-1</operatorId>
            <workstation>.</workstation></answer>
    </LogEvent>
    
    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:33.903Z</timestamp>
        <agencyOrElement>tng001_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>EndCall</eventType>
        <endCall>
            <responseCode>16</responseCode>
            <callReplaced>No</callReplaced></endCall>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:33.904Z</timestamp>
        <agencyOrElement>tng001</agencyOrElement>
        <agent>aikensc</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>EndMedia</eventType>
        <endMedia>
            <mediaLabel>_ML_TESTCASE11_C@tng001</mediaLabel>
            <responseCode>16</responseCode>
            <disconnectReason/>
            <voiceQOS>
                <mediaIpSourceAddr>.</mediaIpSourceAddr>
                <mediaIpDestAddr>.</mediaIpDestAddr>
                <mediaUdpRtpSourcePort>-1</mediaUdpRtpSourcePort>
                <mediaUdpRtpDestPort>-1</mediaUdpRtpDestPort>
                <mediaNumOfIpPktRxed>-1</mediaNumOfIpPktRxed>
                <mediaNumOfIpPktTxed>-1</mediaNumOfIpPktTxed>
                <mediaNumOfIpErroredPktRxed>-1</mediaNumOfIpErroredPktRxed>
                <mediaNumOfRtpPktRxed>-1</mediaNumOfRtpPktRxed>
                <mediaNumOfRtpPktTxed>-1</mediaNumOfRtpPktTxed>
                <mediaNumOfRtpPktLost>-1</mediaNumOfRtpPktLost>
                <mediaNumOfRtpPktDiscarded>-1</mediaNumOfRtpPktDiscarded>
                <mediaRtpJitter>-1</mediaRtpJitter>
                <mediaRtpLatency>-1</mediaRtpLatency>
                <mediaNumOfRtcpPktRxed>-1</mediaNumOfRtcpPktRxed>
                <mediaNumOfRtcpPktTxed>-1</mediaNumOfRtcpPktTxed>
                <mediaFarEndPacketLostPercentage>-1</mediaFarEndPacketLostPercentage>
                <mediaFarEndCumulativePacketLost>-1</mediaFarEndCumulativePacketLost>
                <mediaFarEndInterarrivalJitter>-1</mediaFarEndInterarrivalJitter></voiceQOS>
        </endMedia>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:33.988Z</timestamp>
        <agencyOrElement>tng001_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>EndMedia</eventType>
        <endMedia>
            <mediaLabel>_ML_TESTCASE11_E@tng001</mediaLabel>
            <responseCode>16</responseCode>
            <disconnectReason/>
            <voiceQOS>
                <mediaIpSourceAddr>*************</mediaIpSourceAddr>
                <mediaIpDestAddr>*************</mediaIpDestAddr>
                <mediaUdpRtpSourcePort>21252</mediaUdpRtpSourcePort>
                <mediaUdpRtpDestPort>10002</mediaUdpRtpDestPort>
                <mediaNumOfIpPktRxed>65535</mediaNumOfIpPktRxed>
                <mediaNumOfIpPktTxed>65535</mediaNumOfIpPktTxed>
                <mediaNumOfIpErroredPktRxed>65535</mediaNumOfIpErroredPktRxed>
                <mediaNumOfRtpPktRxed>0</mediaNumOfRtpPktRxed>
                <mediaNumOfRtpPktTxed>1447</mediaNumOfRtpPktTxed>
                <mediaNumOfRtpPktLost>0</mediaNumOfRtpPktLost>
                <mediaNumOfRtpPktDiscarded>0</mediaNumOfRtpPktDiscarded>
                <mediaRtpJitter>0</mediaRtpJitter>
                <mediaRtpLatency>0</mediaRtpLatency>
                <mediaNumOfRtcpPktRxed>0</mediaNumOfRtcpPktRxed>
                <mediaNumOfRtcpPktTxed>10</mediaNumOfRtcpPktTxed>
                <mediaFarEndPacketLostPercentage>0</mediaFarEndPacketLostPercentage>
                <mediaFarEndCumulativePacketLost>0</mediaFarEndCumulativePacketLost>
                <mediaFarEndInterarrivalJitter>0</mediaFarEndInterarrivalJitter></voiceQOS>
        </endMedia>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:33.991Z</timestamp>
        <agencyOrElement>tng001_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>EndMedia</eventType>
        <endMedia>
            <mediaLabel>_ML_TESTCASE_11@tng001</mediaLabel>
            <responseCode>16</responseCode>
            <disconnectReason/>
            <voiceQOS>
                <mediaIpSourceAddr>*************</mediaIpSourceAddr>
                <mediaIpDestAddr>*************</mediaIpDestAddr>
                <mediaUdpRtpSourcePort>21248</mediaUdpRtpSourcePort>
                <mediaUdpRtpDestPort>6030</mediaUdpRtpDestPort>
                <mediaNumOfIpPktRxed>65535</mediaNumOfIpPktRxed>
                <mediaNumOfIpPktTxed>65535</mediaNumOfIpPktTxed>
                <mediaNumOfIpErroredPktRxed>65535</mediaNumOfIpErroredPktRxed>
                <mediaNumOfRtpPktRxed>1516</mediaNumOfRtpPktRxed>
                <mediaNumOfRtpPktTxed>1522</mediaNumOfRtpPktTxed>
                <mediaNumOfRtpPktLost>0</mediaNumOfRtpPktLost>
                <mediaNumOfRtpPktDiscarded>0</mediaNumOfRtpPktDiscarded>
                <mediaRtpJitter>6</mediaRtpJitter>
                <mediaRtpLatency>3</mediaRtpLatency>
                <mediaNumOfRtcpPktRxed>7</mediaNumOfRtcpPktRxed>
                <mediaNumOfRtcpPktTxed>12</mediaNumOfRtcpPktTxed>
                <mediaFarEndPacketLostPercentage>0</mediaFarEndPacketLostPercentage>
                <mediaFarEndCumulativePacketLost>0</mediaFarEndCumulativePacketLost>
                <mediaFarEndInterarrivalJitter>0</mediaFarEndInterarrivalJitter></voiceQOS>
        </endMedia>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:33.992Z</timestamp>
        <agencyOrElement>tng001_A</agencyOrElement>
        <agent>aikensc</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>CDRtype1</eventType>
        <cdrType1>
            <startTime>2023-11-29T20:59:03.435Z</startTime>
            <operatorId>00002</operatorId>
            <ani>934564</ani>
            <presentedTime>2023-11-29T20:59:03.464Z</presentedTime>
            <answeredTime>2023-11-29T20:59:04.896Z</answeredTime>
            <jobNumber>.</jobNumber>
            <transferTime/>
            <transferAnswerTime/>
            <disassociatedTime/>
            <transferTargetType>.</transferTargetType>
            <transferTargetName>.</transferTargetName>
            <transferTarget>.</transferTarget>
            <disconnectReason>.</disconnectReason>
            <ivrOutcome>.</ivrOutcome>
            <externalTransferAttempts>0</externalTransferAttempts>
            <dnis>0002</dnis>
            <endTime>2023-11-29T20:59:33.992Z</endTime>
            <routedRingGroupTime>2023-11-29T20:59:03.443Z</routedRingGroupTime></cdrType1>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:38.744Z</timestamp>
        <agencyOrElement>tng001</agencyOrElement>
        <agent>aikensc</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>OutboundCall</eventType>
        <outboundCall>
            <outboundTarget>4033361663</outboundTarget>
            <rule>rule #3</rule>
            <reason>normal</reason>
            <mediaLabel>_ML_TESTCASE11_D@tng001</mediaLabel>
            <attempt>1</attempt>
            <priority>-1</priority>
            <ani>4035553002</ani>
            <aniDomain>192.168.28.132</aniDomain>
            <dnis>4033361663</dnis>
            <pani>.</pani>
            <callerName>PSAP1-OP2</callerName>
            <aniTranslated>4035553002</aniTranslated>
            <dnisTranslated>4033361663</dnisTranslated>
            <callerNameTranslated>PSAP1-OP2</callerNameTranslated>
            <method>Callback Button</method>
            <targetType>Callback</targetType>
            <targetName>4033361663</targetName></outboundCall>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:38.745Z</timestamp>
        <agencyOrElement>tng001</agencyOrElement>
        <agent>aikensc</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Media</eventType>
        <media>
            <udp>.</udp>
            <mediaLabel>_ML_TESTCASE11_D@tng001</mediaLabel></media>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:38.748Z</timestamp>
        <agencyOrElement>tng001</agencyOrElement>
        <agent>aikensc</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Answer</eventType>
        <answer>
            <mediaLabel>_ML_TESTCASE11_D@tng001</mediaLabel>
            <uri>tel:+4035553002</uri>
            <agentRole>Call Takers</agentRole>
            <tenantGroup>tng001</tenantGroup>
            <operatorId>00002</operatorId>
            <workstation>PSAP1-OP2</workstation></answer>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:38.76Z</timestamp>
        <agencyOrElement>tng001_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Route</eventType>
        <route>
            <uri>00002@*************</uri>
            <rule>.</rule>
            <reason>recording</reason>
            <mediaLabel>_ML_TESTCASE11_A@tng001</mediaLabel>
            <attempt>1</attempt>
            <priority>0</priority>
            <ani>4033361663</ani>
            <aniDomain>192.168.28.132</aniDomain>
            <dnis>00002@*************</dnis>
            <pani>.</pani>
            <esrn>.</esrn>
            <callerName>PSAP1-OP2</callerName>
            <aniTranslated>.</aniTranslated>
            <dnisTranslated>.</dnisTranslated>
            <callerNameTranslated>.</callerNameTranslated>
            <circuit>00/00/03/3149</circuit>
            <circuitId>2147499085</circuitId>
            <ruleName>.</ruleName>
            <concurrentCalls>-1</concurrentCalls>
            <trunkGroupId>.</trunkGroupId></route>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:38.761Z</timestamp>
        <agencyOrElement>tng001_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Media</eventType>
        <media>
            <udp>.</udp>
            <mediaLabel>_ML_TESTCASE11_A@tng001</mediaLabel></media>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:38.776Z</timestamp>
        <agencyOrElement>tng001_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Route</eventType>
        <route>
            <uri>74033361663@************</uri>
            <rule>rule #31</rule>
            <reason>normal</reason>
            <mediaLabel>_ML_TESTCASE_11@tng001</mediaLabel>
            <attempt>1</attempt>
            <priority>0</priority>
            <ani>4035553002</ani>
            <aniDomain>192.168.28.132</aniDomain>
            <dnis>74033361663</dnis>
            <pani>.</pani>
            <esrn>.</esrn>
            <callerName>PSAP1-OP2</callerName>
            <aniTranslated>4035553002</aniTranslated>
            <dnisTranslated>74033361663@************</dnisTranslated>
            <callerNameTranslated>PSAP1-OP2</callerNameTranslated>
            <circuit>30/01/00/0065</circuit>
            <circuitId>125960257</circuitId>
            <ruleName>BlockedIDCatch_all</ruleName>
            <concurrentCalls>1</concurrentCalls>
            <trunkGroupId>800</trunkGroupId></route>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:38.777Z</timestamp>
        <agencyOrElement>tng001_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Media</eventType>
        <media>
            <udp>.</udp>
            <mediaLabel>_ML_TESTCASE_11@tng001</mediaLabel></media>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:38.857Z</timestamp>
        <agencyOrElement>tng001_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Answer</eventType>
        <answer>
            <mediaLabel>_ML_TESTCASE11_A@tng001</mediaLabel>
            <uri>00002@*************</uri>
            <agentRole>.</agentRole>
            <tenantGroup>.</tenantGroup>
            <operatorId>-1</operatorId>
            <workstation>.</workstation></answer>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T20:59:44.338Z</timestamp>
        <agencyOrElement>tng001_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>Answer</eventType>
        <answer>
            <mediaLabel>_ML_TESTCASE_11@tng001</mediaLabel>
            <uri>tel:+74033361663</uri>
            <agentRole>.</agentRole>
            <tenantGroup>.</tenantGroup>
            <operatorId>-1</operatorId>
            <workstation>.</workstation></answer>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T21:00:34.159Z</timestamp>
        <agencyOrElement>tng001</agencyOrElement>
        <agent>aikensc</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>EndMedia</eventType>
        <endMedia>
            <mediaLabel>_ML_TESTCASE11_D@tng001</mediaLabel>
            <responseCode>16</responseCode>
            <disconnectReason/>
            <voiceQOS>
                <mediaIpSourceAddr>.</mediaIpSourceAddr>
                <mediaIpDestAddr>.</mediaIpDestAddr>
                <mediaUdpRtpSourcePort>-1</mediaUdpRtpSourcePort>
                <mediaUdpRtpDestPort>-1</mediaUdpRtpDestPort>
                <mediaNumOfIpPktRxed>-1</mediaNumOfIpPktRxed>
                <mediaNumOfIpPktTxed>-1</mediaNumOfIpPktTxed>
                <mediaNumOfIpErroredPktRxed>-1</mediaNumOfIpErroredPktRxed>
                <mediaNumOfRtpPktRxed>-1</mediaNumOfRtpPktRxed>
                <mediaNumOfRtpPktTxed>-1</mediaNumOfRtpPktTxed>
                <mediaNumOfRtpPktLost>-1</mediaNumOfRtpPktLost>
                <mediaNumOfRtpPktDiscarded>-1</mediaNumOfRtpPktDiscarded>
                <mediaRtpJitter>-1</mediaRtpJitter>
                <mediaRtpLatency>-1</mediaRtpLatency>
                <mediaNumOfRtcpPktRxed>-1</mediaNumOfRtcpPktRxed>
                <mediaNumOfRtcpPktTxed>-1</mediaNumOfRtcpPktTxed>
                <mediaFarEndPacketLostPercentage>-1</mediaFarEndPacketLostPercentage>
                <mediaFarEndCumulativePacketLost>-1</mediaFarEndCumulativePacketLost>
                <mediaFarEndInterarrivalJitter>-1</mediaFarEndInterarrivalJitter></voiceQOS>
        </endMedia>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T21:00:34.247Z</timestamp>
        <agencyOrElement>tng001_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>EndMedia</eventType>
        <endMedia>
            <mediaLabel>_ML_TESTCASE11_A@tng001</mediaLabel>
            <responseCode>16</responseCode>
            <disconnectReason/>
            <voiceQOS>
                <mediaIpSourceAddr>*************</mediaIpSourceAddr>
                <mediaIpDestAddr>*************</mediaIpDestAddr>
                <mediaUdpRtpSourcePort>21256</mediaUdpRtpSourcePort>
                <mediaUdpRtpDestPort>10002</mediaUdpRtpDestPort>
                <mediaNumOfIpPktRxed>65535</mediaNumOfIpPktRxed>
                <mediaNumOfIpPktTxed>65535</mediaNumOfIpPktTxed>
                <mediaNumOfIpErroredPktRxed>65535</mediaNumOfIpErroredPktRxed>
                <mediaNumOfRtpPktRxed>0</mediaNumOfRtpPktRxed>
                <mediaNumOfRtpPktTxed>2767</mediaNumOfRtpPktTxed>
                <mediaNumOfRtpPktLost>0</mediaNumOfRtpPktLost>
                <mediaNumOfRtpPktDiscarded>0</mediaNumOfRtpPktDiscarded>
                <mediaRtpJitter>0</mediaRtpJitter>
                <mediaRtpLatency>0</mediaRtpLatency>
                <mediaNumOfRtcpPktRxed>0</mediaNumOfRtcpPktRxed>
                <mediaNumOfRtcpPktTxed>22</mediaNumOfRtcpPktTxed>
                <mediaFarEndPacketLostPercentage>0</mediaFarEndPacketLostPercentage>
                <mediaFarEndCumulativePacketLost>3</mediaFarEndCumulativePacketLost>
                <mediaFarEndInterarrivalJitter>0</mediaFarEndInterarrivalJitter></voiceQOS>
        </endMedia>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T21:00:34.252Z</timestamp>
        <agencyOrElement>tng001_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>EndCall</eventType>
        <endCall>
            <responseCode>16</responseCode>
            <callReplaced>No</callReplaced></endCall>
    </LogEvent>

    <LogEvent xmlns="http://solacom.com/Logging">
        <logNumber>0</logNumber>
        <timestamp>2023-11-29T21:00:34.255Z</timestamp>
        <agencyOrElement>tng001_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>_CI_TESTCASE_11@tng001</callIdentifier>
        <incidentIdentifier>_II_TESTCASE_11@tng001</incidentIdentifier>
        <eventType>EndMedia</eventType>
        <endMedia>
            <mediaLabel>_ML_TESTCASE_11@tng001</mediaLabel>
            <responseCode>16</responseCode>
            <disconnectReason/>
            <voiceQOS>
                <mediaIpSourceAddr>*************</mediaIpSourceAddr>
                <mediaIpDestAddr>************</mediaIpDestAddr>
                <mediaUdpRtpSourcePort>21260</mediaUdpRtpSourcePort>
                <mediaUdpRtpDestPort>10004</mediaUdpRtpDestPort>
                <mediaNumOfIpPktRxed>65535</mediaNumOfIpPktRxed>
                <mediaNumOfIpPktTxed>65535</mediaNumOfIpPktTxed>
                <mediaNumOfIpErroredPktRxed>65535</mediaNumOfIpErroredPktRxed>
                <mediaNumOfRtpPktRxed>2640</mediaNumOfRtpPktRxed>
                <mediaNumOfRtpPktTxed>2640</mediaNumOfRtpPktTxed>
                <mediaNumOfRtpPktLost>0</mediaNumOfRtpPktLost>
                <mediaNumOfRtpPktDiscarded>0</mediaNumOfRtpPktDiscarded>
                <mediaRtpJitter>3</mediaRtpJitter>
                <mediaRtpLatency>3</mediaRtpLatency>
                <mediaNumOfRtcpPktRxed>11</mediaNumOfRtcpPktRxed>
                <mediaNumOfRtcpPktTxed>20</mediaNumOfRtcpPktTxed>
                <mediaFarEndPacketLostPercentage>0</mediaFarEndPacketLostPercentage>
                <mediaFarEndCumulativePacketLost>9</mediaFarEndCumulativePacketLost>
                <mediaFarEndInterarrivalJitter>0</mediaFarEndInterarrivalJitter></voiceQOS>
        </endMedia>
    </LogEvent>
</LogEvents>
