﻿using System;
using Nest;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Configuration;
using Serilog;
using System.Threading;

namespace HistoricalReIndexer
{
    class Program
    {
        public static bool _DeleteProcessed = true;
        public static bool _ValidatProcessed = true;

        /// <summary>
        /// Main function to define the INDEX names based on the Type passed in.  
        /// </summary>
        /// <param name="indexPostfix">The appended postfix - i.e. butler-oh</param>
        /// <returns></returns>
        /// <remarks>This is a minified version of the mirror call in the main code base</remarks>
        protected static string GetIndexName(string indexPostfix)
        {
            string _CallSummaryIndex = "callsummary";
            
            return $"{_CallSummaryIndex}_{indexPostfix}";
        }

        /// <summary>
        /// Calculates the Answered*Than fields
        /// </summary>
        /// <param name="callsummary"></param>
        /// <returns></returns>
        private static CallSummary SetCallToAnswer(CallSummary callsummary)
        {
            //In case check to confirm the Call summary record is defined.
            if (callsummary == null)
            {
                return callsummary;
            }
            if (callsummary.TimeToAnswerInSeconds < 10)
            {
                callsummary.AnsweredLessThan10s =
                    callsummary.AnsweredLessThan15s = callsummary.AnsweredLessThan20s = callsummary.AnsweredLessThan40s = 1;
                callsummary.AnsweredMoreThan10s = callsummary.AnsweredMoreThan20s = callsummary.AnsweredMoreThan40s = 0;
            }
            else if (callsummary.TimeToAnswerInSeconds >= 10 && callsummary.TimeToAnswerInSeconds < 15)
            {
                callsummary.AnsweredLessThan15s = callsummary.AnsweredLessThan20s = callsummary.AnsweredLessThan40s = callsummary.AnsweredMoreThan10s = 1;
                callsummary.AnsweredLessThan10s = callsummary.AnsweredMoreThan40s = callsummary.AnsweredMoreThan20s = 0;
            }
            else if (callsummary.TimeToAnswerInSeconds >= 15 && callsummary.TimeToAnswerInSeconds < 20)
            {
                callsummary.AnsweredLessThan40s = callsummary.AnsweredLessThan20s = callsummary.AnsweredMoreThan10s = 1;
                callsummary.AnsweredLessThan10s = callsummary.AnsweredLessThan15s = callsummary.AnsweredMoreThan20s = callsummary.AnsweredMoreThan40s = 0;
            }
            else if (callsummary.TimeToAnswerInSeconds >= 20 && callsummary.TimeToAnswerInSeconds < 40)
            {
                callsummary.AnsweredLessThan40s = callsummary.AnsweredMoreThan10s = callsummary.AnsweredMoreThan20s = 1;
                callsummary.AnsweredLessThan10s = callsummary.AnsweredLessThan15s = callsummary.AnsweredLessThan20s = callsummary.AnsweredMoreThan40s = 0;
            }
            else
            {
                callsummary.AnsweredMoreThan40s = callsummary.AnsweredMoreThan10s = callsummary.AnsweredMoreThan20s = 1;
                callsummary.AnsweredLessThan10s =
                    callsummary.AnsweredLessThan15s = callsummary.AnsweredLessThan20s = callsummary.AnsweredLessThan40s = 0;
            }
            return callsummary;
        }
        /// <summary>
        /// Calculates the Non-emergency Answered*Than fields
        /// </summary>
        /// <param name="callsummary"></param>
        /// <returns></returns>
        private static CallSummary SetNonEmergencyCallToAnswer(CallSummary callsummary)
        {
            //In case check to confirm the Call summary record is defined.
            if (callsummary == null)
            {
                return callsummary;
            }
            if (callsummary.NonEmergencyTimeToAnswerInSeconds < 10)
            {
                callsummary.NonEmergencyAnsweredLessThan10s =
                    callsummary.NonEmergencyAnsweredLessThan15s = callsummary.NonEmergencyAnsweredLessThan20s = callsummary.NonEmergencyAnsweredLessThan40s = 1;
                callsummary.NonEmergencyAnsweredMoreThan10s = callsummary.NonEmergencyAnsweredMoreThan20s = callsummary.NonEmergencyAnsweredMoreThan40s = 0;
            }
            else if (callsummary.NonEmergencyTimeToAnswerInSeconds >= 10 && callsummary.NonEmergencyTimeToAnswerInSeconds < 15)
            {
                callsummary.NonEmergencyAnsweredLessThan15s = callsummary.NonEmergencyAnsweredLessThan20s = callsummary.NonEmergencyAnsweredLessThan40s = callsummary.NonEmergencyAnsweredMoreThan10s = 1;
                callsummary.NonEmergencyAnsweredLessThan10s = callsummary.NonEmergencyAnsweredMoreThan40s = callsummary.NonEmergencyAnsweredMoreThan20s = 0;
            }
            else if (callsummary.NonEmergencyTimeToAnswerInSeconds >= 15 && callsummary.NonEmergencyTimeToAnswerInSeconds < 20)
            {
                callsummary.NonEmergencyAnsweredLessThan40s = callsummary.NonEmergencyAnsweredLessThan20s = callsummary.NonEmergencyAnsweredMoreThan10s = 1;
                callsummary.NonEmergencyAnsweredLessThan10s = callsummary.NonEmergencyAnsweredLessThan15s = callsummary.NonEmergencyAnsweredMoreThan20s = callsummary.NonEmergencyAnsweredMoreThan40s = 0;
            }
            else if (callsummary.NonEmergencyTimeToAnswerInSeconds >= 20 && callsummary.NonEmergencyTimeToAnswerInSeconds < 40)
            {
                callsummary.NonEmergencyAnsweredLessThan40s = callsummary.NonEmergencyAnsweredMoreThan10s = callsummary.NonEmergencyAnsweredMoreThan20s = 1;
                callsummary.NonEmergencyAnsweredLessThan10s = callsummary.NonEmergencyAnsweredLessThan15s = callsummary.NonEmergencyAnsweredLessThan20s = callsummary.NonEmergencyAnsweredMoreThan40s = 0;
            }
            else
            {
                callsummary.NonEmergencyAnsweredMoreThan40s = callsummary.NonEmergencyAnsweredMoreThan10s = callsummary.NonEmergencyAnsweredMoreThan20s = 1;
                callsummary.NonEmergencyAnsweredLessThan10s =
                    callsummary.NonEmergencyAnsweredLessThan15s = callsummary.NonEmergencyAnsweredLessThan20s = callsummary.NonEmergencyAnsweredLessThan40s = 0;
            }
            return callsummary;
        }

        static void Main(string[] args)
        {
            Console.Write($"Starting HistoricalReIndexer...");
            
            #region Logging configuration
            Log.Logger = new LoggerConfiguration()
                        .MinimumLevel.Debug()
                        .WriteTo.Logger(l => l.Filter.ByIncludingOnly(e => e.Level == Serilog.Events.LogEventLevel.Error).WriteTo.File($"logs\\logfile_ERR_{DateTime.Now.ToShortDateString()}.txt"))
                        .WriteTo.File($"logs\\logfile_{DateTime.Now.ToShortDateString()}.txt")
                        .CreateLogger();
            //Configuration writes all logs to the main, and only ERROR to the secondary //ref: https://stackoverflow.com/questions/28292601/serilog-multiple-log-files

            Log.Information("Starting HistoricalReIndex...");
            #endregion

            #region Start up routine - getting configuration setup
            var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            var builder = new ConfigurationBuilder()
                .AddJsonFile($"appsettings.json", true, true)
                .AddJsonFile($"appsettings.{env}.json", true, true)
                .AddEnvironmentVariables();

            var config = builder.Build();
            #endregion

            Console.WriteLine($"...Version: {config["version"]}");
            Log.Information($"...Version: {config["version"]}");

            int periodInterval = -1;
            string periodType = config["loop_interval:periodType"];

            //Simple logic, just grab the first character and lower it for later parsing usage.  
            if( !string.IsNullOrEmpty(periodType))
            {
                periodType = periodType.Substring(0, 1).ToLower();
            }

            DateTime startInterval = DateTime.MinValue;
            DateTime endInterval = DateTime.MaxValue;

            int.TryParse(config["loop_interval:period"], out periodInterval);
            DateTime.TryParse(config["loop_interval:dateStart"], out startInterval);
            DateTime.TryParse(config["loop_interval:dateEnd"], out endInterval);

            int listLimitOfQuery = 1000;
            int.TryParse(config["elasticsearchSettings:listLimitOfQuery"], out listLimitOfQuery);

            DateTime startDate = DateTime.MinValue;
            DateTime endDate = DateTime.MaxValue;

            if ( !string.IsNullOrEmpty(config["filter:dateStart"]) )
            {
                startDate = DateTime.Parse(config["filter:dateStart"]);
            }
            if (!string.IsNullOrEmpty(config["filter:dateEnd"]))
            {
                endDate = DateTime.Parse(config["filter:dateEnd"]);
            }
            string url = config["elasticsearchSettings:url"];//"http://localhost:9200"; 
            string userName = config["elasticsearchSettings:username"];
            string password = config["elasticsearchSettings:password"];
            string tenantCode = config["tenantCode"];
            string sourceIndex_Override = config["sourceIndex_override"];
            string targetIndex_suffix = config["targetIndex_suffix"];

            Dictionary<string, string> tenantLookup = new Dictionary<string, string>();
            var tenantSection = config.GetSection($"clientTenantMapping:{tenantCode}");
            foreach (var kv in tenantSection.GetChildren())
            {
                tenantLookup.Add(kv.Key, kv.Value);
            }

            if( tenantLookup == null || tenantLookup.Count == 0)
            {
                Log.Error($"No Tenant Lookup defined for the given client, Migration tool requires Client Tenant Mapping [clientTenantMapping:{tenantCode}] definition to continue. Tenant: {tenantCode}");
                Console.WriteLine($"No Tenant Lookup defined for the given client, Migration tool requires Client Tenant Mapping [clientTenantMapping:{tenantCode}] definition to continue. Tenant: {tenantCode}");
                return;
            }

            ConnectionSettings settings = new ConnectionSettings(new Uri(url));
            settings.BasicAuthentication(userName, password);
            settings.ThrowExceptions(alwaysThrow: true);
            settings.PrettyJson();

            string clientName = config["client"];
            string sourceIndex = clientName;
            string indexName = GetIndexName(clientName);

            if( !string.IsNullOrEmpty(sourceIndex_Override))
            {
                indexName = sourceIndex_Override;
            }

            string originalIndex = sourceIndex;
            string indexPrefix = sourceIndex;
            List<CallSummary> psapList;

            using (Data.ElasticSearch es = new Data.ElasticSearch(settings))
            {
                List<string> callIdList_singlePsap;
                List<string> callIdlist_Transfer;

                Dictionary<string, string> singlePsap_PsapLookup;
                //List<CallSummary> callSummaryList_singlePsap;
                List<CallSummary> callSummaryList_Transfer;
                IEnumerable<List<string>> brokenList_callIdList;
                List<string> redindexList;
                IEnumerable<List<string>> brokenList;
                //overall tracking of all Ids processed - leveraged for the delete routine.
                List<string> processCallSummaryCallIdList = new List<string>();
                string singlePsapValue;

                #region Check to see if the PSAPs available in the given date range are defined in the client mapping. 
                Dictionary<string, long> psapDistinctList = es.GetDistinctPSAPList(indexName, new Data.FilterCriteria() { StartDate = startDate, EndDate = endDate });
                if (psapDistinctList != null && psapDistinctList.Count > 0)
                {
                    Console.WriteLine($"Checking PSAP list of:\n\r\t-{string.Join("\n\r\t-", psapDistinctList.Keys)}");

                    foreach (string psapKey in psapDistinctList.Keys)
                    {
                        if (psapKey.Contains(","))
                        {
                            Log.Warning($"Skipping '{psapKey}' as it is a joined psap list...");
                            continue;
                        }

                        if (tenantLookup.ContainsKey(psapKey.ToLower()))
                        {
                            Log.Information($"Found '{psapKey}'.");
                            continue;
                        }
                        else
                        {
                            Log.Error($"Did not locate '{psapKey}' ( records found: {psapDistinctList[psapKey]}).  Stopping process, please add {psapKey} to the define [clientTenantMapping] for Client {tenantCode}");
                            Console.WriteLine($"Did not locate '{psapKey}' ( records found: {psapDistinctList[psapKey]}).  Stopping process, please add {psapKey} to the define [clientTenantMapping] for Client {tenantCode}");
                            return;
                        }
                    }

                }
                else
                {
                    Log.Warning($"No distinct PSAP's located, therefore there is no need to run the migration tool as no clients where detected. Tenant: {tenantCode}");
                    Console.WriteLine($"No distinct PSAP's located, therefore there is no need to run the migration tool as no clients where detected. Tenant: {tenantCode}");
                    return;
                }
                #endregion

                bool intervalActive = true;
                if (periodInterval > 0 && startInterval != DateTime.MinValue && endInterval != DateTime.MaxValue)
                {
                    startDate = startInterval;

                    switch (periodType)
                    {
                        case "m":
                            endDate = startDate.AddMonths(periodInterval);
                            break;
                        case "d":
                            endDate = startDate.AddDays(periodInterval);
                            break;
                        case "w":
                            endDate = startDate.AddDays(periodInterval * 7);
                            break;
                        default:
                            break;
                    }

                    if (endDate > endInterval)
                    {
                        endDate = endInterval;
                    }

                    Console.WriteLine($"Interval parameters in use: from {startInterval} to {endInterval}, incrementing in {periodInterval}{periodType}");
                    Log.Information($"Interval parameters in use: from {startInterval} to {endInterval}, incrementing in {periodInterval}{periodType}");
                }
                else
                {
                    endInterval = DateTime.MaxValue;
                }

                #region Main Logic - looping across date range based on the AppSetting configuration
                while (intervalActive && startDate < endInterval)
                {
                    Console.WriteLine($"Searching from {startDate} to {endDate}");
                    Log.Information($"Searching from {startDate} to {endDate}");

                    #region retrieve the Call Ids 

                    es.GetCallIdLists(indexName, new Data.FilterCriteria() { StartDate = startDate, EndDate = endDate }, out callIdList_singlePsap, out callIdlist_Transfer);

                    singlePsap_PsapLookup = new Dictionary<string, string>();
                    callSummaryList_Transfer = new List<CallSummary>();

                    Console.WriteLine($"Searched resulted in Single PSAP {callIdList_singlePsap.Count} and Transfer {callIdlist_Transfer.Count} results.  Retrieving...");

                    #endregion

                    #region Seperation of the data into specific Call Summary collections

                    //due to the usage of Match case, need to make sure the query isn't to large for the API, thus needs to be a group of queries out.
                    brokenList_callIdList = SplitList(callIdList_singlePsap, listLimitOfQuery);
                    foreach (List<string> sub_callIdList in brokenList_callIdList)
                    {
                        singlePsap_PsapLookup = singlePsap_PsapLookup.Concat(es.GetCallIdPSAPList(indexName, sub_callIdList)).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

                        Console.WriteLine($"Retrieving Single PSAP Call Summary list for: {sub_callIdList.Count} count, total found: {singlePsap_PsapLookup.Keys.Count}.");
                    }
                    #endregion

                    #region Single PSAP Call Summaries - Re-index logic/action.
                    redindexList = new List<string>();
                    Console.WriteLine("Processing of Single PSAP call summaries");
                    brokenList = null;

                    if (singlePsap_PsapLookup != null && singlePsap_PsapLookup.Keys.Count > 0)
                    {
                        Dictionary<string, List<string>> reindexByPsap = new Dictionary<string, List<string>>();

                        foreach (string callIdKey in singlePsap_PsapLookup.Keys)
                        {
                            //skip empty psap records.
                            singlePsapValue = singlePsap_PsapLookup[callIdKey];
                            if ( string.IsNullOrEmpty(singlePsapValue)) //PsapName
                            {
                                continue;
                            }

                            Log.Debug($"CallId: {callIdKey} (Psap: {singlePsapValue})");

                            if( !reindexByPsap.ContainsKey(singlePsapValue))
                            {
                                reindexByPsap[singlePsapValue] = new List<string>();
                            }
                            
                            //only add the distint CallId for the given PSAP 
                            if( !reindexByPsap[singlePsapValue].Contains(callIdKey))
                            {
                                reindexByPsap[singlePsapValue].Add(callIdKey);
                            }
                        }

                        //Need to break up the requests into a smaller group of multiple calls as to not overload the ES instance.
                        //Based on configuration - to allow for easier tweaking.
                        ReindexOnServerResponse rtn;
                        foreach(string psapKey in reindexByPsap.Keys)
                        {
                            brokenList = SplitList(reindexByPsap[psapKey], listLimitOfQuery);

                            string reindexTarget;

                            foreach (List<string> sub_reindexList in brokenList)
                            {
                                reindexTarget = $"{sourceIndex}-{tenantLookup[psapKey.ToLower()]}".ToLower();

                                if( !string.IsNullOrEmpty(targetIndex_suffix))
                                {
                                    reindexTarget = $"{reindexTarget}{targetIndex_suffix}";
                                }

                                Log.Information($"Located PSAP, reindex postfix target from {sourceIndex} to {reindexTarget}");

                                rtn = es.Reindex(indexName, reindexTarget, sub_reindexList);

                                Console.WriteLine($"DONE REINDEX of single PSAP items, Total: {rtn.Total}");
                                Log.Information($"DONE REINDEX of single PSAP items, Total: {rtn.Total}");

                                processCallSummaryCallIdList.AddRange(sub_reindexList);
                            }
                        }
                        
                        Console.WriteLine($"COMPLETED REINDEX of single PSAP items");
                        Log.Information($"COMPLETED REINDEX of single PSAP items");
                    }
                    else
                    {
                        Console.WriteLine($"------- No Single PSAP Call Summares Located. ------- ");
                        Log.Information($"------- No Single PSAP Call Summares Located. ------- ");
                    }
                    #endregion

                    brokenList_callIdList = SplitList(callIdlist_Transfer, listLimitOfQuery);
                    List<Thread> threadList = new List<Thread>();
                    foreach (List<string> sub_callIdList in brokenList_callIdList)
                    {
                        //callSummaryList_Transfer.AddRange(es.GetCallSummaryList(indexName, sub_callIdList));
                        //Console.WriteLine($"Retrieving Transfer PSAP Call Summary list for: {sub_callIdList.Count} count, total found: {callSummaryList_Transfer.Count}");

                        Thread tr = new Thread(delegate () {
                            ProcessByCallIdGroup(sub_callIdList, indexName, originalIndex, sourceIndex, indexPrefix, targetIndex_suffix, tenantLookup, es, ref processCallSummaryCallIdList);
                        });

                        threadList.Add(tr);

                        Console.WriteLine($"Thread #{threadList.Count} started. Thread Id: {tr.ManagedThreadId}");


                        /*///1 ///Keeping this code as refernce, issue was it was completing the threads before the function full finnished. 
                                //using System.Threading.Tasks;
                        Thread tr = new Thread(delegate () {
                            ProcessByCallIdGroup(sub_callIdList, indexName, originalIndex, sourceIndex, indexPrefix, targetIndex_suffix, tenantLookup, es, ref processCallSummaryCallIdList);
                        });
                        Task t = Task.Factory.StartNew(() => tr.Start());
                        //For tracking of the threads to make sure they are completed.
                        taskList.Add(t);

                        Console.WriteLine($"Thread #{taskList.Count} started. Thread Id: {tr.ManagedThreadId}");

                        /// AFTER THE FOREACH LOOP: 1 Task.WaitAll(taskList.ToArray());
                        */
                        //ProcessByCallIdGroup(sub_callIdList, indexName, originalIndex, sourceIndex, indexPrefix, targetIndex_suffix, tenantLookup, es, ref processCallSummaryCallIdList);
                    }

                    threadList.ForEach(x => x.Start());
                    Console.WriteLine($"All {threadList.Count} threads started...");

                    threadList.ForEach(x => x.Join());  //join locks the process until the thread is completed, in affect awaiting each thread to complete.
                    Console.WriteLine($"All {threadList.Count} threads Completed...");

                    #region Data Validation checks

                    //Section performed a data validation based on processed data against original source data.
                    if (_ValidatProcessed)
                    {
                        if (processCallSummaryCallIdList != null && processCallSummaryCallIdList.Count > 0)
                        {
                            processCallSummaryCallIdList = processCallSummaryCallIdList.Distinct().ToList();

                            Dictionary<string, List<CallSummaryLimited>> dest_CallsummaryAudit = new Dictionary<string, List<CallSummaryLimited>>();
                            Dictionary<string, List<CallSummaryLimited>> src_CallsummaryAudit = new Dictionary<string, List<CallSummaryLimited>>();
                            List<string> errorList = new List<string>();

                            brokenList_callIdList = SplitList(processCallSummaryCallIdList, listLimitOfQuery);

                            //Destination index is a wildcard to capture data from all PSAP processed elements.
                            //UPDATED since the root index was not being included as it does not have a character present between the elements to wildcard off of.  i.e. it is "ClientName""targetIndex_suffix"
                            string wildcardIndex = $"{GetIndexName(clientName)}*{targetIndex_suffix.TrimStart('_')}";

                            Log.Information($"Validation Routine check, comparing source: {indexName} against destination: {wildcardIndex}");
                            Console.WriteLine($"Validation Routine check, comparing source: {indexName} against destination: {wildcardIndex}");

                            int totalProcessed = 0;

                            foreach (List<string> sub_callIdList in brokenList_callIdList)
                            {
                                totalProcessed += sub_callIdList.Count;
                                Log.Information($"Validation of {totalProcessed} of {processCallSummaryCallIdList.Count}...");

                                dest_CallsummaryAudit = es.GetLimitedCallSummaryByCallId(wildcardIndex, sub_callIdList);

                                src_CallsummaryAudit = es.GetLimitedCallSummaryByCallId(indexName, sub_callIdList);

                                Log.Information($"... Routine check found {dest_CallsummaryAudit.Count} destination CallIds and {src_CallsummaryAudit.Count} source CallIds.");

                                //doing the loop validation here instead of creating the master list - since we are dealing with the same CallId list, this self contained loop logic will work as expected.
                                foreach (string callIdKey in src_CallsummaryAudit.Keys)
                                {
                                    //Check 0: making sure the destination HAS the records.
                                    if (!dest_CallsummaryAudit.ContainsKey(callIdKey))
                                    {
                                        Log.Error($"ERROR 1.0: {callIdKey} does not exist in the Destination Indexes.");
                                        errorList.Add(callIdKey);
                                        continue;
                                    }
                                    
                                    //check 1: If the Children elements are equal.
                                    if( dest_CallsummaryAudit[callIdKey].Count(n => !n.IsRootElement) != src_CallsummaryAudit[callIdKey].Count(n => !n.IsRootElement))
                                    {
                                        Log.Error($"ERROR 1.1: {callIdKey} does not match CallSummary Children Counts.  Src: {src_CallsummaryAudit[callIdKey].Count(n => !n.IsRootElement)}, Dest: {dest_CallsummaryAudit[callIdKey].Count(n => !n.IsRootElement)}");
                                        errorList.Add(callIdKey);
                                        continue;
                                    }
                                    //check 2: if the root element is not the first element in the destination list
                                    if (!dest_CallsummaryAudit[callIdKey][0].IsRootElement)
                                    {
                                        bool failed_validation = true; //used for Alternative Route followup check if required.

                                        //edge case, when the leading value is an Alternative route case, therefore need to skip over this value and confirm the First Summary that isn't an Alternative Root case.
                                        if( dest_CallsummaryAudit[callIdKey][0].IsAlternativeRoute == 1)
                                        {
                                            for(int i = 1; i < dest_CallsummaryAudit[callIdKey].Count; i++)
                                            {
                                                if(dest_CallsummaryAudit[callIdKey][i].IsAlternativeRoute == 1)
                                                {
                                                    continue;   //finding the first non-alternative route case
                                                }
                                                else if( !dest_CallsummaryAudit[callIdKey][i].IsRootElement)
                                                {
                                                    Log.Error($"ERROR 1.2.1: {callIdKey} containes an Alternative Route case, skipped {i} forward for Check. CallDetailsId: {dest_CallsummaryAudit[callIdKey][i].CallDetailsId}");
                                                    break;
                                                }

                                                //if it gets here, it means it went past the first Summary record that isn't an alt.Route case fine.
                                                failed_validation = false;
                                                break;
                                            }
                                        }
                                        
                                        if( failed_validation )
                                        {
                                            Log.Error($"ERROR 1.2: {callIdKey} does not contain a leading Root callSummary.  First CallDetailsId: {dest_CallsummaryAudit[callIdKey][0].CallDetailsId}");
                                            errorList.Add(callIdKey);
                                            continue;
                                        }
                                    }
                                    //check 3: if the number of root callsummaries are more or equal in the destination src
                                    if (dest_CallsummaryAudit[callIdKey].Count(n => n.IsRootElement) < src_CallsummaryAudit[callIdKey].Count(n => n.IsRootElement))
                                    {
                                        Log.Error($"ERROR 1.3: {callIdKey} does not match CallSummary counts.  Src: {src_CallsummaryAudit[callIdKey].Count(n => n.IsRootElement)}, Dest: {dest_CallsummaryAudit[callIdKey].Count(n => n.IsRootElement)}");
                                        errorList.Add(callIdKey);
                                        continue;
                                    }
                                }
                            }                            

                            Log.Information($"Validation Routine check completed with {errorList.Count} Callids in failed validation state.");
                            Console.WriteLine($"Validation Routine check completed with {errorList.Count} Callids in failed validation state.");

                            //remove any errorlist callids form the processed list to maintain them in the source data set for re-processing/analysis of failure.
                            if (errorList.Count > 0)
                            {
                                Log.Error($"Total CallIds that failed validation: {errorList.Count}.");

                                foreach (string callIdKey in errorList)
                                {
                                    processCallSummaryCallIdList.Remove(callIdKey);
                                    Log.Information($"-- Removed {callIdKey} from processCallSummaryCallIdList.");
                                }
                            }
                            
                        }
                    }
                    #endregion

                    #region Deletion of the Source Index of records processed

                    if (_DeleteProcessed)
                    {
                        if (processCallSummaryCallIdList != null && processCallSummaryCallIdList.Count > 0)
                        {
                            long deletionCount = 0;

                            processCallSummaryCallIdList = processCallSummaryCallIdList.Distinct().ToList(); //This is completed above, but just reproducing in case _validate is false.

                            brokenList_callIdList = SplitList(processCallSummaryCallIdList, listLimitOfQuery / 4);    //listLimit is reduced as DELETE is far more sensative to size.

                            DeleteByQueryResponse drtn;
                            foreach (List<string> sub_callIdList in brokenList_callIdList)
                            {
                                //Same issue as REDINDEX need to do smaller chunks - broken list is already created above.
                                drtn = es.DeleteDocuments(indexName, sub_callIdList);
                                deletionCount += drtn.Deleted;
                            }

                            Console.WriteLine($"Deleted the processed items, Deleted.Count: {deletionCount}");
                            Log.Information($"Deleted the processed items, Deleted.Count: {deletionCount}");
                        }

                     
                    }
                    #endregion

                    Console.WriteLine($"Processed Unique Calls, Count: {processCallSummaryCallIdList.Count}");
                    Log.Information($"Processed Unique Calls, Count: {processCallSummaryCallIdList.Count}");
                    processCallSummaryCallIdList.Clear();

                    #region While Loop update of Interval variables
                    //internval updating if required
                    if (periodInterval > 0 && startInterval != DateTime.MinValue && endInterval != DateTime.MaxValue)
                    {
                        switch (periodType)
                        {
                            case "m":
                                startDate = startDate.AddMonths(periodInterval);
                                endDate = startDate.AddMonths(periodInterval);
                                break;
                            case "d":
                                startDate = startDate.AddDays(periodInterval);
                                endDate = startDate.AddDays(periodInterval);
                                break;
                            case "w":
                                startDate = startDate.AddDays(periodInterval * 7);
                                endDate = startDate.AddDays(periodInterval * 7);
                                break;
                            default:
                                break;
                        }

                        if (endDate > endInterval)
                        {
                            endDate = endInterval;
                        }

                        Console.WriteLine($"---- Next interval: {startDate} for {periodInterval}{periodType}");
                        Log.Information($"---- Next interval: {startDate} for {periodInterval}{periodType}");
                    }
                    else
                    {
                        intervalActive = false;
                    }

                    #endregion
                }

                #endregion

            }

            Log.CloseAndFlush();
        }

        private static bool ProcessByCallIdGroup(List<string> sub_callIdList, string indexName,
            string originalIndex, string sourceIndex, string indexPrefix, string targetIndex_suffix,
            Dictionary<string, string> tenantLookup, Data.ElasticSearch es, ref List<string> processCallSummaryCallIdList)
        {
            
            List<CallSummary> callSummaryList_Transfer  = es.GetCallSummaryList(indexName, sub_callIdList);
            Console.WriteLine($"Retrieving Transfer PSAP Call Summary list for: {sub_callIdList.Count} count, total found: {callSummaryList_Transfer.Count}");


            //Section that processes the Multiple tenant cases.

            List<CallSummary> psapList;

            Console.WriteLine($"Transfer Psap List (Total: {callSummaryList_Transfer.Count})");
            Log.Information($"Transfer Psap List (Total: {callSummaryList_Transfer.Count})");
            Dictionary<string, List<CallSummary>> summaryByCallId = new Dictionary<string, List<CallSummary>>();
            foreach (CallSummary cs in callSummaryList_Transfer)
            {
                //to make parsing easier, clustering the Call Summary records per Call Id.
                if (!summaryByCallId.ContainsKey(cs.Callid))
                {
                    summaryByCallId.Add(cs.Callid, new List<CallSummary>());
                }
                summaryByCallId[cs.Callid].Add(cs);

                Log.Debug($"CallId: {cs.Callid} (Psap: {cs.PsapName})");
            }

            foreach (string callId in summaryByCallId.Keys)
            {
                ///**DIFFERENT FROM SOURCE**
                /// - Logger is a different object (Log.* vs _logger.Log*)
                /// - psapList is defined differently.
                /// - psapList collapsing - since we don't have direct knowledge of inner transferring, any concurrent matching PSAP lines are ignored for transfer i.e. [Geneva,Geneva,Geneva,Geneva] to [Geneva] vs [Geneva,Enterprise,Geneva,Geneva] to [Geneva,Enterprise,Geneva]
                /// - Insert to ES works with the local class + slight steamline logic usage.
                #region Logic pulled from Main code base for tenant parsing
                Dictionary<string, List<CallSummary>> callSummaryClients = new Dictionary<string, List<CallSummary>>();

                //introduced due to isAlternativeRoute - to enable order enforcement of processing of empty PSAP case.
                List<string> processedPSAPKeys = new List<string>();

                //fetch all non empty PSAP names that are unique ...
                //Filters a unique PSAP PER call leg - based on unique grouping from PSAP and CallPresented, with the filtering against IsTransferred
                ////The grouping is required to capture if there is two seperate call legs / transfer events in the same PSAP.

                //Fetch all distinct PSAPs outside the Root Call Summary **DIFFERENT FROM SOURCE**
                psapList = summaryByCallId[callId].Where(cs => !string.IsNullOrEmpty(cs.PsapName) && cs.CallDetailsId.ToString() != "00000000-0000-0000-0000-000000000000").GroupBy(cs => new { cs.PsapName, cs.CallPresented }).Select(pn => pn.FirstOrDefault()).ToList();
                //List that tracks the PSAP idenfier to actual PSAP name, required for multiple inner transfer cases (i.e. PSAP1 -> PSAP2 -> PSAP1)
                Dictionary<string, string> psapLookupList = new Dictionary<string, string>();
                //If there is a transfer case of note
                if (psapList != null && psapList.Count > 0)
                {
                    //Displaying debug information based on the filtering list of Call Summary data.
                    //Base case, there is a transfer event and multiple Psap's are part of it.
                    if (psapList.Count > 1)
                    {
                        Log.Information($"Transfer Event with multiple Psaps, callid: {psapList[0].Callid} - PSAP List: {string.Join("|", psapList.Select(x => x.PsapName))}");

                        if (psapList.Count > 5)
                        {
                            Log.Warning($"Detecting a high level of PSAPs {psapList.Count} - investigation maybe required. CallId {psapList[0].Callid}");
                        }
                    }
                    else if (psapList.Count == 1)   //there is a transfer event, but only a single PSAP defined.  No additional logic required.
                    {
                        Log.Warning($"Transfer Event with only a single PSAP listed, callid: {psapList[0].Callid} - PSAP: {psapList[0].PsapName}");
                    }
                    else
                    {
                        Log.Warning($"Transfer Event with no PSAP information detected, callid: {summaryByCallId[callId][0].Callid}");
                    }
                    //Geneva|Geneva|Geneva|Geneva|Geneva
                    int psapIdx;
                    string psapLookup;
                    string previousPsap = string.Empty;
                    //initializing the listing object that will be used for the transferring logic.
                    foreach (CallSummary cs in psapList)
                    {
                        /////**DIFFERENT FROM SOURCE** NEW CASE: if the previous PSAP is the same, don't register it as a transfer PSAP case.
                        //// Logic is to avoid catching the internal ring group cases in a PSAP, assuming they are NOT internal transfer events.
                        if (previousPsap == cs.PsapName)
                        {
                            continue;
                        }
                        previousPsap = cs.PsapName;
                        /////

                        psapLookup = cs.PsapName;
                        //Adds a field PER call leg, appending a index count to a given PSAP if it is re-transferred to as part of the calls. 
                        psapIdx = 1;
                        while (callSummaryClients.Keys.Contains(psapLookup))
                        {
                            psapLookup = $"{psapLookup}_{psapIdx++}";
                        }
                        if (!psapLookup.Contains(",")) //skipping the root / comma seperated list - since PSAPs are singular elsewhere, no need to worry about secondary parsing of this case
                        {
                            callSummaryClients.Add(psapLookup, new List<CallSummary>());
                            //storing the source PSAP Name from the lookup, to enable easier handling of later logic for index and call summary generation.
                            if (!psapLookupList.ContainsKey(psapLookup))
                            {
                                psapLookupList.Add(psapLookup, cs.PsapName);
                            }
                        }

                    }

                    //Case can occur if there is only the single PSAP available outside the Root element comma list.
                    if (callSummaryClients.Count == 1)
                    {
                        callSummaryClients[callSummaryClients.Keys.First()] = summaryByCallId[callId];
                        Log.Warning($"Single PSAP found for callid: {summaryByCallId[callId][0].Callid}, PSAP: {callSummaryClients.Keys.First()}");
                    }
                    else if (callSummaryClients.Count == 0) //this case shouldn't occur naturally, however, logic present for this edge case - where there was no  PSAPs found.
                    {
                        callSummaryClients.Add(string.Empty, summaryByCallId[callId]);
                        Log.Warning($"No Unique PSAP found for callid: {summaryByCallId[callId][0].Callid}, PSAP List - {string.Join("|", psapList.Select(x => x.PsapName))}.");
                    }


                }
                else  //Create the single element call summary since it isn't a transfer case
                {
                    //retrieve the PSAP name from the avialable call summary data. - excluding the ROOT element as that is a summary element which can contain a comma seperated PSAP list. 
                    List<CallSummary> psapListSingle = summaryByCallId[callId].Where(cs => !string.IsNullOrEmpty(cs.PsapName) && cs.CallDetailsId.ToString() != "00000000-0000-0000-0000-000000000000").GroupBy(cs => cs.PsapName).Select(pn => pn.FirstOrDefault()).ToList();

                    if (psapListSingle != null && psapListSingle.Count > 0)
                    {
                        callSummaryClients.Add(psapListSingle[0].PsapName, summaryByCallId[callId]);
                    }
                    else  //no PSAP case - SHOULD be very rare.
                    {
                        //New backup check, if the PSAPList is empty, retrieve the PSAP and retrieve the first valid psap element. (comma seperate issue in historical data)
                        psapListSingle = summaryByCallId[callId].Where(cs => !string.IsNullOrEmpty(cs.PsapName)).GroupBy(cs => cs.PsapName).Select(pn => pn.FirstOrDefault()).ToList();
                        if (psapListSingle != null && psapListSingle.Count > 0)
                        {
                            Log.Warning($"PSAP found only in Root Element for callid: {summaryByCallId[callId][0].Callid}, PSAP {summaryByCallId[callId][0].PsapName}");
                            callSummaryClients.Add(psapListSingle[0].PsapName.Split(',')[0], summaryByCallId[callId]);
                        }
                        else
                        {
                            callSummaryClients.Add(string.Empty, summaryByCallId[callId]);
                            Log.Warning($"No PSAP found for callid: {summaryByCallId[callId][0].Callid}");
                        }
                    }

                }

                CallSummary rootCallSummary = null;

                //Tracks the unique PSAPs in the transfer and how many times they are processed.
                Dictionary<string, int> mappedPsapLookup = new Dictionary<string, int>();
                foreach (string psapLookupKey in psapLookupList.Keys)
                {
                    mappedPsapLookup.Add(psapLookupKey, 0);
                }

                processedPSAPKeys = callSummaryClients.Keys.ToList();
                string mappedPsap = string.Empty;
                if (callSummaryClients != null && callSummaryClients.Count > 1)     //no need for any processing if there is only ONE PSAP defined.
                {

                    //Used to track updating the time tracking of End Time and Start Time for each non-root Call summary record.
                    DateTime callReleasedPerPsap = DateTime.MinValue;
                    DateTime callArrivedPerPsap = DateTime.MinValue;

                    //Loop through the data and add the call summaries to the collection based on the current PSAP detected.
                    //CallSummary records are always in order of the call
                    foreach (CallSummary callsummary in summaryByCallId[callId])
                    {
                        if (callsummary.CallDetailsId == Guid.Empty)
                        {
                            rootCallSummary = callsummary;
                            continue;  // will do logic of Call Summary root addition after the initial seeding. 
                        }

                        //Updating the currently active PSAP in the call
                        if (!string.IsNullOrEmpty(callsummary.PsapName))
                        {
                            if (callSummaryClients.ContainsKey(callsummary.PsapName))
                            {

                                //grabbing the count of the current PSAP transferring events, to track different legs of a transfer in the same PSAP
                                int mappedPsapLoopupIndex = mappedPsapLookup[callsummary.PsapName];

                                if (mappedPsapLoopupIndex > 0)
                                {
                                    mappedPsap = $"{callsummary.PsapName}_{mappedPsapLoopupIndex}";
                                }
                                else
                                {
                                    mappedPsap = callsummary.PsapName;
                                }

                                //Case when there are multiple PSAPs in the callsummary collection, but they are NOT transfer events.  (i.e. Transfer -> ringgroup -> transfer)
                                //Make sure the index is avialable before incrementing the tracking state
                                if (callSummaryClients.ContainsKey($"{callsummary.PsapName}_{mappedPsapLoopupIndex + 1}"))
                                {
                                    //Tracking the number of times the given PSAP is transferred to - thus allowing a mapping to the unique call legs.
                                    mappedPsapLookup[callsummary.PsapName]++;
                                }
                            }
                        }

                        //Adding the Call Summary record to the appropriate element position.
                        if (!string.IsNullOrEmpty(mappedPsap))
                        {
                            callSummaryClients[mappedPsap].Add(callsummary);
                        }
                        else
                        {
                            //Does occur on edge case, where the leading Call Summary records do not have a PSAP associated to them, termed a "Alternative Route" case.
                            Log.Error($"Alternative Route case detected.  PSAP not defined in Transfer Population logic for callId: {callsummary.Callid}");
                            //https://solacomtech.atlassian.net/browse/INFO-1366
                            //NEW LOGIC - adding in the empty look up entry, as there are few edge cases with this behaviour.
                            if (!callSummaryClients.ContainsKey(string.Empty))
                            {
                                callSummaryClients.Add(string.Empty, new List<CallSummary>());
                            }
                            //setting the flag to track these occurrences.
                            callsummary.IsAlternativeRoute = 1;
                            callSummaryClients[string.Empty].Add(callsummary);
                        }
                    }

                    //resetting the count tracking for need for Call Summary Root id incrementing.
                    mappedPsapLookup = new Dictionary<string, int>();
                    foreach (string psapLookupKey in psapLookupList.Keys)
                    {
                        mappedPsapLookup.Add(psapLookupKey, 0);
                    }

                    //Loop which pushes the Call summary Root object to each PSAP in the call.   Updating the Call Summary as required for this seperation.
                    int currentIndex = 0;
                    foreach (string csKey in processedPSAPKeys)   //to reenforce ordering processing
                    {
                        //Case: Empty PSAP edge case, need to not re-create the Call Summary root on this case as it is not a full call leg, 
                        // but capturing of the Call Summary into the Root (emtpy) PSAP index.  No data is updated for these cases.
                        if (csKey == string.Empty)
                        {
                            continue;
                        }

                        string esPSAPName = psapLookupList[csKey];

                        CallSummary newRoot = rootCallSummary.ShallowClone();
                        newRoot.TransferFrom = newRoot.TransferTo = string.Empty;   //required to reinit the transfer fields as later runs pull Call Summary data from ES for initializing.
                        newRoot.PsapName = esPSAPName;   //setting the PSAP name as the singular PSAP vs the comma seperated
                        newRoot.IsInternalTransferCall = 0; //setting the default state.
                        newRoot.Id = $"{rootCallSummary.Callid}_0"; //reintializing the Root identifier - required due to the append behaviour on a reprocess occurrence.

                        newRoot.IsAbandoned = false;
                        newRoot.IsAbandonedCallback = newRoot.IsCallback = newRoot.AbandonedState = newRoot.AgentAbandonedState = 0;
                        newRoot.IsOutbound = newRoot.CompletedState = 0;
                        newRoot.HoldTimeInSeconds = newRoot.NonEmergencyHoldTimeInSeconds = null;

                        //Setting the time fields on the call summary root record based on the first record.
                        if (callSummaryClients[csKey].Count > 0)
                        {
                            newRoot.TimeStamp = callSummaryClients[csKey][0].CallAnswered.GetValueOrDefault();
                            newRoot.CallAnswered = callSummaryClients[csKey][0].CallAnswered;
                            //newRoot.TotalCallTimeInSeconds = (callSummaryClients[csKey][0].Endtime - callSummaryClients[csKey][0].Starttime).GetValueOrDefault().TotalSeconds;
                            //newRoot.TimeToAnswerInSeconds = (callSummaryClients[csKey][0].CallAnswered - callSummaryClients[csKey][0].CallArrived).GetValueOrDefault().TotalSeconds;

                            newRoot.IsAbandonedCallback = callSummaryClients[csKey].Any(cs => cs.IsAbandonedCallback == 1) ? 1 : 0;
                            newRoot.IsCallback = callSummaryClients[csKey].Any(cs => cs.IsCallback == 1) ? 1 : 0;
                            newRoot.IsAbandoned = callSummaryClients[csKey].Any(cs => cs.IsAbandoned == true);
                            newRoot.AbandonedState = callSummaryClients[csKey].Any(cs => cs.AbandonedState == 1) ? 1 : 0;   //abandonedState is always 1 or 0 
                            newRoot.IsOutbound = callSummaryClients[csKey].Any(cs => cs.IsOutbound == 1) ? 1 : 0;

                            //Edge case capturing when the call has no callstate fields - logging the case for audit awareness
                            CallSummary callState_callsummary = callSummaryClients[csKey].Where(cs => !string.IsNullOrEmpty(cs.Callstate)).FirstOrDefault();
                            if( callState_callsummary != null)
                            {
                                newRoot.Callstate = callState_callsummary.Callstate;
                            }
                            else
                            {   
                                Log.Warning($"Call Leg has no call state data, callid {rootCallSummary.Callid}, PSAP: {csKey}");
                            }
                            
                            newRoot.CompletedState = callSummaryClients[csKey].Any(cs => cs.CompletedState == 1) ? 1 : 0;

                        }
                        else  //Logically this shouldn't happen with the data - however, making sure we capture it for future analysis of edge cases that can occur. (can only occur if the Call Root is the only record with PSAP information and no additional children exist)
                        {
                            Log.Warning($"Call Leg has no data associated to it, skipping, callid {rootCallSummary.Callid}, PSAP: {csKey}");
                            continue;
                        }



                        //uniqueness requirement on the _id field, making sure it is always unique to the PSAP of the call.  (to handle non tenant configured transfer events)
                        int mappedPsapLoopupIndex = mappedPsapLookup[esPSAPName];
                        if (mappedPsapLoopupIndex > 0)   //Use case of having possible multiple Call Summary Roots, need to create a incremental index. 
                        {
                            newRoot.Id = $"{newRoot.Id}_{esPSAPName}_{mappedPsapLoopupIndex}";
                        }
                        else
                        {
                            newRoot.Id = $"{newRoot.Id}_{esPSAPName}";
                        }
                        mappedPsapLookup[esPSAPName]++;

                        //Logic to set the Transfer to / From based on the position in the call
                        if (currentIndex == 0 && psapLookupList.Count > 0)
                        {
                            newRoot.TransferTo = psapLookupList.ElementAt(currentIndex + 1).Value;
                        }
                        else if (currentIndex == psapLookupList.Count - 1 && currentIndex > 0)
                        {
                            newRoot.TransferFrom = psapLookupList.ElementAt(currentIndex - 1).Value;
                        }
                        else
                        {
                            newRoot.TransferFrom = psapLookupList.ElementAt(currentIndex - 1).Value;
                            newRoot.TransferTo = psapLookupList.ElementAt(currentIndex + 1).Value;
                        }

                        //Case, when there is a internal transfer event.
                        if (!string.IsNullOrEmpty(newRoot.TransferFrom) && string.Equals(newRoot.TransferFrom, newRoot.PsapName, StringComparison.OrdinalIgnoreCase))
                        {
                            newRoot.IsInternalTransferCall = 1;
                        }

                        callSummaryClients[csKey].Insert(0, newRoot);

                        //Looping through the resulting data set to set the End / Start Time for each transit leg
                        //get the Latest Call Released and First Call Arrived
                        callReleasedPerPsap = DateTime.MinValue;
                        callArrivedPerPsap = DateTime.MinValue;

                        var tempCallSummary = callSummaryClients[csKey].Where(c => c.CallReleased != null).OrderBy(co => co.CallReleased);
                        if (tempCallSummary != null && tempCallSummary.Count() > 0)
                        {
                            callReleasedPerPsap = tempCallSummary.Last().CallReleased.GetValueOrDefault();
                        }
                        else
                        {
                            Log.Warning($"Call Leg is missing Call Released data, callid: {rootCallSummary.Callid}, PSAP: {csKey}");
                        }
                        tempCallSummary = callSummaryClients[csKey].Where(c => c.CallArrived != null).OrderBy(co => co.CallArrived);
                        if (tempCallSummary != null && tempCallSummary.Count() > 0)
                        {
                            callArrivedPerPsap = tempCallSummary.First().CallArrived.GetValueOrDefault();
                        }
                        else
                        {
                            Log.Warning($"Call Leg is missing Call Arrived data, callid {rootCallSummary.Callid}, PSAP: {csKey}");
                            Log.Warning($"Retrieving root record timestamp instead of Call Arrived, callid {rootCallSummary.Callid}, PSAP: {csKey}");

                            callArrivedPerPsap = callSummaryClients[csKey][0].TimeStamp;
                        }
                        foreach (CallSummary callsummary in callSummaryClients[csKey])
                        {
                            callsummary.Endtime = callReleasedPerPsap;
                            callsummary.Starttime = callArrivedPerPsap;
                        }

                        //Final action - set the time calculations - needed to occur later as the starttime/endtime calculations needed to be compelted against the full call before these calculations can be applied.
                        if (callSummaryClients[csKey].Count > 0)    //To confirm the record exists - that there is a root summary event to the collection
                        {
                            if (callSummaryClients[csKey][0].IsEmergency)
                            {
                                var callsummarytemp = callSummaryClients[csKey].Where(cs => (cs.HoldTimeInSeconds != null));
                                if (callsummarytemp != null && callsummarytemp.Count() > 0)
                                {
                                    callSummaryClients[csKey][0].HoldTimeInSeconds = callsummarytemp.FirstOrDefault().HoldTimeInSeconds;
                                }
                                callSummaryClients[csKey][0].TotalCallTimeInSeconds = (callSummaryClients[csKey][0].Endtime - callSummaryClients[csKey][0].Starttime).GetValueOrDefault().TotalSeconds;
                                callSummaryClients[csKey][0].TimeToAnswerInSeconds = (callSummaryClients[csKey][0].CallAnswered - callSummaryClients[csKey][0].CallArrived).GetValueOrDefault().TotalSeconds;
                                callSummaryClients[csKey][0] = SetCallToAnswer(newRoot);
                            }
                            else
                            {
                                var callsummarytemp = callSummaryClients[csKey].Where(cs => (cs.NonEmergencyHoldTimeInSeconds != null));
                                if (callsummarytemp != null && callsummarytemp.Count() > 0)
                                {
                                    callSummaryClients[csKey][0].NonEmergencyHoldTimeInSeconds = callsummarytemp.FirstOrDefault().NonEmergencyHoldTimeInSeconds;
                                }

                                callSummaryClients[csKey][0].NonEmergencyTotalCallTimeInSeconds = (callSummaryClients[csKey][0].Endtime.GetValueOrDefault() - callSummaryClients[csKey][0].Starttime.GetValueOrDefault()).TotalSeconds;
                                callSummaryClients[csKey][0].NonEmergencyTimeToAnswerInSeconds = (callSummaryClients[csKey][0].CallAnswered.GetValueOrDefault() - callSummaryClients[csKey][0].Starttime.GetValueOrDefault()).TotalSeconds;
                                callSummaryClients[csKey][0] = SetNonEmergencyCallToAnswer(newRoot);
                            }
                        }

                        currentIndex++;
                    }

                }
                //update the Non-transfer Call summary records with the time updates.  
                else if (callSummaryClients != null)
                {
                    //populating the Time Properities to each Call Summary record for single PSAP case.
                    string singleCSKey = callSummaryClients.Keys.First();
                    DateTime? rootEndTime = callSummaryClients[singleCSKey][0].Endtime; //source: PouplateEndTime - last occurence EndCall eventLog.timestamp
                    foreach (CallSummary callSummary in callSummaryClients[singleCSKey])
                    {
                        callSummary.Endtime = rootEndTime;
                        //Note: starttime is already part of the core call summary template 
                    }
                }

                ///
                ///Keeping logic for a template for the Historical Migration tool as this will need to be executed against historical data.
                ///
                /// Logic is excluded from the migration routine as the IsTransferred state is not available, therefore does not result in a accurate Time to Transfer time.
                #region Time To Transfer calculation based on existing Call summary record with populate Transfer state.
                //DateTime? storedCallPresented = null;
                ////Logic to calculated the Time to Transfer field - required to be a seperate loop to best populate the field
                //foreach (string csKey in callSummaryClients.Keys.Reverse())
                //{
                //    //List<CallSummary> psapListSingle = resultCallsummary.Where(cs => !string.IsNullOrEmpty(cs.PsapName)).GroupBy(cs => cs.PsapName).Select(pn => pn.FirstOrDefault()).ToList();
                //    CallSummary firstEntry = callSummaryClients[csKey].Where(cs => cs.IsTransferred == 1 && cs.CallDetailsId.ToString() != "00000000-0000-0000-0000-000000000000").FirstOrDefault();

                //    if( firstEntry == null )
                //    {
                //        _logger.LogWarning($"Could not locate the first entry of the Tranfer leg, CallId: {rootCallSummary.Callid}, PSAP Key: {csKey}");
                //        continue;
                //    }

                //    if( storedCallPresented != null)
                //    {
                //        firstEntry.TimeToTransferInSeconds = (storedCallPresented - firstEntry.CallAnswered).GetValueOrDefault().TotalSeconds;
                //    }
                //    storedCallPresented = firstEntry.CallPresented;
                //    if( storedCallPresented == null)
                //    {
                //        _logger.LogWarning($"Detected Call Summary record in a Transfer chain that is missing a valid Call Presented field, CallId: {firstEntry.Callid}, Id: {firstEntry.CallDetailsId}");
                //    }
                //}
                #endregion

                //Finally, add the call summary data to the ES instance
                originalIndex = sourceIndex;

                foreach (string psapClient in callSummaryClients.Keys)
                {
                    string esPSAPName = string.Empty;

                    if (!String.IsNullOrWhiteSpace(psapClient) && psapLookupList.Count > 0)  //required due to the non-transfer cases
                    {
                        esPSAPName = psapLookupList[psapClient];
                    }
                    else   //default case, single PSAP from the available list.
                    {
                        esPSAPName = psapClient;
                    }

                    if (!string.IsNullOrEmpty(esPSAPName) && tenantLookup.ContainsKey(esPSAPName.ToLower()))
                    {
                        indexPrefix = $"{originalIndex}-{tenantLookup[esPSAPName.ToLower()]}".ToLower();

                        Log.Information($"Located PSAP, redefined index from {originalIndex} to {indexPrefix}");
                    }
                    else
                    {
                        indexPrefix = originalIndex;
                    }

                    if (!string.IsNullOrEmpty(targetIndex_suffix))
                    {
                        indexPrefix = $"{indexPrefix}{targetIndex_suffix}";
                    }

                    Log.Information($"Update/Insert to index postfix {indexPrefix} root call summary with Call ID {callSummaryClients[psapClient][0].Callid}");

                    if (!string.IsNullOrEmpty(callSummaryClients[psapClient][0].Id))
                    {
                        //NOTE: the update routine does a UPSERT - therefore it does the insert if the record is not present already - so no additional conditional logic required.
                        // this is the ONLY place the call summary is generated or updated in the code - single entry point for writting on process of the call.
                        //await _callSummaryEsRepo.Update(callSummaryClients[psapClient][0], callSummaryClients[psapClient][0].Id, indexPrefix, null);
                        ///**DIFFERENT FROM SOURCE**
                        string rtn = es.Update(callSummaryClients[psapClient][0], callSummaryClients[psapClient][0].Id, indexPrefix);
                        if (!string.IsNullOrEmpty(rtn))
                        {
                            Log.Warning($"Update error: {rtn}");
                            continue;  //continue unto the next records, therefore no additional data movement nor deletion of records.
                        }

                        Log.Information($"Adding {callSummaryClients[psapClient][0].Callid} to Processed List");

                        //storing the processed CallId to the delete collection - NOTE: duplicate entries can occur here due to split PSAP logic - instead of duplicate check here, doing distinct filter later.
                        processCallSummaryCallIdList.Add(callSummaryClients[psapClient][0].Callid);

                        callSummaryClients[psapClient].RemoveAt(0);
                        if (callSummaryClients[psapClient] != null && callSummaryClients[psapClient].Count > 0)
                        {
                            rtn = es.InsertMany(callSummaryClients[psapClient], indexPrefix);
                            if (!string.IsNullOrEmpty(rtn))
                            {
                                Log.Warning($"{rtn}");
                            }
                        }
                    }
                    else
                    {   //sending exception to allow the code to handle the setting of the tracking state in the DB to maintain the record for future review if required.
                        throw new Exception($"Processing of events failed for Call ID: {callSummaryClients[psapClient][0].Callid} due to empty Id field.");
                    }

                    #endregion
                }
            }

            return true;
        }

        /// <summary>
        /// Performs a logic split on the data into smaller chunks for processing
        /// </summary>
        /// <typeparam name="T">Type Of object</typeparam>
        /// <param name="sourceList">Source object list</param>
        /// <param name="nSize">Size of new split collections</param>
        /// <returns></returns>
        public static IEnumerable<List<T>> SplitList<T>(List<T> sourceList, int nSize = 30)
        {
            for (int i = 0; i < sourceList.Count; i += nSize)
            {
                yield return sourceList.GetRange(i, Math.Min(nSize, sourceList.Count - i));
            }
        }
    }
}
