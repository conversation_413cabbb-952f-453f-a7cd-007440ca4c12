version: '3.4'

services:
 
#  mariadb:
#    image: mariadb:10.4
#    ports:
#      - "3306:3306"
#    environment:
#      MYSQL_ROOT_PASSWORD: root
#      MYSQL_USER: info
#      MYSQL_PASSWORD: root
#    restart: always
#    volumes:
#      - ./init.sql :/docker-entrypoint-initdb.d/init.sql

  elasticsearch:
    environment:
      http.host: 0.0.0.0
      transport.host: 127.0.0.1
    image: docker.elastic.co/elasticsearch/elasticsearch:7.2.0
    networks:
      elk: null
    ports:
      - 9200:9200
    restart: unless-stopped
    volumes:
      - elasticsearch:/usr/share/elasticsearch/data:rw

#  logstash:
#    depends_on:
#      - elasticsearch
#    image: docker.elastic.co/logstash/logstash-oss:7.2.0
#    ports:
#      - 5044:5044
#    networks:
#      elk: null
#    restart: unless-stopped
#    volumes:
#      - ./etc/logstash/pipeline:/usr/share/logstash/pipeline:ro

  kibana:
    depends_on:
      - elasticsearch
    environment:
      ELASTICSEARCH_PASSWORD: root
      ELASTICSEARCH_URL: http://elasticsearch:9200
      ELASTICSEARCH_USERNAME: root
    image: docker.elastic.co/kibana/kibana-oss:7.2.0
    ports:
      - 5601:5601
    networks:
      elk: null
    restart: unless-stopped
  
  rabbitmq:
    image: "rabbitmq:3-management"
    ports:
      - 15672:15672
      - 5672:5672

  redis:
    image: redis:latest
    ports:
      - 6379:6379
  
  redis-commander:
    container_name: redis-commander
    hostname: redis-commander
    image: rediscommander/redis-commander:latest
    restart: always
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"

#  collectorapi:
#    depends_on:
#      - redis
#      - elasticsearch
#    image: ${DOCKER_REGISTRY-}solacominfohubeventreceiverwebapi
#    ports:
#      - 8080:80
#    restart: unless-stopped

#  elmadapter:
#    depends_on:
#      - rabbitmq
#    image: ${DOCKER_REGISTRY-}elmadapter
#    ports:
#      - 8082:80
#    restart: unless-stopped
#  
#  rabbitmqconsumerservice:
#    depends_on:
#      - rabbitmq
#    image: ${DOCKER_REGISTRY-}rabbitmqconsumerservice
#    ports:
#      - 8084:80
#    restart: unless-stopped

networks:
  elk:

volumes:
  elasticsearch:
    driver: local
  my-db: