﻿using System.Xml.Serialization;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    [XmlRoot(ElementName = "busiedOut")]
    public class BusiedOut
    {
        [XmlElement(ElementName = "mediaLabel")]
        public string MediaLabel { get; set; }
        [XmlElement(ElementName = "uri")]
        public string Uri { get; set; }
        [XmlElement(ElementName = "agentRole")]
        public string AgentRole { get; set; }
        [XmlElement(ElementName = "tenantGroup")]
        public string TenantGroup { get; set; }
        [XmlElement(ElementName = "operatorId")]
        public string OperatorId { get; set; }
        [XmlElement(ElementName = "workstation")]
        public string Workstation { get; set; }
        [XmlElement(ElementName = "busiedOutAction")]
        public string BusiedOutAction { get; set; }
    }
}
