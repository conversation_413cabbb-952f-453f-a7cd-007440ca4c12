resource "aws_security_group" "main" {
  name        = "allow_all"
  description = "Allow all inbound and outbound traffic"
  vpc_id      = "${var.vpc_id}"

  ingress {
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags  = {
    Environment = "${terraform.workspace}"
    Name        = "${var.security_group_tag_name}-${terraform.workspace}"
  }
}