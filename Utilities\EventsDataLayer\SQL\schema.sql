﻿USE MASTER
IF EXISTS(select * from sys.databases where name='MisEvents')
DROP DATABASE MisEvents

CREATE DATABASE MisEvents

GO


USE [MisEvents]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('dbo.[Events]', 'U') IS NOT NULL 
  DROP TABLE dbo.[Events]; 

GO
CREATE TABLE [dbo].[Events] (
    [Id]									INT				IDENTITY (1, 1) NOT NULL PRIMARY KEY,
    [XmlContent]							XML				NOT NULL,
    [CallId]								NVARCHAR (200)	NULL,
    [TimeStamp]								DATETIME		NULL,
	[ClientCode]							NVARCHAR(20)	NOT NULL,
	[IsLastPushSuccess]						BIT				NULL,
	[LastPushTimeStamp]						DATETIME		NULL,
	LastPushMessage							NVARCHAR(MAX) NULL
);


