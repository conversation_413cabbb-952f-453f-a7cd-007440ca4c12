﻿<?xml version="1.0" encoding="utf-8"?>
<LogEvents>

  <LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>{{timestamp}}</timestamp>
    <agencyOrElement>{{PSAPName}}-A_A</agencyOrElement>
    <agent>operator</agent>
    <callIdentifier>{{CallID}}</callIdentifier>
    <incidentIdentifier>{{IncidentID}}</incidentIdentifier>
    <eventType>StartCall</eventType>
    <startCall>
      <header><![CDATA[INVITE sip:9991@************ SIP/2.0  Via: SIP/2.0/UDP ************:5060;branch=z9hG4bK-d8754z-2d4e8660672ae54f-1---d8754z-;rport  Max-Forwards: 70  Contact: <sip:8197720005@************:5060>  To: <sip:9991@************>  From: <sip:8197720005@***********>;tag=e7625571  Call-ID: YmFlYjQyMjkyOWM3NjJlMmZjYjNjNzA3ODViZmFkMTA.  CSeq: 1 INVITE  Allow: INVITE, ACK, CANCEL, OPTIONS, BYE, REFER, INFO, NOTIFY  Content-Type: application/sdp  Supported: replaces  User-Agent: ipTTY  Content-Length: 330]]></header>
      <location>.</location>
      <mediaLabel>_ML_dev_{{IterationCounter}}</mediaLabel>
      <incomingCallPolicy>.</incomingCallPolicy>
      <callType>SR911</callType>
      <signallingType>VOIP</signallingType>
      <circuit>31/01/00/0000</circuit>
      <circuitId>130154496</circuitId>
      <trunkGroupId>300</trunkGroupId>
      <ani>8197720005</ani>
      <aniDomain>************</aniDomain>
      <dnis>9991</dnis>
      <dnisDomain>************</dnisDomain>
      <pani>8197720005</pani>
      <esrn>.</esrn>
      <callerName>n/a</callerName>
      <concurrentCalls>-1</concurrentCalls>
    </startCall>
  </LogEvent>


  <LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>{{timestamp}}</timestamp>
    <agencyOrElement>{{PSAPName}}-A_A</agencyOrElement>
    <agent>.</agent>
    <callIdentifier>{{CallID}}</callIdentifier>
    <incidentIdentifier>{{IncidentID}}</incidentIdentifier>
    <eventType>Media</eventType>
    <media>
      <udp>v=0  o=user1 53655765 2353687637 IN IP 4 *********  s=Sip Call  c=IN IP4 *********  t=0 0  m=audio 6000 RTP/AVP 0 101  a=rtpmap:0 PCMU/8000  a=rtpmap:101 telephone-event/8000  a=sendrecv  </udp>
      <mediaLabel>_ML_dev_{{IterationCounter}}</mediaLabel>
    </media>
  </LogEvent>
  
  <LogEvent xmlns="http://solacom.com/Logging">
          <timestamp>{{timestamp}}</timestamp>
          <agencyOrElement>{{PSAPName}}-A_A</agencyOrElement>
          <agent>.</agent>
          <callIdentifier>{{CallID}}</callIdentifier>
          <incidentIdentifier>{{IncidentID}}</incidentIdentifier>
          <eventType>HELDquery</eventType>
          <heldQuery>
                  <mediaLabel>_ML_dev_{{IterationCounter}}</mediaLabel>
                  <heldDomain>**********</heldDomain>
                  <heldPurpose>Dereferencing</heldPurpose>
                  <held-uri>tel:8195551002</held-uri>
          </heldQuery>
  </LogEvent>
  
  <LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>{{timestamp}}</timestamp>
        <agencyOrElement>{{PSAPName}}-A_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>{{CallID}}</callIdentifier>
        <incidentIdentifier>{{IncidentID}}</incidentIdentifier>
        <eventType>HELDresponse</eventType>
        <heldResponse>
                <mediaLabel>_ML_dev_{{IterationCounter}}</mediaLabel>
                <heldDomain>**********</heldDomain>
                <responseCode>200</responseCode>
                <held><locationResponse xmlns="urn:ietf:params:xml:ns:geopriv:held" xmlns:gml="http://www.opengis.net/gml" xmlns:ns4="urn:ietf:params:xml:ns:pidf" xmlns:ns5="urn:ietf:params:xml:ns:pidf:data-model" xmlns:ns6="urn:ietf:params:xml:ns:pidf:geopriv10" xmlns:ca="urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr" xmlns:ns7="urn:ietf:params:xml:ns:pidf:geopriv10:basicPolicy" xmlns:ns8="http://www.opengis.net/pidflo/1.0" xmlns:ns9="urn:ietf:params:xml:ns:geopriv:held:id"><locationUriSet expires="2023-02-06T07:55:42" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><locationUri>https://ls.example.com:9768/357yc6s64ceyoiuy5ax3o</locationUri><locationUri>sip:<EMAIL>:</locationUri></locationUriSet><presence entity="pres:<EMAIL>" xmlns="urn:ietf:params:xml:ns:pidf" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><tuple id="lisLocation"><status><geopriv xmlns="urn:ietf:params:xml:ns:pidf:geopriv10"><location-info><ca:civicAddress xmlns:ca="urn:ietf:params:xml:ns:pidf:geopriv10:civicAddr" xml:lang="en-us"><ca:country>TX</ca:country><ca:A1>TX</ca:A1><ca:A2>dddd</ca:A2><ca:A3>Wollongon</ca:A3><ca:A4>Gwynneville</ca:A4><ca:A5>testCounty</ca:A5><ca:STS>JeanProulx s  curit  </ca:STS><ca:LMK>University of Wollongong</ca:LMK><ca:NAM>Andrew Corporation</ca:NAM><ca:FLR>2</ca:FLR><ca:POBOX>U40</ca:POBOX><ca:SEAT>WS-183</ca:SEAT></ca:civicAddress></location-info><usage-rules><retransmission-allowed>true</retransmission-allowed><retention-expiry>2023-02-06T07:55:42</retention-expiry></usage-rules><method>Wiremap</method></geopriv></status><timestamp>2023-02-06T07:55:42</timestamp></tuple></presence></locationResponse></held>
        </heldResponse>
  </LogEvent>
  
  <LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>{{timestamp}}</timestamp>
        <agencyOrElement>{{PSAPName}}</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>{{CallID}}</callIdentifier>
        <incidentIdentifier>{{IncidentID}}</incidentIdentifier>
        <eventType>Route</eventType>
        <route>
                <uri>9991504</uri> 
                <rule>rule #157</rule>
                <reason>normal</reason>
                <mediaLabel>.</mediaLabel>
                <attempt>1</attempt>
                <priority>-1</priority>
                <ani>8195551002</ani>
                <aniDomain>*********</aniDomain>
                <dnis>0009991504</dnis>
                <pani>8195551002</pani>
                <esrn>0009991504</esrn>
                <callerName>Preserve Call Test</callerName>
                <aniTranslated>8195551002</aniTranslated>
                <dnisTranslated>9991504</dnisTranslated>
                <callerNameTranslated>Preserve Call Test</callerNameTranslated>
        </route>
  </LogEvent>
  
  <LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>{{timestamp}}</timestamp>
        <agencyOrElement>{{PSAPName}}</agencyOrElement>
        <agent>operatorAgent01</agent>
        <callIdentifier>{{CallID}}</callIdentifier>
        <incidentIdentifier>{{IncidentID}}</incidentIdentifier>
        <eventType>Route</eventType>
        <route>
                <uri>7775553004</uri> 
                <rule>rule #67</rule>
                <reason>normal</reason>
                <mediaLabel>_ML_dev_2_{{IterationCounter}}</mediaLabel>
                <attempt>1</attempt>
                <priority>-1</priority>
                <ani>8195551002</ani>
                <aniDomain>*********</aniDomain>
                <dnis>7775553004</dnis>
                <pani>8195551002</pani>
                <esrn>0009991504</esrn>
                <callerName>Preserve Call Test</callerName>
                <aniTranslated>8195551002</aniTranslated>
                <dnisTranslated>7775553004</dnisTranslated>
                <callerNameTranslated>Preserve Call Test</callerNameTranslated>
        </route>
  </LogEvent>
  
  <LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>{{timestamp}}</timestamp>
        <agencyOrElement>{{PSAPName}}</agencyOrElement>
        <agent>operatorAgent01</agent>
        <callIdentifier>{{CallID}}</callIdentifier>
        <incidentIdentifier>{{IncidentID}}</incidentIdentifier>
        <eventType>Media</eventType>
        <media>
            <udp>.</udp>
            <mediaLabel>_ML_dev_2_{{IterationCounter}}</mediaLabel>
        </media>
  </LogEvent>

  <LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>{{timestamp}}</timestamp>
        <agencyOrElement>{{PSAPName}}-A_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>{{CallID}}</callIdentifier>
        <incidentIdentifier>{{IncidentID}}</incidentIdentifier>
        <eventType>Answer</eventType>
        <answer>
                <mediaLabel>_ML_dev_{{IterationCounter}}</mediaLabel>
                <uri>tel:+8195551002</uri>
                <agentRole>.</agentRole>
                <tenantGroup>.</tenantGroup>
                <operatorId>-1</operatorId>
                <workstation>.</workstation>
        </answer>
  </LogEvent>

  <LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>{{timestamp}}</timestamp>
        <agencyOrElement>{{PSAPName}}</agencyOrElement>
        <agent>operatorAgent01</agent>
        <callIdentifier>{{CallID}}</callIdentifier>
        <incidentIdentifier>{{IncidentID}}</incidentIdentifier>
        <eventType>EndMedia</eventType>
        <endMedia>
            <mediaLabel>_ML_dev_2_{{IterationCounter}}</mediaLabel>
            <responseCode>41</responseCode>
            <disconnectReason></disconnectReason>
            <voiceQOS>
                    <mediaIpSourceAddr>.</mediaIpSourceAddr> 
                    <mediaIpDestAddr>.</mediaIpDestAddr>
                    <mediaUdpRtpSourcePort>-1</mediaUdpRtpSourcePort> 
                    <mediaUdpRtpDestPort>-1</mediaUdpRtpDestPort>
                    <mediaNumOfIpPktRxed>-1</mediaNumOfIpPktRxed> 
                    <mediaNumOfIpPktTxed>-1</mediaNumOfIpPktTxed>
                    <mediaNumOfIpErroredPktRxed>-1</mediaNumOfIpErroredPktRxed>
                    <mediaNumOfRtpPktRxed>-1</mediaNumOfRtpPktRxed>
                    <mediaNumOfRtpPktTxed>-1</mediaNumOfRtpPktTxed>
                    <mediaNumOfRtpPktLost>-1</mediaNumOfRtpPktLost>
                    <mediaNumOfRtpPktDiscarded>-1</mediaNumOfRtpPktDiscarded>
                    <mediaRtpJitter>-1</mediaRtpJitter>
                    <mediaRtpLatency>-1</mediaRtpLatency>
                    <mediaNumOfRtcpPktRxed>-1</mediaNumOfRtcpPktRxed>
                    <mediaNumOfRtcpPktTxed>-1</mediaNumOfRtcpPktTxed> 
                    <mediaFarEndPacketLostPercentage>-1</mediaFarEndPacketLostPercentage>
                    <mediaFarEndCumulativePacketLost>-1</mediaFarEndCumulativePacketLost> 
                    <mediaFarEndInterarrivalJitter>-1</mediaFarEndInterarrivalJitter> 
            </voiceQOS>
        </endMedia>
  </LogEvent>
  
  <LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>{{timestamp}}</timestamp>
        <agencyOrElement>{{PSAPName}}-A_A</agencyOrElement>
        <agent>.</agent>
        <callIdentifier>{{CallID}}</callIdentifier>
        <incidentIdentifier>{{IncidentID}}</incidentIdentifier>
        <eventType>EndMedia</eventType>
        <endMedia>
                <mediaLabel>_ML_dev_{{IterationCounter}}</mediaLabel>
                <responseCode>41</responseCode>
                <disconnectReason></disconnectReason>
                <voiceQOS>
                        <mediaIpSourceAddr>.</mediaIpSourceAddr> 
                        <mediaIpDestAddr>.</mediaIpDestAddr>
                        <mediaUdpRtpSourcePort>-1</mediaUdpRtpSourcePort> 
                        <mediaUdpRtpDestPort>-1</mediaUdpRtpDestPort>
                        <mediaNumOfIpPktRxed>-1</mediaNumOfIpPktRxed> 
                        <mediaNumOfIpPktTxed>-1</mediaNumOfIpPktTxed>
                        <mediaNumOfIpErroredPktRxed>-1</mediaNumOfIpErroredPktRxed>
                        <mediaNumOfRtpPktRxed>-1</mediaNumOfRtpPktRxed>
                        <mediaNumOfRtpPktTxed>-1</mediaNumOfRtpPktTxed>
                        <mediaNumOfRtpPktLost>-1</mediaNumOfRtpPktLost>
                        <mediaNumOfRtpPktDiscarded>-1</mediaNumOfRtpPktDiscarded>
                        <mediaRtpJitter>-1</mediaRtpJitter>
                        <mediaRtpLatency>-1</mediaRtpLatency>
                        <mediaNumOfRtcpPktRxed>-1</mediaNumOfRtcpPktRxed>
                        <mediaNumOfRtcpPktTxed>-1</mediaNumOfRtcpPktTxed> 
                        <mediaFarEndPacketLostPercentage>-1</mediaFarEndPacketLostPercentage>
                        <mediaFarEndCumulativePacketLost>-1</mediaFarEndCumulativePacketLost> 
                        <mediaFarEndInterarrivalJitter>-1</mediaFarEndInterarrivalJitter> 
                </voiceQOS>
        </endMedia>
  </LogEvent>
  
  <LogEvent xmlns="http://solacom.com/Logging">
        <timestamp>{{timestamp}}</timestamp>
        <agencyOrElement>{{PSAPName}}</agencyOrElement>
        <agent>operatorAgent01</agent>
        <callIdentifier>{{CallID}}</callIdentifier>
        <incidentIdentifier>{{IncidentID}}</incidentIdentifier>
        <eventType>Route</eventType>
        <route>
                <uri>7775553004</uri> 
                <rule>rule #67</rule>
                <reason>alternate</reason>
                <mediaLabel>_ML_dev_3_{{IterationCounter}}</mediaLabel>
                <attempt>1</attempt>
                <priority>-1</priority>
                <ani>8195551002</ani>
                <aniDomain>.</aniDomain>
                <dnis>7775553004</dnis>
                <pani>8195551002</pani>
                <esrn>0009991504</esrn>
                <callerName>Preserve Call Test</callerName>
                <aniTranslated>8195551002</aniTranslated>
                <dnisTranslated>7775553004</dnisTranslated>
                <callerNameTranslated>Preserve Call Test</callerNameTranslated>
        </route>
  </LogEvent>
  
  <LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>{{timestamp}}</timestamp>
    <agencyOrElement>{{PSAPName}}</agencyOrElement>
    <agent>operatorAgent01</agent>
    <callIdentifier>{{CallID}}</callIdentifier>
    <incidentIdentifier>{{IncidentID}}</incidentIdentifier>
    <eventType>Media</eventType>
    <media>
            <udp>.</udp>
            <mediaLabel>_ML_dev_3_{{IterationCounter}}</mediaLabel>
    </media>
  </LogEvent>

  <LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>{{timestamp}}</timestamp>
    <agencyOrElement>{{PSAPName}}</agencyOrElement>
    <agent>operatorAgent01</agent>
    <callIdentifier>{{CallID}}</callIdentifier>
    <incidentIdentifier>{{IncidentID}}</incidentIdentifier>
    <eventType>Answer</eventType>
    <answer>
            <mediaLabel>_ML_dev_2_{{IterationCounter}}</mediaLabel>
            <uri>tel:+7775553004</uri>
            <agentRole>Admin</agentRole>
            <tenantGroup>Tenant1</tenantGroup>
            <operatorId>0</operatorId>
            <workstation>PORSCHE-004</workstation>
    </answer>
  </LogEvent>

  <LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>{{timestamp}}</timestamp>
    <agencyOrElement>{{PSAPName}}-A_A</agencyOrElement>
    <agent>.</agent>
    <callIdentifier>{{CallID}}</callIdentifier>
    <incidentIdentifier>{{IncidentID}}</incidentIdentifier>
    <eventType>EndCall</eventType>
    <endCall>
            <responseCode>16</responseCode>
            <callReplaced>No</callReplaced>
    </endCall>
  </LogEvent>
  
  <LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>{{timestamp}}</timestamp>
    <agencyOrElement>{{PSAPName}}</agencyOrElement>
    <agent>operatorAgent01</agent>
    <callIdentifier>{{CallID}}</callIdentifier>
    <incidentIdentifier>{{IncidentID}}</incidentIdentifier>
    <eventType>EndMedia</eventType>
    <endMedia>
        <mediaLabel>_ML_dev_3_{{IterationCounter}}</mediaLabel>
        <responseCode>16</responseCode>
        <disconnectReason></disconnectReason>
        <voiceQOS>
                <mediaIpSourceAddr>.</mediaIpSourceAddr> 
                <mediaIpDestAddr>.</mediaIpDestAddr>
                <mediaUdpRtpSourcePort>-1</mediaUdpRtpSourcePort> 
                <mediaUdpRtpDestPort>-1</mediaUdpRtpDestPort>
                <mediaNumOfIpPktRxed>-1</mediaNumOfIpPktRxed> 
                <mediaNumOfIpPktTxed>-1</mediaNumOfIpPktTxed>
                <mediaNumOfIpErroredPktRxed>-1</mediaNumOfIpErroredPktRxed>
                <mediaNumOfRtpPktRxed>-1</mediaNumOfRtpPktRxed>
                <mediaNumOfRtpPktTxed>-1</mediaNumOfRtpPktTxed>
                <mediaNumOfRtpPktLost>-1</mediaNumOfRtpPktLost>
                <mediaNumOfRtpPktDiscarded>-1</mediaNumOfRtpPktDiscarded>
                <mediaRtpJitter>-1</mediaRtpJitter>
                <mediaRtpLatency>-1</mediaRtpLatency>
                <mediaNumOfRtcpPktRxed>-1</mediaNumOfRtcpPktRxed>
                <mediaNumOfRtcpPktTxed>-1</mediaNumOfRtcpPktTxed> 
                <mediaFarEndPacketLostPercentage>-1</mediaFarEndPacketLostPercentage>
                <mediaFarEndCumulativePacketLost>-1</mediaFarEndCumulativePacketLost> 
                <mediaFarEndInterarrivalJitter>-1</mediaFarEndInterarrivalJitter> 
        </voiceQOS>
    </endMedia>
  </LogEvent>
  
  <LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>{{timestamp}}</timestamp>
    <agencyOrElement>{{PSAPName}}-A_A</agencyOrElement>
    <agent>operatorAgent01</agent>
    <callIdentifier>{{CallID}}</callIdentifier>
    <incidentIdentifier>{{IncidentID}}</incidentIdentifier>
    <eventType>CDRtype1</eventType>
    <cdrType1>
            <startTime>2023-02-06T12:31:14.226Z</startTime>
            <operatorId>0</operatorId>
            <ani>8195551002</ani>
            <presentedTime>2023-02-06T12:31:14.541Z</presentedTime>
            <answeredTime>2023-02-06T12:31:16.159Z</answeredTime>
            <jobNumber>.</jobNumber>
            <transferTime></transferTime>
            <transferAnswerTime></transferAnswerTime>
            <disassociatedTime></disassociatedTime>
            <transferTargetType>.</transferTargetType>
            <transferTargetName>.</transferTargetName>
            <transferTarget>.</transferTarget>
            <disconnectReason>.</disconnectReason>
            <ivrOutcome>.</ivrOutcome>
            <externalTransferAttempts>0</externalTransferAttempts>
            <dnis>779</dnis>
            <endTime>2023-02-06T12:42:17.555Z</endTime>
    </cdrType1>
  </LogEvent>

</LogEvents>