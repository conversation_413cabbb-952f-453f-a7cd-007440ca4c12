# ---------------------------------------------------------------------------------------------------------------------
# General Variables
# ---------------------------------------------------------------------------------------------------------------------


aws_region = {
  PROD  = "us-east-1"
  QA    = "us-west-1"
}

# ---------------------------------------------------------------------------------------------------------------------
# Elastic Beanstalk
# ---------------------------------------------------------------------------------------------------------------------


ebs_application_name        = "CollectorAPI"
ebs_application_description = "deployment for Collector API"



# ---------------------------------------------------------------------------------------------------------------------
# VPC
# ---------------------------------------------------------------------------------------------------------------------

db_instance_identifier            	= "mis-free-tier"
db_instance_name                  	= "MIS" # Make sure that database name is capitalized, otherwise RDS will try to recreate RDS instance every time
db_instance_username              	= "admin"
db_instance_password              	= "Welcome123!"
db_instance_parameter_group_name  	= "collectorapi.mariadb.10.3.13"
db_instance_tag_name              	= "collectorapi"
db_instance_vpc_security_group_ids 	= "sg-1a0b3766"
