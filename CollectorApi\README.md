# CollectorAPI

CollectorAPI is a .net core app that processes business logic upon i3logs and pushes the resulting data into Elasticsearch


## Configuration

Configuration is contained in the **appsetting.json** file.

Key configuration includes:

* ```awsBucketSettings```
    * configuration of AWS s3 credentials and properities 

* ```Database```
    * configuration of Aurora database setting which are used to store enriched event log data

* ```elasticsearchSettings```
    * configuration of additional properities that are attached to any logging events for easier identification of source of the log

* ```eventFileLocations```
    *  configuration of s3 bucket to store daily event logs

* ```Logging.elasticsearchsettings```
    * configuration of the elasticsearch instance any logging information will be written to    

* ```userKeyData```
    * configuration of the API credentials

* ```Version```
    * Default is Build infromation - can be any string to designate a version that is running.


## Encryption of Secrets

* To Encrypt
    * Leverage the POST API end point with the text as the payload to create the encrypted text. 
    * Returned infromation is the resulting hashed information to copy in the given field.
    > ie. ```curl -X POST /encrypt -d SecretText```


    Encryptable Fields

    Any of these fields can be encrypted in the **appsetting.json**, but none are required.
    * ```awsBucketSettings```
        *  ```accessId```
        *  ```bucketName```
        *  ```regionName```
        *  ```secretKey```

    * ```Database```
        *  ```mysql```    

    * ```elasticsearchSettings```
        *  ```password```
        *  ```userName```
        *  ```url```

    * ```eventFileLocations```
        *  ```bucketPath```

    * ```Logging.elasticsearchsettings```
        *  ```password```
        *  ```userName```
        *  ```url```

    * ```userKeyData```
        *  ```hashedApiKey```
        *  ```rawApiKey```
        *  ```userId```


## How to run

Visual studios:

After pressing the build button, the application will automatically start once connections are made to the local MySQL or Maria DB, Elasticsearch and RabbitMQ endpoints.


Elastic Beanstalk:

Push the docker image to AWS ECR repository , upload and deploy into the correct AWS Elastic Beanstalk environment. The application will automatically start once connections are made to Aurora, Elasticsearch and RabbitMQ cluster endpoints.

References:

- Pushing a Docker image to AWS Elastic Container Registery: https://docs.aws.amazon.com/AmazonECR/latest/userguide/docker-push-ecr-image.html
- Deploying applications to Elastic Beanstalk environments: https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/using-features.deploy-existing-version.html
