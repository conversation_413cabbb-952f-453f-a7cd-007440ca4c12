﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Solacom.InfoHub.EventReceiver.Entities;
using Solacom.InfoHub.EventReceiver.MariaDb.Context.Models;

namespace Solacom.InfoHub.EventReceiver.MariaDb.Context.Interfaces
{
    public interface IUnitOfWork : IDisposable
    {
        Task<Eventhash> GetHashEvent(string hashedEvent, string clientCode);
        Task UpdateHashEvent(Eventhash eventHash);
        Task DeleteHashEvent(string hashedContent, string clientCode);
        Task UpdateAgentSession(AgentSessionRecord agentSessionRecord, string clientCode);
        Task<AgentSessionRecord> GetAgentSession(string mediaLabel, string clientCode);
        Task<int> AddEvent(string clientCode, EventLog eventLog);
        Task<(Dictionary<string, int>, int, int)> GetEventTypeCount(string callIdentifier, string clientId, int maxEventId);
        Task<IList<CallInstance>> GetExpiredEventsCallId(int olderthanHours);
        Task<long> SetEventProcessed(string callIdentifier, string clientId, int maxEventId);
        Task<long> SetErrorEventState(string callIdentifier, string clientId, int maxEventId);
        Task<long> SetExpiredEventState(string callIdentifier, string clientId, int maxEventId);

        Task SetProcessQueue(string callIdentifier, string clientId);
        Task<IList<CallInstance>> GetProcessQueueOlderThan(int olderthanMinutes);
        Task DeleteProcessQueue(string callIdentifier, string clientId);

        Task LogError(string clientCode, string callId);
        Task DeleteErrorEvent(string data);
        Task<List<string>> GetErrorEvents();
        Task<List<EventLog>> GetEventsForProcessing(string callIdentifier, string clientId, int maxEventId);

        Task CleanUpTablesHashEvents(int olderThan);
        Task CleanUpTablesEvents(int olderThan);
        Task CleanUpTablesAgentSession(int olderThan);
    }

    public interface IDataProvider
    {
        Task<Eventhash> GetHashEvent(string hashedEvent, string clientCode);
        Task UpdateHashEvent(Eventhash eventHash);
        Task DeleteHashEvent(string hashedContent, string clientCode);
        Task UpdateAgentSession(AgentSessionRecord agentSessionRecord, string clientCode);
        Task<AgentSessionRecord> GetAgentSession(string mediaLabel, string clientCode);
        Task<int> AddEvent(string clientCode, EventLog eventLog);
        Task<(Dictionary<string, int>, int, int)> GetEventTypeCount(string callIdentifier, string clientId, int maxEventId);
        Task<IList<CallInstance>> GetExpiredEventsCallId(int olderthanHours);
        Task<long> SetEventProcessed(string callIdentifier, string clientId, int maxEventId);
        Task<long> SetErrorEventState(string callIdentifier, string clientId, int maxEventId);
        Task<long> SetExpiredEventState(string callIdentifier, string clientId, int maxEventId);
        Task SetProcessQueue(string callIdentifier, string clientId);
        Task<IList<CallInstance>> GetProcessQueueOlderThan(int olderthanMinutes);
        Task DeleteProcessQueue(string callIdentifier, string clientId);
        Task LogError(string clientCode, string callId);
        Task<List<string>> GetErrorEvents();
        Task<List<EventLog>> GetEventsForProcessing(string callIdentifier, string clientId, int maxEventId);
        Task DeleteErrorEvent(string data);
        
        Task CleanUpTablesHashEvents(int olderThan);
        Task CleanUpTablesEvents(int olderThan);
        Task CleanUpTablesAgentSession(int olderThan);

    }
}