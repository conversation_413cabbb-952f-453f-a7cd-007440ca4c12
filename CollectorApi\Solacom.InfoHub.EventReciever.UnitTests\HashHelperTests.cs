using Microsoft.VisualStudio.TestTools.UnitTesting;
using Solacom.InfoHub.EventReceiver.BusinessLogic;

namespace Solacom.InfoHub.EventReciever.UnitTests
{
    [TestClass]
    public class HashHelperTests
    {
        private HashingHelper _helper;

        [TestInitialize]
        public void Init()
        {
            _helper = new HashingHelper();
        }

        [TestMethod]
        public void CanGenerateHash()
        {
            var hashed = _helper.Hash("Test");
            Assert.IsNotNull(hashed);
        }

        [TestMethod]
        public void GeneratesSameHashForSameString()
        {
            var hashed1 = _helper.Hash("Test");
            var hashed2 = _helper.Hash("Test");
           
            Assert.AreEqual(hashed1, hashed2);
        }

        [TestMethod]
        public void GeneratesDifferentHashForDifferentStrings()
        {
            var hashed1 = _helper.Hash("Test1");
            var hashed2 = _helper.Hash("Test2");
           
            Assert.AreNotEqual(hashed1, hashed2);
        }
    }
}