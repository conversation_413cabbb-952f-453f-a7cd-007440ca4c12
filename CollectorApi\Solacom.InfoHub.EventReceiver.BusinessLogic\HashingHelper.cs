﻿using System.Security.Cryptography;
using System.Text;

namespace Solacom.InfoHub.EventReceiver.BusinessLogic
{
    public class HashingHelper
    {
        public string Hash(string input)
        {
            using (var sha256 = SHA256.Create())
            {
                var bytes = Encoding.ASCII.GetBytes(input);
                byte[] hashValue = sha256.ComputeHash(bytes);

                return Encoding.ASCII.GetString(hashValue);
            }
        }
    }
}