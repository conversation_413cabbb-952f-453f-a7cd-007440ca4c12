﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using Solacom.InfoHub.EventReceiver.Web.API.Controllers;
using Solacom.InfoHub.EventReceiver.Web.API.Security;

namespace Solacom.InfoHub.EventReciever.UnitTests
{
    [TestClass]
    public class UserKeyGeneratorTests
    {
        private UserKeyManager _manager;

        [TestInitialize]
        public void Init()
        {
            _manager = new UserKeyManager();
        }


        [TestMethod]
        public void Can_Generate_UserKeys()
        {
            var result = _manager.Generate();

            Assert.IsNotNull(result.UserId);
        }

        [TestMethod]
        public void Can_Match_Password()
        {
            var result = _manager.Generate();

            Assert.IsNotNull(result.UserId);

            var isMatch = _manager.Match(result);
            Assert.IsTrue(isMatch);
        }

        [TestMethod]
        public void Can_Not_Match_Wrong_Password()
        {
            var result = _manager.Generate();

            Assert.IsNotNull(result.UserId);

            result.RawApiKey = "test";

            var isMatch = _manager.Match(result);
            Assert.IsFalse(isMatch);
        }
    }
}