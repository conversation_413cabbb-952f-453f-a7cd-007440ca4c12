{"info": {"_postman_id": "8e4c8b9c-021e-4bc3-8eae-7f0b499198f5", "name": "InfohubAPI", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "StartCall", "event": [{"listen": "test", "script": {"id": "30798716-156a-486d-94f4-1f22085d4a51", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"StartCall\",\r\n        \"startCall\": {\r\n            \"header\": \"abc \",\r\n            \"location\": \".\",\r\n            \"mediaLabel\": \"_ML_168487DF5A8C0011F973@Godzilla\",\r\n            \"incomingCallPolicy\": \"2619_Traffic_Held_by_Ref\",\r\n            \"callType\": \"E911\",\r\n            \"signallingType\": \"VOIP\",\r\n            \"circuit\": \"15/12/00/0382\",\r\n            \"circuitId\": \"64487806\",\r\n            \"trunkGroupId\": \"360\",\r\n            \"ani\": \"5559990004\",\r\n            \"aniDomain\": \"************\",\r\n            \"dnis\": \"721\",\r\n            \"dnisDomain\": null,\r\n            \"pani\": \"5559990004\",\r\n            \"esrn\": \".\",\r\n            \"callerName\": \"State\"\r\n        }\r\n    }\r\n    "}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "StartCall ES", "event": [{"listen": "test", "script": {"id": "30798716-156a-486d-94f4-1f22085d4a51", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"StartCall\",\r\n        \"startCall\": {\r\n            \"header\": \"abc \",\r\n            \"location\": \".\",\r\n            \"mediaLabel\": \"_ML_168487DF5A8C0011F973@Godzilla\",\r\n            \"incomingCallPolicy\": \"2619_Traffic_Held_by_Ref\",\r\n            \"callType\": \"E911\",\r\n            \"signallingType\": \"VOIP\",\r\n            \"circuit\": \"15/12/00/0382\",\r\n            \"circuitId\": \"64487806\",\r\n            \"trunkGroupId\": \"360\",\r\n            \"ani\": \"5559990004\",\r\n            \"aniDomain\": \"************\",\r\n            \"dnis\": \"721\",\r\n            \"dnisDomain\": null,\r\n            \"pani\": \"5559990004\",\r\n            \"esrn\": \".\",\r\n            \"callerName\": \"State\"\r\n        }\r\n    }\r\n    "}, "url": {"raw": "http://{{environment_url}}/events/_doc/1", "protocol": "http", "host": ["{{environment_url}}"], "path": ["events", "_doc", "1"]}}, "response": []}, {"name": "StartCall XML", "event": [{"listen": "test", "script": {"id": "e12bf576-f50d-48ce-88eb-f547781d2241", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>StartCall</eventType>\n    <startCall>\n        <header>\n            <![CDATA[INVITE sip:721@***********4;transport=tcp SIP/2.0  Via: SIP/2.0/TCP ************;branch=z9hG4bK11da.8781d746.0  To: sip:721@***********4;transport=tcp  From: 'LaFleur' <sip:5559990004@***********:5060>;tag=6c9195496254002150d5987b8e17da44-c08a  CSeq: 2 INVITE  Call-ID: B2B.435.6493349  Max-Forwards: 70  Content-Length: 152  User-Agent: OpenSIPS (1.8.1-notls (x86_64/linux))  Content-Type: application/sdp  Geolocation: <https://***********/LIS-ECRF%20Services/i3service.asmx/held>  Initial-CallID: 325208-6920@***********  Contact: <sip:************:5060;transport=tcp>]]>\n        </header>\n        <location>.</location>\n        <mediaLabel>_ML_168487DF5A8C0011F973@Godzilla</mediaLabel>\n        <incomingCallPolicy>2619_Traffic_Held_by_Ref</incomingCallPolicy>\n        <callType>E911</callType>\n        <signallingType>VOIP</signallingType>\n        <circuit>15/12/00/0382</circuit>\n        <circuitId>64487806</circuitId>\n        <trunkGroupId>360</trunkGroupId>\n        <ani>5559990004</ani>\n        <aniDomain>************</aniDomain>\n        <dnis>721</dnis>\n        <pani>5559990004</pani>\n        <esrn>.</esrn>\n        <callerName>State</callerName>\n    </startCall>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "StartCall No CDATA XML", "event": [{"listen": "test", "script": {"id": "7ad1d6ef-c81b-4146-8d1b-a3880ee0e43c", "exec": ["pm.test(\"Status code is 400\", function () {", "    pm.response.to.have.status(400);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>StartCall</eventType>\n    <startCall>\n        <header><[INVITE sip:721@***********4;transport=tcp SIP/2.0  Via: SIP/2.0/TCP ************;branch=z9hG4bK11da.8781d746.0  To: sip:721@***********4;transport=tcp  From: 'LaFleur' \n            <sip:5559990004@***********:5060>;tag=6c9195496254002150d5987b8e17da44-c08a  CSeq: 2 INVITE  Call-ID: B2B.435.6493349  Max-Forwards: 70  Content-Length: 152  User-Agent: OpenSIPS (1.8.1-notls (x86_64/linux))  Content-Type: application/sdp  Geolocation: \n                <https://***********/LIS-ECRF%20Services/i3service.asmx/held>  Initial-CallID: 325208-6920@***********  Contact: <sip:************:5060;transport=tcp>]>\n                </header>\n                <location>.</location>\n                <mediaLabel>_ML_168487DF5A8C0011F973@Godzilla</mediaLabel>\n                <incomingCallPolicy>2619_Traffic_Held_by_Ref</incomingCallPolicy>\n                <callType>E911</callType>\n                <signallingType>VOIP</signallingType>\n                <circuit>15/12/00/0382</circuit>\n                <circuitId>64487806</circuitId>\n                <trunkGroupId>360</trunkGroupId>\n                <ani>5559990004</ani>\n                <aniDomain>************</aniDomain>\n                <dnis>721</dnis>\n                <pani>5559990004</pani>\n                <esrn>.</esrn>\n                <callerName>State</callerName>\n            </startCall>\n        </LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"id": "55b0aee6-3d4c-4a1d-99ab-69b4822ff6a0", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"HELDQuery\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": {\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"heldDomain\": \"URIForHELDServer\",\r\n            \"heldPurpose\": \"InitialLocation\",\r\n            \"helduri\": \"tel:+9201230123\"\r\n        },\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "HeldQuery XML", "event": [{"listen": "test", "script": {"id": "755a3da3-5efc-4670-8396-d9525d524dca", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>HELDQuery</eventType>\n    <heldQuery>\n        <mediaLabel><EMAIL></mediaLabel>\n        <heldDomain>URIForHELDServer</heldDomain>\n        <heldPurpose>InitialLocation</heldPurpose>\n        <held-uri>tel:+9201230123</held-uri>\n    </heldQuery>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Held Response", "event": [{"listen": "test", "script": {"id": "41d2a8c9-f0b9-46a2-92ca-347cf1f1cb3b", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"HELDresponse\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": {\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"heldDomain\": \"URIForHELDServer\",\r\n            \"responseCode\": \"200\",\r\n            \"held\": \"XML document as per RFC5985\"\r\n        },\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n   \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Held Response XML", "event": [{"listen": "test", "script": {"id": "416c665d-74b5-4d75-b120-578c9fa1792e", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>HELDresponse</eventType>\n    <heldResponse>\n        <mediaLabel><EMAIL></mediaLabel>\n        <heldDomain>URIForHELDServer</heldDomain>\n        <responseCode>200</responseCode>\n        <held>XML document as per RFC5985</held>\n    </heldResponse>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Media", "event": [{"listen": "test", "script": {"id": "1ff3bd96-a2f5-4899-bbb0-122f7b9eb893", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"Media\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": {\r\n            \"udp\": \"SDP is placed here\",\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"mediaType\": \"Voice\"\r\n        },\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n   \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Media XML", "event": [{"listen": "test", "script": {"id": "aa5b6f04-2878-41de-91b6-19d89ced51f2", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>Media</eventType>\n    <media>\n        <udp>SDP is placed here</udp>\n        <mediaLabel><EMAIL></mediaLabel>\n        <mediaType>Voice</mediaType>\n    </media>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "ECRF Response", "event": [{"listen": "test", "script": {"id": "4de9fcc6-9fbf-4a7a-9d0a-578301134351", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"ECRFResponse\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": {\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"ecrfDomain\": \"URI For ECRF\",\r\n            \"responseCode\": \"200\",\r\n            \"lost\": \"XML body of LoST Response as per RFC 5222\"\r\n        },\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "ECRF Response XML", "event": [{"listen": "test", "script": {"id": "e7af682b-f909-4bed-840c-ac023e9b39d8", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>ECRFResponse</eventType>\n    <ecrfResponse>\n        <mediaLabel><EMAIL></mediaLabel>\n        <ecrfDomain>URI For ECRF</ecrfDomain>\n        <responseCode>200</responseCode>\n        <lost>XML body of LoST Response as per RFC 5222</lost>\n    </ecrfResponse>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "ECRF Query", "event": [{"listen": "test", "script": {"id": "1d7bd4f9-7a09-4a02-ba27-ba92b96ceb24", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \" ECRFquery \",\r\n        \"ecrfQuery\": {\r\n            \"mediaLabel\": \" <EMAIL> \",\r\n            \"ecrfDomain\": \" URIForECRF \",\r\n            \"serviceurn\": \" nena:service: sos \",\r\n            \"ecrfPurpose\": \" routing \",\r\n            \"location\": \" PIDF_LO as per RFC 4119 and RFC 5139 but starting at location-info tag as per NENA ICE8 xsd \"\r\n        },\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "ECRF Query XML", "event": [{"listen": "test", "script": {"id": "2a6b7201-e9ee-4b93-9bc3-01fbdc9119ca", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType> ECRFquery </eventType>\n    <ecrfQuery>\n        <mediaLabel> <EMAIL> </mediaLabel>\n        <ecrfDomain> URIForECRF </ecrfDomain>\n        <service-urn> nena:service: sos </service-urn>\n        <ecrfPurpose> routing </ecrfPurpose>\n        <location> PIDF_LO as per RFC 4119 and RFC 5139 but starting at location-info tag as per NENA ICE8 xsd </location>\n    </ecrfQuery>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "VPC Query", "event": [{"listen": "test", "script": {"id": "6288f745-4088-46d9-bd02-5359c9fc4ea7", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"VPCQuery\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": {\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"vpcDomain\": \"URIForVPC\",\r\n            \"ani\": \"8195551234\",\r\n            \"dnis\": \"911\"\r\n        },\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "VPC Query XML", "event": [{"listen": "test", "script": {"id": "b0a753d8-4e67-4ebf-8f1c-65d2d0063e0d", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>VPCQuery</eventType>\n    <vpcQuery>\n        <mediaLabel><EMAIL></mediaLabel>\n        <vpcDomain>URIForVPC</vpcDomain>\n        <ani>8195551234</ani>\n        <dnis>911</dnis>\n    </vpcQuery>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "VPC Response", "event": [{"listen": "test", "script": {"id": "2e176cf3-1bb8-4ec2-afda-aade2645a81c", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"VPCResponse\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": {\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"vpcDomain\": \"URIForECRF\",\r\n            \"responseCode\": \"200\",\r\n            \"esrn\": \"8197781234\",\r\n            \"esqk\": \"8195551010\",\r\n            \"esn\": \"4500\"\r\n        },\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "VPC Response XML", "event": [{"listen": "test", "script": {"id": "9def97fb-5fb4-48fa-ae32-8c591e92b230", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>VPCResponse</eventType>\n    <vpcResponse>\n        <mediaLabel><EMAIL></mediaLabel>\n        <vpcDomain>URIForECRF</vpcDomain>\n        <responseCode>200</responseCode>\n        <esrn>8197781234</esrn>\n        <esqk>8195551010</esqk>\n        <esn>4500</esn>\n    </vpcResponse>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "SRDB Query", "event": [{"listen": "test", "script": {"id": "d8a6596c-19cb-4881-a4e8-3ba64640af64", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"SRDBQuery\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": {\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"ani\": \"8197781234\"\r\n        },\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n   \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "SRDB Query XML", "event": [{"listen": "test", "script": {"id": "7639951d-34d4-4f4e-ad14-8e05335b7291", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>SRDBQuery</eventType>\n    <srdbQuery>\n        <mediaLabel><EMAIL></mediaLabel>\n        <ani>8197781234</ani>\n    </srdbQuery>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "SRDB Response", "event": [{"listen": "test", "script": {"id": "72ee9c59-7684-4c47-a831-77111a244e4b", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"SRDBResponse\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": {\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"responseCode\": \"Success\",\r\n            \"esn\": \"4123\"\r\n        },\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "SRDB Response XML", "event": [{"listen": "test", "script": {"id": "c11dc4c1-03bb-4fa3-a9e9-b587455959f3", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>SRDBResponse</eventType>\n    <srdbResponse>\n        <mediaLabel><EMAIL></mediaLabel>\n        <responseCode>Success</responseCode>\n        <esn>4123</esn>\n    </srdbResponse>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "ALI Query", "event": [{"listen": "test", "script": {"id": "51ebb7a9-016f-4ba0-8375-c8b8aeb0e68c", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"ALIQuery\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": {\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"aliLink\": \"ATT link 1\",\r\n            \"uri\": \"tel:+8197781234\",\r\n            \"serviceArea\": \"991\",\r\n            \"aliQueryType\": \"AutomaticInitial\"\r\n        },\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "ALI Query XML", "event": [{"listen": "test", "script": {"id": "27c8f7f1-9979-4b12-96a8-55394b4361bb", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>ALIQuery</eventType>\n    <aliQuery>\n        <mediaLabel><EMAIL></mediaLabel>\n        <aliLink>ATT link 1</aliLink>\n        <uri>tel:+8197781234</uri>\n        <serviceArea>991</serviceArea>\n        <aliQueryType>AutomaticInitial</aliQueryType>\n    </aliQuery>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "ALI Response", "event": [{"listen": "test", "script": {"id": "5f24047b-11dd-41f3-8e41-fdc40491f526", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"ALIResponse\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": {\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"aliLink\": \"ATT link 1\",\r\n            \"ali\": \"Raw ALI text\",\r\n            \"aliResponseCode\": \" Data retrieved, both paths operational \"\r\n        },\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "ALI Response XML", "event": [{"listen": "test", "script": {"id": "5f24047b-11dd-41f3-8e41-fdc40491f526", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>ALIResponse</eventType>\n    <aliResponse>\n        <mediaLabel><EMAIL></mediaLabel>\n        <aliLink>ATT link 1</aliLink>\n        <ali>Raw ALI text</ali>\n        <aliResponseCode> Data retrieved, both paths operational </aliResponseCode>\n    </aliResponse>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Route", "event": [{"listen": "test", "script": {"id": "ec2e8502-9b81-4894-8439-e08e929df748", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"Route\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": {\r\n            \"uri\": \"tel:+6432341234\",\r\n            \"rule\": \"CountyXPsapRoute2\",\r\n            \"reason\": \"normal\",\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"attempt\": \"1\",\r\n            \"priority\": \"1\",\r\n            \"ani\": \"9200000003\",\r\n            \"aniDomain\": \"<EMAIL>\",\r\n            \"dnis\": \"6432341234\",\r\n            \"pani\": \"8191230987\",\r\n            \"esrn\": \"8197781234\",\r\n            \"callerName\": \"Joe Smith\",\r\n            \"aniTranslated\": \"9200000003\",\r\n            \"dnisTranslated\": \"6432341234\",\r\n            \"callerNameTranslated\": \"8191230987\"\r\n        },\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Route XML", "event": [{"listen": "test", "script": {"id": "ec2e8502-9b81-4894-8439-e08e929df748", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>Route</eventType>\n    <route>\n        <uri>tel:+6432341234</uri>\n        <rule>CountyXPsapRoute2</rule>\n        <reason>normal</reason>\n        <mediaLabel><EMAIL></mediaLabel>\n        <attempt>1</attempt>\n        <priority>1</priority>\n        <ani>9200000003</ani>\n        <aniDomain><EMAIL></aniDomain>\n        <dnis>6432341234</dnis>\n        <pani>8191230987</pani>\n        <esrn>8197781234</esrn>\n        <callerName><PERSON></callerName>\n        <aniTranslated>9200000003</aniTranslated>\n        <dnisTranslated>6432341234</dnisTranslated>\n        <callerNameTranslated>8191230987</callerNameTranslated>\n    </route>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Answer", "event": [{"listen": "test", "script": {"id": "349d6216-0d89-49a4-adf6-dccea3288abd", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"Answer\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": {\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"uri\": \"tel:+6432341234\",\r\n            \"agentRole\": \"operator\",\r\n            \"tenantGroup\": \"chicago.psap.il.us\",\r\n            \"operatorId\": \"001\",\r\n            \"workstation\": \"PC Host name\"\r\n        },\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Answer XML", "event": [{"listen": "test", "script": {"id": "349d6216-0d89-49a4-adf6-dccea3288abd", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>Answer</eventType>\n    <answer>\n        <mediaLabel><EMAIL></mediaLabel>\n        <uri>tel:+6432341234</uri>\n        <agentRole>operator</agentRole>\n        <tenantGroup>chicago.psap.il.us</tenantGroup>\n        <operatorId>001</operatorId>\n        <workstation>PC Host name</workstation>\n    </answer>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Transfer Call", "event": [{"listen": "test", "script": {"id": "2acb3768-0a67-4910-a9c5-814470b9f4a2", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"TransferCall\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": {\r\n            \"transferTarget\": \"tel:+ 6432341234\",\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"originatorMediaLabel\": \"<EMAIL>\",\r\n            \"rule\": \"CountyXPsapRoute2\",\r\n            \"reason\": \"normal\",\r\n            \"attempt\": \"1\",\r\n            \"priority\": \"1\",\r\n            \"ani\": \"9200000003\",\r\n            \"aniDomain\": \"<EMAIL>\",\r\n            \"dnis\": \"6432341234\",\r\n            \"pani\": \"8191230987\",\r\n            \"callerName\": \"Joe Smith\",\r\n            \"aniTranslated\": \"9200000003\",\r\n            \"dnisTranslated\": \"6432341234\",\r\n            \"callerNameTranslated\": \"Joe Smith\",\r\n            \"method\": \"Direct Access Button\",\r\n            \"targetType\": \"Ring Group\",\r\n            \"targetName\": \"State Patrol\"\r\n        },\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Transfer Call XML", "event": [{"listen": "test", "script": {"id": "2acb3768-0a67-4910-a9c5-814470b9f4a2", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>TransferCall</eventType>\n    <transferCall>\n        <transferTarget>tel:+ 6432341234</transferTarget>\n        <mediaLabel><EMAIL></mediaLabel>\n        <originatorMediaLabel><EMAIL></originatorMediaLabel>\n        <rule>CountyXPsapRoute2</rule>\n        <reason>normal</reason>\n        <attempt>1</attempt>\n        <priority>1</priority>\n        <ani>9200000003</ani>\n        <aniDomain><EMAIL></aniDomain>\n        <dnis>6432341234</dnis>\n        <pani>8191230987</pani>\n        <callerName>Joe Smith</callerName>\n        <aniTranslated>9200000003</aniTranslated>\n        <dnisTranslated>6432341234</dnisTranslated>\n        <callerNameTranslated>Joe Smith</callerNameTranslated>\n        <method>Direct Access Button</method>\n        <targetType>Ring Group</targetType>\n        <targetName>State Patrol</targetName>\n    </transferCall>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Hold", "event": [{"listen": "test", "script": {"id": "871b0a46-4572-4184-bee1-0be20f9659b8", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"Hold\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": {\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"holdType\": \"Normal\"\r\n        },\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Hold XML", "event": [{"listen": "test", "script": {"id": "871b0a46-4572-4184-bee1-0be20f9659b8", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>Hold</eventType>\n    <hold>\n        <mediaLabel><EMAIL></mediaLabel>\n        <holdType>Normal</holdType>\n    </hold>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Hold Retrieved", "event": [{"listen": "test", "script": {"id": "d07368d4-b0e2-4d19-bb3d-b14b2c51aab8", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"HoldRetrieved\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": {\r\n            \"mediaLabel\": \"<EMAIL>\"\r\n        },\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Hold Retrieved XML", "event": [{"listen": "test", "script": {"id": "d07368d4-b0e2-4d19-bb3d-b14b2c51aab8", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>HoldRetrieved</eventType>\n    <holdRetrieved>\n        <mediaLabel><EMAIL></mediaLabel>\n    </holdRetrieved>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"id": "f02341c0-79a9-4099-8064-4fb7c35c5fab", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"MuteOn\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": {\r\n            \"mediaLabel\": \"<EMAIL>\"\r\n        },\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Mute On XML", "event": [{"listen": "test", "script": {"id": "f02341c0-79a9-4099-8064-4fb7c35c5fab", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>MuteOn</eventType>\n    <muteOn>\n        <mediaLabel><EMAIL></mediaLabel>\n    </muteOn>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Mute Off", "event": [{"listen": "test", "script": {"id": "8113a323-06d9-46f9-8f71-b66c94c73114", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"MuteOff\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": {\r\n            \"mediaLabel\": \"<EMAIL>\"\r\n        },\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Mute Off XML", "event": [{"listen": "test", "script": {"id": "8113a323-06d9-46f9-8f71-b66c94c73114", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>MuteOff</eventType>\n    <muteOff>\n        <mediaLabel><EMAIL></mediaLabel>\n    </muteOff>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Privacy On", "event": [{"listen": "test", "script": {"id": "b0272eab-6da0-4df2-9393-3d64c2da4210", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"PrivacyOn\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": {\r\n            \"mediaLabel\": \"<EMAIL>\"\r\n        },\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Privacy On XML", "event": [{"listen": "test", "script": {"id": "b0272eab-6da0-4df2-9393-3d64c2da4210", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>PrivacyOn</eventType>\n    <privacyOn>\n        <mediaLabel><EMAIL></mediaLabel>\n    </privacyOn>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Privacy Off", "event": [{"listen": "test", "script": {"id": "6c3c7443-0c2f-4b9e-88ad-7b6c8d88ca83", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"PrivacyOff\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": {\r\n            \"mediaLabel\": \"<EMAIL>\"\r\n        },\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Privacy Off XML", "event": [{"listen": "test", "script": {"id": "6c3c7443-0c2f-4b9e-88ad-7b6c8d88ca83", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>PrivacyOff</eventType>\n    <privacyOff>\n        <mediaLabel><EMAIL></mediaLabel>\n    </privacyOff>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"id": "dd442c72-a0df-4203-a5fd-0b11df42e131", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"MergeCall\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": {\r\n            \"callIdentifier2\": \"<EMAIL>\",\r\n            \"incidentIdentifier2\": \"<EMAIL>\"\r\n        },\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Merge Call XML", "event": [{"listen": "test", "script": {"id": "dd442c72-a0df-4203-a5fd-0b11df42e131", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>MergeCall</eventType>\n    <mergeCall>\n        <callIdentifier2><EMAIL></callIdentifier2>\n        <incidentIdentifier2><EMAIL></incidentIdentifier2>\n    </mergeCall>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Outbound Call", "event": [{"listen": "test", "script": {"id": "36b2b8e8-ca59-44fd-8fd9-227270492d66", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"OutboundCall\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": {\r\n            \"outboundTarget\": \"tel:+6432341234\",\r\n            \"rule\": \"CountyXPsapRoute2\",\r\n            \"reason\": \"normal\",\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"attempt\": \"1\",\r\n            \"priority\": \"1\",\r\n            \"ani\": \"9200000003\",\r\n            \"aniDomain\": \"<EMAIL>\",\r\n            \"dnis\": \"6432341234\",\r\n            \"pani\": \"8191230987\",\r\n            \"callerName\": \"Joe Smith\",\r\n            \"aniTranslated\": \"9200000003\",\r\n            \"dnisTranslated\": \"6432341234\",\r\n            \"callerNameTranslated\": \"8191230987\",\r\n            \"method\": \"Direct Access Button\",\r\n            \"targetType\": \"Ring Group\",\r\n            \"targetName\": \"IL State PD\"\r\n        },\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Outbound Call XML", "event": [{"listen": "test", "script": {"id": "36b2b8e8-ca59-44fd-8fd9-227270492d66", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>OutboundCall</eventType>\n    <outboundCall>\n        <outboundTarget>tel:+6432341234</outboundTarget>\n        <rule>CountyXPsapRoute2</rule>\n        <reason>normal</reason>\n        <mediaLabel><EMAIL></mediaLabel>\n        <attempt>1</attempt>\n        <priority>1</priority>\n        <ani>9200000003</ani>\n        <aniDomain><EMAIL></aniDomain>\n        <dnis>6432341234</dnis>\n        <pani>8191230987</pani>\n        <callerName><PERSON></callerName>\n        <aniTranslated>9200000003</aniTranslated>\n        <dnisTranslated>6432341234</dnisTranslated>\n        <callerNameTranslated>8191230987</callerNameTranslated>\n        <method>Direct Access Button</method>\n        <targetType>Ring Group</targetType>\n        <targetName>IL State PD</targetName>\n    </outboundCall>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "End Media", "event": [{"listen": "test", "script": {"id": "f0dce1ed-e4f5-40c9-92db-06bc9d36a253", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"EndMedia\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": {\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"responseCode\": \"16\",\r\n            \"disconnectReason\": null,\r\n            \"voiceQOS\": {\r\n                \"mediaIpSourceAddr\": \"***********\",\r\n                \"mediaIpDestAddr\": \"***********\",\r\n                \"mediaUdpRtpSourcePort\": \"5000\",\r\n                \"mediaUdpRtpDestPort\": \"5000\",\r\n                \"mediaNumOfIpPktRxed\": \"1\",\r\n                \"mediaNumOfIpPktTxed\": \"1\",\r\n                \"mediaNumOfIpErroredPktRxed\": \"1\",\r\n                \"mediaNumOfRtpPktRxed\": \"1\",\r\n                \"mediaNumOfRtpPktTxed\": \"1\",\r\n                \"mediaNumOfRtpPktLost\": \"1\",\r\n                \"mediaNumOfRtpPktDiscarded\": \"1\",\r\n                \"mediaRtpJitter\": \"1\",\r\n                \"mediaRtpLatency\": \"1\",\r\n                \"mediaNumOfRtcpPktRxed\": \"1\",\r\n                \"mediaNumOfRtcpPktTxed\": \"1\",\r\n                \"mediaFarEndPacketLostPercentage\": \"1\",\r\n                \"mediaFarEndCumulativePacketLost\": \"1\",\r\n                \"mediaFarEndInterarrivalJitter\": \"1\"\r\n            }\r\n        },\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "End Media XML", "event": [{"listen": "test", "script": {"id": "f0dce1ed-e4f5-40c9-92db-06bc9d36a253", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>EndMedia</eventType>\n    <endMedia>\n        <mediaLabel><EMAIL></mediaLabel>\n        <responseCode>16</responseCode>\n        <diconnectReason>FireEmergency</diconnectReason>\n        <voiceQOS>\n            <mediaIpSourceAddr>***********</mediaIpSourceAddr>\n            <mediaIpDestAddr>***********</mediaIpDestAddr>\n            <mediaUdpRtpSourcePort>5000</mediaUdpRtpSourcePort>\n            <mediaUdpRtpDestPort>5000</mediaUdpRtpDestPort>\n            <mediaNumOfIpPktRxed>1</mediaNumOfIpPktRxed>\n            <mediaNumOfIpPktTxed>1</mediaNumOfIpPktTxed>\n            <mediaNumOfIpErroredPktRxed>1</mediaNumOfIpErroredPktRxed>\n            <mediaNumOfRtpPktRxed>1</mediaNumOfRtpPktRxed>\n            <mediaNumOfRtpPktTxed>1</mediaNumOfRtpPktTxed>\n            <mediaNumOfRtpPktLost>1</mediaNumOfRtpPktLost>\n            <mediaNumOfRtpPktDiscarded>1</mediaNumOfRtpPktDiscarded>\n            <mediaRtpJitter>1</mediaRtpJitter>\n            <mediaRtpLatency>1</mediaRtpLatency>\n            <mediaNumOfRtcpPktRxed>1</mediaNumOfRtcpPktRxed>\n            <mediaNumOfRtcpPktTxed>1</mediaNumOfRtcpPktTxed>\n            <mediaFarEndPacketLostPercentage>1</mediaFarEndPacketLostPercentage>\n            <mediaFarEndCumulativePacketLost>1</mediaFarEndCumulativePacketLost>\n            <mediaFarEndInterarrivalJitter>1</mediaFarEndInterarrivalJitter>\n        </voiceQOS>\n    </endMedia>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "End Call", "event": [{"listen": "test", "script": {"id": "0717c014-64ff-4baf-99db-969253e0c4cc", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"EndCall\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": {\r\n            \"responseCode\": \"16\",\r\n            \"callReplaced\": \"0\"\r\n        },\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "End Call XML", "event": [{"listen": "test", "script": {"id": "0717c014-64ff-4baf-99db-969253e0c4cc", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>EndCall</eventType>\n    <endCall>\n        <responseCode>16</responseCode>\n        <callReplaced>0</callReplaced>\n    </endCall>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"id": "db65c4e0-4adb-459d-be5a-659c1d930253", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"Login\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": {\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"uri\": \"tel:+6432341234\",\r\n            \"agentRole\": \"operator\",\r\n            \"tenantGroup\": \"chicago.psap.il.us\",\r\n            \"operatorId\": \"001\",\r\n            \"workstation\": \"PC Host name\",\r\n            \"deviceName\": \"Headset\",\r\n            \"reason\": \"normal\"\r\n        },\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Logout", "event": [{"listen": "test", "script": {"id": "eb7534f3-fd5f-4c4b-99d4-e68ff73d8e63", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"Logout\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": {\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"uri\": \"tel:+6432341234\",\r\n            \"agentRole\": \"operator\",\r\n            \"tenantGroup\": \"chicago.psap.il.us\",\r\n            \"operatorId\": \"001\",\r\n            \"workstation\": \"PC Host name\",\r\n            \"deviceName\": \"Headset\",\r\n            \"reason\": \"Normal\",\r\n            \"responseCode\": \"16\",\r\n            \"voiceQOS\": {\r\n                \"mediaIpSourceAddr\": \"***********\",\r\n                \"mediaIpDestAddr\": \"***********\",\r\n                \"mediaUdpRtpSourcePort\": \"5000\",\r\n                \"mediaUdpRtpDestPort\": \"5000\",\r\n                \"mediaNumOfIpPktRxed\": \"1\",\r\n                \"mediaNumOfIpPktTxed\": \"1\",\r\n                \"mediaNumOfIpErroredPktRxed\": \"1\",\r\n                \"mediaNumOfRtpPktRxed\": \"1\",\r\n                \"mediaNumOfRtpPktTxed\": \"1\",\r\n                \"mediaNumOfRtpPktLost\": \"1\",\r\n                \"mediaNumOfRtpPktDiscarded\": \"1\",\r\n                \"mediaRtpJitter\": \"1\",\r\n                \"mediaRtpLatency\": \"1\",\r\n                \"mediaNumOfRtcpPktRxed\": \"1\",\r\n                \"mediaNumOfRtcpPktTxed\": \"1\",\r\n                \"mediaFarEndPacketLostPercentage\": \"1\",\r\n                \"mediaFarEndCumulativePacketLost\": \"1\",\r\n                \"mediaFarEndInterarrivalJitter\": \"1\"\r\n            }\r\n        },\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Logout XML", "event": [{"listen": "test", "script": {"id": "eb7534f3-fd5f-4c4b-99d4-e68ff73d8e63", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>Logout</eventType>\n    <logout>\n        <mediaLabel><EMAIL></mediaLabel>\n        <uri>tel:+6432341234</uri>\n        <agentRole>operator</agentRole>\n        <tenantGroup>chicago.psap.il.us</tenantGroup>\n        <operatorId>001</operatorId>\n        <workstation>PC Host name</workstation>\n        <deviceName>Headset</deviceName>\n        <reason>Normal</reason>\n        <responseCode>16</responseCode>\n        <voiceQOS>\n            <mediaIpSourceAddr>***********</mediaIpSourceAddr>\n            <mediaIpDestAddr>***********</mediaIpDestAddr>\n            <mediaUdpRtpSourcePort>5000</mediaUdpRtpSourcePort>\n            <mediaUdpRtpDestPort>5000</mediaUdpRtpDestPort>\n            <mediaNumOfIpPktRxed>1</mediaNumOfIpPktRxed>\n            <mediaNumOfIpPktTxed>1</mediaNumOfIpPktTxed>\n            <mediaNumOfIpErroredPktRxed>1</mediaNumOfIpErroredPktRxed>\n            <mediaNumOfRtpPktRxed>1</mediaNumOfRtpPktRxed>\n            <mediaNumOfRtpPktTxed>1</mediaNumOfRtpPktTxed>\n            <mediaNumOfRtpPktLost>1</mediaNumOfRtpPktLost>\n            <mediaNumOfRtpPktDiscarded>1</mediaNumOfRtpPktDiscarded>\n            <mediaRtpJitter>1</mediaRtpJitter>\n            <mediaRtpLatency>1</mediaRtpLatency>\n            <mediaNumOfRtcpPktRxed>1</mediaNumOfRtcpPktRxed>\n            <mediaNumOfRtcpPktTxed>1</mediaNumOfRtcpPktTxed>\n            <mediaFarEndPacketLostPercentage>1</mediaFarEndPacketLostPercentage>\n            <mediaFarEndCumulativePacketLost>1</mediaFarEndCumulativePacketLost>\n            <mediaFarEndInterarrivalJitter>1</mediaFarEndInterarrivalJitter>\n        </voiceQOS>\n    </logout>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Busied Out", "event": [{"listen": "test", "script": {"id": "7f2b636b-5ec4-444a-8a56-806ed1bb8dad", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"AgentBusiedOut\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": {\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"uri\": \"tel:+6432341234\",\r\n            \"agentRole\": \"operator\",\r\n            \"tenantGroup\": \"chicago.psap.il.us\",\r\n            \"operatorId\": \"001\",\r\n            \"workstation\": \"PC Host name\",\r\n            \"busiedOutAction\": \"Manual\"\r\n        },\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Busied Out XML", "event": [{"listen": "test", "script": {"id": "7f2b636b-5ec4-444a-8a56-806ed1bb8dad", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>AgentBusiedOut</eventType>\n    <busiedOut>\n        <mediaLabel><EMAIL></mediaLabel>\n        <uri>tel:+6432341234</uri>\n        <agentRole>operator</agentRole>\n        <tenantGroup>chicago.psap.il.us</tenantGroup>\n        <operatorId>001</operatorId>\n        <workstation>PC Host name</workstation>\n        <busiedOutAction>Manual</busiedOutAction>\n    </busiedOut>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Agent Available", "event": [{"listen": "test", "script": {"id": "8491ac28-760e-4a76-9c45-379849299acf", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"AgentAvailable\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": {\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"uri\": \"tel:+6432341234\",\r\n            \"agentRole\": \"operator\",\r\n            \"tenantGroup\": \"chicago.psap.il.us\",\r\n            \"operatorId\": \"001\",\r\n            \"workstation\": \"PC Host name\",\r\n            \"busiedOutAction\": \"Manual\"\r\n        },\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n   \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Agent Available XML", "event": [{"listen": "test", "script": {"id": "8491ac28-760e-4a76-9c45-379849299acf", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>AgentAvailable</eventType>\n    <agentAvailable>\n        <mediaLabel><EMAIL></mediaLabel>\n        <uri>tel:+6432341234</uri>\n        <agentRole>operator</agentRole>\n        <tenantGroup>chicago.psap.il.us</tenantGroup>\n        <operatorId>001</operatorId>\n        <workstation>PC Host name</workstation>\n        <busiedOutAction>Manual</busiedOutAction>\n    </agentAvailable>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "ACD Login", "event": [{"listen": "test", "script": {"id": "e80ac9d0-2502-45ab-ac42-97c627324496", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"ACDLogin\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": {\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"agentUri\": \"tel:+6432341234\",\r\n            \"agentRole\": \"operator\",\r\n            \"tenantGroup\": \"chicago.psap.il.us\",\r\n            \"operatorId\": \"001\",\r\n            \"workstation\": \"PC Host name\",\r\n            \"deviceName\": \"Headset\",\r\n            \"ringGroupName\": \"911 Queue\",\r\n            \"ringGroupUri\": \"tel:+6432341234\"\r\n        },\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "ACD Login XML", "event": [{"listen": "test", "script": {"id": "e80ac9d0-2502-45ab-ac42-97c627324496", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>ACDLogin</eventType>\n    <acdLogin>\n        <mediaLabel><EMAIL></mediaLabel>\n        <agentUri>tel:+6432341234</agentUri>\n        <agentRole>operator</agentRole>\n        <tenantGroup>chicago.psap.il.us</tenantGroup>\n        <operatorId>001</operatorId>\n        <workstation>PC Host name</workstation>\n        <deviceName>Headset</deviceName>\n        <ringGroupName>911 Queue</ringGroupName>\n        <ringGroupUri>tel:+6432341234</ringGroupUri>\n    </acdLogin>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "ACD Logout", "event": [{"listen": "test", "script": {"id": "cde4ad79-5ae3-4982-93be-b95f8b60e2d2", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"ACDLogout\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": {\r\n            \"mediaLabel\": \"<EMAIL>\",\r\n            \"agentUri\": \"tel:+6432341234\",\r\n            \"agentRole\": \"operator\",\r\n            \"tenantGroup\": \"chicago.psap.il.us\",\r\n            \"operatorId\": \"001\",\r\n            \"workstation\": \"PC Host name\",\r\n            \"deviceName\": \"Headset\",\r\n            \"ringGroupName\": \"911 Queue\",\r\n            \"ringGroupUri\": \"tel:+6432341234\"\r\n        },\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "ACD Logout XML", "event": [{"listen": "test", "script": {"id": "cde4ad79-5ae3-4982-93be-b95f8b60e2d2", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>ACDLogout</eventType>\n    <acdLogout>\n        <mediaLabel><EMAIL></mediaLabel>\n        <agentUri>tel:+6432341234</agentUri>\n        <agentRole>operator</agentRole>\n        <tenantGroup>chicago.psap.il.us</tenantGroup>\n        <operatorId>001</operatorId>\n        <workstation>PC Host name</workstation>\n        <deviceName>Headset</deviceName>\n        <ringGroupName>911 Queue</ringGroupName>\n        <ringGroupUri>tel:+6432341234</ringGroupUri>\n    </acdLogout>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Message", "event": [{"listen": "test", "script": {"id": "0878467b-240c-4f37-b380-aa2e20f6c36c", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"Message\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": {\r\n            \"mediaLabel\": \"_ML_114F67B7A08C00000000@ chicago.psap.il.us \",\r\n            \"direction\": \"Out\",\r\n            \"messageType\": \"SMS\",\r\n            \"text\": \"What is your emergency?\"\r\n        },\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Message XML", "event": [{"listen": "test", "script": {"id": "0878467b-240c-4f37-b380-aa2e20f6c36c", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>Message</eventType>\n    <message>\n        <mediaLabel>_ML_114F67B7A08C00000000@ chicago.psap.il.us </mediaLabel>\n        <direction>Out</direction>\n        <messageType>SMS</messageType>\n        <text>What is your emergency?</text>\n    </message>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "EIDD", "event": [{"listen": "test", "script": {"id": "6cd845c7-ae9d-4b54-bd42-c9820d3ca4fa", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", " "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"EIDD\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": {\r\n            \"eiddBody\": \"EIDD xml document here\",\r\n            \"direction\": \"Out\"\r\n        },\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    }"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "EIDD XML", "event": [{"listen": "test", "script": {"id": "6cd845c7-ae9d-4b54-bd42-c9820d3ca4fa", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>EIDD</eventType>\n    <EIDD>\n        <EIDDBody>EIDD xml document here</EIDDBody>\n        <direction>Out</direction>\n    </EIDD>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Element State Change", "event": [{"listen": "test", "script": {"id": "76c46993-b0a5-435f-97d1-a72133810705", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"ElementStateChange\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": {\r\n            \"stateChangeNewStateValue\": \"Unmanned\"\r\n        },\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Element State Change XML", "event": [{"listen": "test", "script": {"id": "76c46993-b0a5-435f-97d1-a72133810705", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>ElementStateChange</eventType>\n    <elementStateChange>\n        <StateChangeNewStateValue>Unmanned</StateChangeNewStateValue>\n    </elementStateChange>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "CDR Type1", "event": [{"listen": "test", "script": {"id": "33585926-b4f1-46ab-87a4-7cbc52fa246f", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"CDRType1\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": {\r\n            \"startTime\": \"2015-08-21T12:58:03.01Z \",\r\n            \"operatorId\": \"1\",\r\n            \"ani\": \"2470001234\",\r\n            \"presentedTime\": \"2015-08-21T12:58:03.03Z \",\r\n            \"answeredTime\": \"2015-08-21T12:58:03.10Z \",\r\n            \"jobNumber\": \"101001\",\r\n            \"transferTime\": \"2015-08-21T12:58:03.30Z \",\r\n            \"transferAnswerTime\": \"2015-08-21T12:58:03.35Z \",\r\n            \"disassociatedTime\": \"2015-08-21T12:58:03.40Z \",\r\n            \"transferTargetType\": \"Workstation\",\r\n            \"transferTargetName\": \" PC Host name \",\r\n            \"transferTarget\": \"2471230012\",\r\n            \"disconnectReason\": \".\",\r\n            \"ivrOutcome\": \".\",\r\n            \"externalTransferAttempts\": \"0\",\r\n            \"dnis\": \"911\",\r\n            \"endTime\": \"2015-08-21T12:58:04.10Z \"\r\n        },\r\n        \"queueStateChange\": null\r\n    \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "CDR Type1 XML", "event": [{"listen": "test", "script": {"id": "33585926-b4f1-46ab-87a4-7cbc52fa246f", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>CDRType1</eventType>\n    <cdrType1>\n        <startTime>2015-08-21T12:58:03.01Z </startTime>\n        <operatorId>1</operatorId>\n        <ani>2470001234</ani>\n        <presentedTime>2015-08-21T12:58:03.03Z </presentedTime>\n        <answeredTime>2015-08-21T12:58:03.10Z </answeredTime>\n        <jobNumber>101001</jobNumber>\n        <transferTime>2015-08-21T12:58:03.30Z </transferTime>\n        <transferAnswerTime>2015-08-21T12:58:03.35Z </transferAnswerTime>\n        <disassociatedTime>2015-08-21T12:58:03.40Z </disassociatedTime>\n        <transferTargetType>Workstation</transferTargetType>\n        <transferTargetName> PC Host name </transferTargetName>\n        <transferTarget>2471230012</transferTarget>\n        <disconnectReason>.</disconnectReason>\n        <ivrOutcome>.</ivrOutcome>\n        <externalTransferAttempts>0</externalTransferAttempts>\n        <dnis>911</dnis>\n        <endTime>2015-08-21T12:58:04.10Z </endTime>\n    </cdrType1>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Queue State Change", "event": [{"listen": "test", "script": {"id": "69408c47-553e-47e2-8401-e4c7e3111341", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n        \"logNumber\": 0,\r\n        \"timestamp\": \"2019-01-13T13:34:01.247-05:00\",\r\n        \"agencyOrElement\": \"Godzilla_AB\",\r\n        \"agent\": \".\",\r\n        \"callIdentifier\": \"_CI_168487DF5A8C0011F973@Godzilla\",\r\n        \"incidentIdentifier\": \"_II_168487DF5A8C0011F973@Godzilla\",\r\n        \"eventType\": \"QueueStateChange\",\r\n        \"ecrfQuery\": null,\r\n        \"heldQuery\": null,\r\n        \"heldResponse\": null,\r\n        \"media\": null,\r\n        \"startCall\": null,\r\n        \"ecrfResponse\": null,\r\n        \"vpcQuery\": null,\r\n        \"vpcResponse\": null,\r\n        \"srdbQuery\": null,\r\n        \"srdbResponse\": null,\r\n        \"aliQuery\": null,\r\n        \"aliResponse\": null,\r\n        \"route\": null,\r\n        \"answer\": null,\r\n        \"transferCall\": null,\r\n        \"hold\": null,\r\n        \"holdRetrieved\": null,\r\n        \"muteOn\": null,\r\n        \"muteOff\": null,\r\n        \"privacyOn\": null,\r\n        \"privacyOff\": null,\r\n        \"mergeCall\": null,\r\n        \"outboundCall\": null,\r\n        \"endMedia\": null,\r\n        \"endCall\": null,\r\n        \"login\": null,\r\n        \"logout\": null,\r\n        \"busiedOut\": null,\r\n        \"agentAvailable\": null,\r\n        \"acdLogin\": null,\r\n        \"acdLogout\": null,\r\n        \"message\": null,\r\n        \"eidd\": null,\r\n        \"elementStateChange\": null,\r\n        \"cdrType1\": null,\r\n        \"queueStateChange\": {\r\n            \"stateChangeNotificationContents\": \"Count\",\r\n            \"queueId\": \"2470001234\",\r\n            \"queueName\": \"2470001234\",\r\n            \"direction\": \"\",\r\n            \"count\": \"1\"\r\n        }\r\n   \r\n}"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "Queue State Change XML", "event": [{"listen": "test", "script": {"id": "69408c47-553e-47e2-8401-e4c7e3111341", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", " ", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<LogEvent xmlns='http://solacom.com/Logging'>\n    <timestamp>2019-01-13T18:34:01.247Z</timestamp>\n    <agencyOrElement>Godzilla_AB</agencyOrElement>\n    <agent>.</agent>\n    <callIdentifier>_CI_168487DF5A8C0011F973@Godzilla</callIdentifier>\n    <incidentIdentifier>_II_168487DF5A8C0011F973@Godzilla</incidentIdentifier>\n    <eventType>QueueStateChange</eventType>\n    <QueueStateChange>\n        <StateChangeNotificationContents>Count</StateChangeNotificationContents>\n        <queueId>2470001234</queueId>\n        <queueName>2470001234</queueName>\n        <direction></direction>\n        <count>1</count>\n    </QueueStateChange>\n</LogEvent>"}, "url": {"raw": "http://{{environment_url}}/api/events", "protocol": "http", "host": ["{{environment_url}}"], "path": ["api", "events"]}}, "response": []}, {"name": "https://{{elasticsearch_url}}", "event": [{"listen": "test", "script": {"id": "55e54618-0c8e-4a59-9b23-88cb20d8e1cf", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response type is json\", function () {", "    pm.response.to.be.json;", "});", "", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "basic", "basic": [{"key": "password", "value": "Solacom2019", "type": "string"}, {"key": "username", "value": "micha<PERSON>.yee", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "https://{{elasticsearch_url}}", "protocol": "https", "host": ["{{elasticsearch_url}}"]}, "description": "ElasticSearch Health Check"}, "response": []}, {"name": "https://{{kibana_url}}", "event": [{"listen": "test", "script": {"id": "0d903977-b5df-4ee2-865e-2f72274a197f", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "basic", "basic": [{"key": "password", "value": "Solacom2019", "type": "string"}, {"key": "username", "value": "micha<PERSON>.yee", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "https://{{kibana_url}}", "protocol": "https", "host": ["{{kibana_url}}"]}, "description": "Kibana Health Check"}, "response": []}]}