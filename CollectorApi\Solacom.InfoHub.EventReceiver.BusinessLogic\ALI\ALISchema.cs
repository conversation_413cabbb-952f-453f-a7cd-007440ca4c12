﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Serialization;

namespace Solacom.InfoHub.EventReceiver.BusinessLogic.ALI
{
    [XmlRoot(ElementName = "alischema")]
    public class ALISchema
    {
        [XmlElement(ElementName = "carrierStart")]
        public int? CarrierStart { get; set; }
        [XmlElement(ElementName = "carrierEnd")]
        public int? CarrierEnd { get; set; }
        public Carriers carriers { get; set; }
    }

    [XmlRoot(ElementName = "type")]
    public class Type
    {
        [XmlElement(ElementName = "start")]
        public int? Start { get; set; }
        [XmlElement(ElementName = "length")]
        public int? Length { get; set; }
    }

    [XmlRoot(ElementName = "positionId")]
    public class PositionId
    {
        [XmlElement(ElementName = "start")]
        public int? Start { get; set; }
        [XmlElement(ElementName = "length")]
        public int? Length { get; set; }
    }

    [XmlRoot(ElementName = "callbackNumber")]
    public class CallbackNumber
    {
        [XmlElement(ElementName = "start")]
        public int? Start { get; set; }
        [XmlElement(ElementName = "length")]
        public int? Length { get; set; }
    }

    [XmlRoot(ElementName = "time")]
    public class Time
    {
        [XmlElement(ElementName = "start")]
        public int? Start { get; set; }
        [XmlElement(ElementName = "length")]
        public int? Length { get; set; }
    }

    [XmlRoot(ElementName = "esn")]
    public class Esn
    {
        [XmlElement(ElementName = "start")]
        public int? Start { get; set; }
        [XmlElement(ElementName = "length")]
        public int? Length { get; set; }
    }

    [XmlRoot(ElementName = "streetNumber")]
    public class StreetNumber
    {
        [XmlElement(ElementName = "start")]
        public int? Start { get; set; }
        [XmlElement(ElementName = "length")]
        public int? Length { get; set; }
    }

    [XmlRoot(ElementName = "date")]
    public class Date
    {
        [XmlElement(ElementName = "start")]
        public int? Start { get; set; }
        [XmlElement(ElementName = "length")]
        public int? Length { get; set; }
    }

    [XmlRoot(ElementName = "address")]
    public class Address
    {
        [XmlElement(ElementName = "start")]
        public int? Start { get; set; }
        [XmlElement(ElementName = "length")]
        public int? Length { get; set; }
    }

    [XmlRoot(ElementName = "city")]
    public class City
    {
        [XmlElement(ElementName = "start")]
        public int? Start { get; set; }
        [XmlElement(ElementName = "length")]
        public int? Length { get; set; }
    }

    [XmlRoot(ElementName = "state")]
    public class State
    {
        [XmlElement(ElementName = "start")]
        public int? Start { get; set; }
        [XmlElement(ElementName = "length")]
        public int? Length { get; set; }
    }

    [XmlRoot(ElementName = "country")]
    public class Country
    {
        [XmlElement(ElementName = "start")]
        public int? Start { get; set; }
        [XmlElement(ElementName = "length")]
        public int? Length { get; set; }
    }

    [XmlRoot(ElementName = "zipcode")]
    public class Zipcode
    {
        [XmlElement(ElementName = "start")]
        public int? Start { get; set; }
        [XmlElement(ElementName = "length")]
        public int? Length { get; set; }
    }

    [XmlRoot(ElementName = "latitude")]
    public class Latitude
    {
        [XmlElement(ElementName = "start")]
        public int? Start { get; set; }
        [XmlElement(ElementName = "length")]
        public int? Length { get; set; }
    }

    [XmlRoot(ElementName = "longitude")]
    public class Longitude
    {
        [XmlElement(ElementName = "start")]
        public int? Start { get; set; }
        [XmlElement(ElementName = "length")]
        public int? Length { get; set; }
    }

    [XmlRoot(ElementName = "confidence")]
    public class Confidence
    {
        [XmlElement(ElementName = "start")]
        public int? Start { get; set; }
        [XmlElement(ElementName = "length")]
        public int? Length { get; set; }
    }

    [XmlRoot(ElementName = "uncertainity")]
    public class Uncertainty
    {
        [XmlElement(ElementName = "start")]
        public int? Start { get; set; }
        [XmlElement(ElementName = "length")]
        public int? Length { get; set; }
    }

    [XmlRoot(ElementName = "cos")]
    public class Cos
    {
        [XmlElement(ElementName = "name")]
        public string Name { get; set; }
        [XmlElement(ElementName = "type")]
        public Type Type { get; set; }
        [XmlElement(ElementName = "positionId")]
        public PositionId PositionId { get; set; }
        [XmlElement(ElementName = "callbackNumber")]
        public CallbackNumber CallbackNumber { get; set; }
        [XmlElement(ElementName = "time")]
        public Time Time { get; set; }
        [XmlElement(ElementName = "date")]
        public Date Date { get; set; }
        [XmlElement(ElementName = "address")]
        public Address Address { get; set; }
        [XmlElement(ElementName = "city")]
        public City City { get; set; }
        [XmlElement(ElementName = "state")]
        public State State { get; set; }
        [XmlElement(ElementName = "country")]
        public Country Country { get; set; }
        [XmlElement(ElementName = "zipcode")]
        public Zipcode Zipcode { get; set; }
        [XmlElement(ElementName = "latitude")]
        public Latitude Latitude { get; set; }
        [XmlElement(ElementName = "longitude")]
        public Longitude Longitude { get; set; }
        [XmlElement(ElementName = "confidence")]
        public Confidence Confidence { get; set; }
        [XmlElement(ElementName = "uncertainty")]
        public Uncertainty Uncertainty { get; set; }
        [XmlElement(ElementName = "streetNumber")]
        public StreetNumber StreetNumber { get; set; }
        [XmlElement(ElementName = "esn")]
        public Esn Esn { get; set; }
    }

    [XmlRoot(ElementName = "classOfService")]
    public class ClassOfService
    {
        [XmlElement(ElementName = "cos")]
        public List<Cos> Cos { get; set; }
    }

    [XmlRoot(ElementName = "carrier")]
    public class Carrier
    {
        [XmlElement(ElementName = "name")]
        public string Name { get; set; }
        [XmlElement(ElementName = "cosStart")]
        public int? CosStart { get; set; }
        [XmlElement(ElementName = "cosLength")]
        public int? CosLength { get; set; }
        [XmlElement(ElementName = "classOfService")]
        public ClassOfService ClassOfService { get; set; }
    }

    [XmlRoot(ElementName = "carriers")]
    public class Carriers
    {
        [XmlElement(ElementName = "carrier")]
        public List<Carrier> Carrier { get; set; }
    }
}
