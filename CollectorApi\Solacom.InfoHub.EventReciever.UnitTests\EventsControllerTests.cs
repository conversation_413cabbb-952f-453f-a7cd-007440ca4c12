using System.Net;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using Solacom.InfoHub.EventReceiver.AppService;
using Solacom.InfoHub.EventReceiver.AppService.Interfaces;
using Solacom.InfoHub.EventReceiver.BusinessLogic.Interfaces;
using Solacom.InfoHub.EventReceiver.Entities;
using Solacom.InfoHub.EventReceiver.Web.API.Controllers;
using Solacom.InfoHub.EventReceiver.Web.API.Security;
using Solacom.InfoHub.EventReceiver.Web.Dtos;
using Microsoft.Extensions.Logging;

namespace Solacom.InfoHub.EventReciever.UnitTests
{
    [TestClass]
    public class EventsControllerTests
    {
        private EventsController _controller;
        private IEventsService _service;
        private Mock<IEventsManager> _mockEventManager;
        private Mock<IOptions<UserKeyData>> _mockData;
        private Mock<ILogger<EventsController>> _mockLogger; 
        private string _request;

        [TestInitialize]
        public void Init()
        {
            _mockEventManager = new Mock<IEventsManager>();
            _mockData = new Mock<IOptions<UserKeyData>>();
            _service = new EventsService(_mockEventManager.Object);
            _mockLogger = new Mock<ILogger<EventsController>>();

            _controller = new EventsController(_service, _mockLogger.Object, new UserKeyManager(), _mockData.Object,null,null);
            _request ="";
        }

    }
}
