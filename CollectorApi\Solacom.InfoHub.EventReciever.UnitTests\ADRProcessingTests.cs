using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Solacom.InfoHub.EventReceiver.BusinessLogic;
using Solacom.InfoHub.EventReceiver.Exceptions;
using EventLog = Solacom.InfoHub.EventReceiver.Entities.EventLog;
using ALIRawData = Solacom.InfoHub.EventReceiver.BusinessLogic.ALI.ALIRawData;
using ALIHelper = Solacom.InfoHub.EventReceiver.BusinessLogic.ALI.ALIHelper;
using Entities = Solacom.InfoHub.EventReceiver.ElasticSearch.Entities;
using System.Collections.Generic;

namespace Solacom.InfoHub.EventReciever.UnitTests
{
    /// <summary>
    /// Test cases around specific EziNet functionality / ADR/EIDD
    /// </summary>
    [TestClass]
    public class ADRProcessingTests
    {
        [TestMethod]
        public void ADR_COSCalculation_NoMatch()
        {
            string pidflomethod = "Manual";
            string serviceType = "Pigeons";
            string serviceEnvironment = "";
            string serviceMobility = "Mobile";

            string cos = EventReceiver.Entities.ClassOfService.GetCOSValue(pidflomethod,
                                                                            serviceType,
                                                                            serviceEnvironment,
                                                                            serviceMobility
                                                                        );
            Assert.IsTrue(cos == null);
        }


        [TestMethod]
        public void ADR_COSCalculation_WPH0()
        {
            string pidflomethod = "Manual";
            string serviceType = "Wireless";
            string serviceEnvironment = "";
            string serviceMobility = "Mobile";

            string cos = EventReceiver.Entities.ClassOfService.GetCOSValue(pidflomethod,
                                                                            serviceType,
                                                                            serviceEnvironment,
                                                                            serviceMobility
                                                                        );
            Assert.IsTrue(cos == "WPH0");
        }

        [TestMethod]
        public void ADR_COSCalculation_WPH1()
        {
			string pidflomethod = "Cell";
            string serviceType = "Wireless";
            string serviceEnvironment = "";
			string serviceMobility = "Mobile";
			
			string cos = EventReceiver.Entities.ClassOfService.GetCOSValue(	pidflomethod,
                                                                            serviceType,
                                                                            serviceEnvironment,
																			serviceMobility																			
																		);
            Assert.IsTrue(cos == "WPH1");
        }


        [TestMethod]
        public void ADR_COSCalculation_WPH2()
        {
            string pidflomethod = "";
            string serviceType = "wireless";
            string serviceEnvironment = "";
            string serviceMobility = "Mobile";

            string cos = EventReceiver.Entities.ClassOfService.GetCOSValue(pidflomethod,
                                                                            serviceType,
                                                                            serviceEnvironment,
                                                                            serviceMobility
                                                                        );
            Assert.IsTrue(cos == "WPH2");
        }

        [TestMethod]
        public void ADR_COSCalculation_voIPBusiness()
        {
            string pidflomethod = "Manual";
            string serviceType = "VOIP";
            string serviceEnvironment = "Business";
            string serviceMobility = "Unknown";

            string cos = EventReceiver.Entities.ClassOfService.GetCOSValue(pidflomethod,
                                                                            serviceType,
                                                                            serviceEnvironment,
                                                                            serviceMobility
                                                                        );
            Assert.IsTrue(cos == "VoIP Business");
        }

        [TestMethod]
        public void ADR_COSCalculation_Residence()
        {
            string pidflomethod = "Manual";
            string serviceType = "POTS";
            string serviceEnvironment = "Residence";
            string serviceMobility = "Fixed";

            string cos = EventReceiver.Entities.ClassOfService.GetCOSValue(pidflomethod,
                                                                            serviceType,
                                                                            serviceEnvironment,
                                                                            serviceMobility
                                                                        );
            Assert.IsTrue(cos == "Residence");
        }

        [TestMethod]
        public void ADR_COSCalculation_Business()
        {
            string pidflomethod = "Manual";
            string serviceType = "POTS";
            string serviceEnvironment = "Business";
            string serviceMobility = "Fixed";

            string cos = EventReceiver.Entities.ClassOfService.GetCOSValue(pidflomethod,
                                                                            serviceType,
                                                                            serviceEnvironment,
                                                                            serviceMobility
                                                                        );
            Assert.IsTrue(cos == "Business");
        }

        [TestMethod]
        public void ADR_COSCalculation_NULL_Check()
        {
            string pidflomethod = null;
            string serviceType = "Wireless";
            string serviceEnvironment = null;
            string serviceMobility = "Mobile";

            string cos = EventReceiver.Entities.ClassOfService.GetCOSValue(pidflomethod,
                                                                            serviceType,
                                                                            serviceEnvironment,
                                                                            serviceMobility
                                                                        );
            Assert.IsTrue(cos == "WPH2");
        }

        [TestMethod]
        public void ADR_COSCalculation_OTTBusiness_Check()
        {
            string pidflomethod = "Manual";
            string serviceType = "OTT";
            string serviceEnvironment = "Business";
            string serviceMobility = "Unknown";

            string cos = EventReceiver.Entities.ClassOfService.GetCOSValue(pidflomethod,
                                                                            serviceType,
                                                                            serviceEnvironment,
                                                                            serviceMobility
                                                                        );
            Assert.IsTrue(cos == "VoIP Business");
        }

        [TestMethod]
        public void ADR_COSCalculation_DigitalCoin_Check()
        {
            string pidflomethod = "Manual";
            string serviceType = "digital;coin";
            string serviceEnvironment = "Business";
            string serviceMobility = "Unknown";

            string cos = EventReceiver.Entities.ClassOfService.GetCOSValue(pidflomethod,
                                                                            serviceType,
                                                                            serviceEnvironment,
                                                                            serviceMobility
                                                                        );
            Assert.IsTrue(cos == "VoIP Coin or Pay Phone");
        }


    }
}