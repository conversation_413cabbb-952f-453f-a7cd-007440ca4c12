﻿using System;
using System.Net.Http;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Xml;
using System.Linq;

namespace EventPusher
{
    class Program
    {

        /// <summary>
        /// threshold of when the program auto terminates if the number of concurrent failures occurs. (exceptions)  
        /// Introduced to avoid flooding the channel if there is a terminating case occuring to often.
        /// </summary>
        private static int _FAILURE_THRESHOLD = 100;

        private static HttpClient _CLIENT;

        /// <summary>
        /// Main entry point.
        /// 
        /// Any exceptions will cause a stop condition - can expand this to handle exceptions gracefully as required.
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        static async Task Main(string[] args)
        {
            WriteMessage($"Starting Event Pusher.");

            using (_CLIENT = new HttpClient())
            {
                //passed in configuration
                string targetUrl;
                List<string> psapList;
                int fullIterationCount;
                int delaybetweeninterations;

                //simple logic check - just making sure at least the two leading parameters are set.
                if (args.Length < 2)
                {
                    Console.WriteLine($"Must pass in at least 2 parameters.");
                    Console.WriteLine($"Format: [[FULL URL]] [[PSAP LIST]] [[Iterations]] [[DELAY BETWEEN loops (ms)]]");
                    Console.WriteLine($"Example: http://localhost:55247/api/event  Ottawa,Gatineau 100 500");
                    Console.WriteLine($"Setting iterations to -1 will result in continuation until termination.");
                    return;
                }


                targetUrl = (args.Length > 0 ? args[0] : "http://localhost:55247/api/event");
                psapList = args.Length > 1 ? args[1].Split(',').ToList() : new List<string>() { "" };
                fullIterationCount = args.Length > 2 ? int.Parse(args[2]) : 1;
                fullIterationCount = fullIterationCount == -1 ? int.MaxValue : fullIterationCount;  //setting the "infinit case" when iteration is set to -1
                delaybetweeninterations = args.Length > 3 ? int.Parse(args[3]) : 1;

                WriteMessage($"Executing Parameters: \r\n\t url: {targetUrl}, \r\n\t psapList: {string.Join(",", psapList)}, \r\n\t iterations:{fullIterationCount}, \r\n\t delay: {delaybetweeninterations}ms");

                //per Event
                DateTime timestamp = DateTime.Now;

                XmlDocument xmlDoc = new XmlDocument();
                xmlDoc.Load(@"resources/TestEvents.xml");
                XmlNodeList nodeList = xmlDoc.SelectNodes("//*[local-name()='LogEvent']");

                if (nodeList != null && nodeList.Count > 0)
                {
                    //loop defined variables
                    string eventXml = string.Empty;
                    string psapName = string.Empty;
                    string id = string.Empty;
                    int successCounter = 0;
                    int totalNodes = 0;
                    //tracks the number of failures - gets reset after a success. 
                    int failureCounter = 0;


                    for (int i = 0; i < fullIterationCount; i++)
                    {
                        psapName = psapList[i % psapList.Count];    //logic loops through the PSAP list per iteration - going one after another.
                        id = $"{DateTime.Now.ToString("yyyyMMddHHmmss")}_{psapName}_{i}";

                        totalNodes += nodeList.Count;

                        foreach (XmlNode node in nodeList)
                        {
                            eventXml = @$"<LogEvent xmlns=""http://solacom.com/Logging"">{node.InnerXml}</LogEvent>";

                            eventXml = eventXml.Replace("{{timestamp}}", $"{DateTime.Now.ToUniversalTime().ToString("s")}Z");
                            eventXml = eventXml.Replace("{{PSAPName}}", psapName);
                            eventXml = eventXml.Replace("{{CallID}}", $"_CI_dev_{id}");
                            eventXml = eventXml.Replace("{{IncidentID}}", $"_II_dev_{id}");
                            eventXml = eventXml.Replace("{{IterationCounter}}", i.ToString());

                            try
                            {
                                if (await RunAPI(targetUrl, eventXml))
                                {
                                    successCounter++;
                                    failureCounter = 0;
                                }
                                else
                                {
                                    failureCounter++;
                                }
                            }
                            catch (Exception ex)
                            {
                                WriteMessage($"Failure event {i} - Unique Id: {id} Exception: - {ex.Message}");
                                failureCounter++;
                            }
                        }

                        //introducing rolling message writting - write fewer messaging as the iteration counter increases
                        //Concept is as the number increases, show less status updates - minus the final update.
                        if (i < 10
                            || (i > 10 && i < 100 && i % 10 == 0)
                            || (i > 100 && i < 1000 && i % 100 == 0)
                            || (i > 1000 && i < 10000 && i % 1000 == 0)
                            || (i > 10000 && i % 10000 == 0)
                            || i >= fullIterationCount - 1)
                        {
                            WriteMessage($"Iteration {i} - (latest) Unique Id: {id} Status: {successCounter}/{totalNodes}");
                            totalNodes = 0;
                            successCounter = 0;
                        }

                        if (failureCounter > _FAILURE_THRESHOLD)
                        {
                            WriteMessage($"!!!!!!!!!!!!!!!!!!!!!!!!! \r\n\t Concurrent failure limit of {_FAILURE_THRESHOLD} reached.  Terminating process.");
                            return;
                        }

                        if (i < fullIterationCount - 1)
                        {
                            System.Threading.Thread.Sleep(delaybetweeninterations);
                        }
                        else if( fullIterationCount == int.MaxValue)    //reset the iterations to keep the loop going and going ... and going.
                        {
                            i = 0;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Calls a end point with a given payload
        /// </summary>
        /// <param name="targetUrl">fully qualified end point</param>
        /// <param name="eventXmlPayload">XML based payload</param>
        /// <returns>Success bool</returns>
        /// <remarks>outputs to the Console on failure.</remarks>
        /// <exception cref="Exception"></exception>
        static async Task<bool> RunAPI(string targetUrl, string eventXmlPayload)
        {
            _CLIENT.DefaultRequestHeaders.Accept.Clear();

            HttpContent content = new StringContent(eventXmlPayload, System.Text.Encoding.UTF8, "text/xml");

            HttpResponseMessage response = await _CLIENT.PostAsync(targetUrl, content);

            if (response.IsSuccessStatusCode)
            {
                return true;
            }
            else
            {
                string result = response.Content.ReadAsStringAsync().Result;

                WriteMessage($"Failure occurred: {result}");

                return false;
            }
            
        }

        /// <summary>
        /// Writes a Console message with a leading timestamp.
        /// </summary>
        /// <param name="message">Message to write</param>
        static void WriteMessage(string message)
        {
            Console.WriteLine($"{DateTime.Now.ToString()} - {message}");
        }

    }
}
