﻿using System;

namespace Solacom.InfoHub.EventReceiver.MariaDb.Context.Models
{
    /// <summary>
    /// Stores limited data that defines a Call
    /// </summary>
    public class CallInstance
    {        
        /// <summary>
        /// Unique call identifier
        /// </summary>
        public string CallIdentifier { get; set; }

        /// <summary>
        /// unique Client Identifier
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// Date object to reflect the source date
        /// </summary>
        public DateTime? Date{ get; set; }
    }
}
