﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Solacom.InfoHub.EventReceiver.BusinessLogic.Interfaces;
using Solacom.InfoHub.EventReceiver.Entities;
using Solacom.InfoHub.EventReceiver.AppService.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Generic;

namespace Solacom.InfoHub.EventReceiver.Web.API.ScheduledTasks
{
    public class DBCleanupService : IHostedService, IDisposable
    {
        private readonly IEventsManager _eventsManager;
        private Timer _timer;
        private readonly ScheduledServicesFrequenciesInMinutes _settings;
        private readonly ILogger<ExpiredEventService> _logger;
        public readonly string _connectionString = string.Empty;
        private readonly IConfiguration _configuration;
        private int _hashEventsCleanupOlderThan = 24;
        private int _eventsCleanupOlderThan = 24;
        private int _agentSessionCleanupOlderThan = 24;

        public DBCleanupService(IServiceScopeFactory serviceScopeFactory, ILogger<ExpiredEventService> logger, IOptions<ScheduledServicesFrequenciesInMinutes> options, IConfiguration configuration)
        {
            _logger = logger;

            var scope = serviceScopeFactory.CreateScope();
            _eventsManager = scope.ServiceProvider.GetService<IEventsManager>();

            _settings = options.Value;

            _configuration = configuration;

            //loading the configurations on the init
            if( !int.TryParse(_configuration["Database:CleanupOlderThanInHours:hashEvents"], out _hashEventsCleanupOlderThan))
            {
                _logger.LogError("Error parsing Cleanup hashEvents setting");
            }
            if (!int.TryParse(_configuration["Database:CleanupOlderThanInHours:events"], out _eventsCleanupOlderThan))
            {
                _logger.LogError("Error parsing Cleanup events setting");
            }
            if (!int.TryParse(_configuration["Database:CleanupOlderThanInHours:agentsession"], out _agentSessionCleanupOlderThan))
            {
                _logger.LogError("Error parsing Cleanup agentSession setting");
            }

        }

        public Task StartAsync(CancellationToken stoppingToken)
        {
            _timer = new Timer(async o =>await DoWork(o), null, TimeSpan.Zero,
                TimeSpan.FromMinutes(_settings.DBCleanupService));

            return Task.CompletedTask;
        }

        private async Task DoWork(object state)
        {
            await _eventsManager.CleanUpTables(_hashEventsCleanupOlderThan, _eventsCleanupOlderThan, _agentSessionCleanupOlderThan);
        }

        public Task StopAsync(CancellationToken stoppingToken)
        {
            _timer?.Change(Timeout.Infinite, 0);

            return Task.CompletedTask;
        }

        public void Dispose()
        {
            _timer?.Dispose();
        }
    }
}