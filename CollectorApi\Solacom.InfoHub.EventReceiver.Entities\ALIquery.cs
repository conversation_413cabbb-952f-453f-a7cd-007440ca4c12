﻿using System.Xml.Serialization;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    [XmlRoot(ElementName = "aliQuery")]
    public class AliQuery
    {
        [XmlElement(ElementName = "mediaLabel")]
        public string MediaLabel { get; set; }
        [XmlElement(ElementName = "aliLink")]
        public string AliLink { get; set; }
        [XmlElement(ElementName = "uri")]
        public string Uri { get; set; }
        [XmlElement(ElementName = "serviceArea")]
        public string ServiceArea { get; set; }
        [XmlElement(ElementName = "aliQueryType")]
        public string AliQueryType { get; set; }
    }
}
