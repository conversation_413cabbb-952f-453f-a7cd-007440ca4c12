﻿using System.Collections.Generic;

namespace Solacom.InfoHub.EventReceiver.Entities
{
    public class RootEvent
    {
        public RootEvent()
        {
            Events = new List<EventLog>();
        }
        public bool IsESRootExists { get; set; }
        public string CallId { get; set; }
        /// <summary>
        /// Set to private due to refactoring - not currently in usage.
        /// </summary>
        private string MediaLabel { get; set; }//We do not need this as well
        public List<EventLog> Events { get; set; }

        /// <summary>
        /// Stores the Client Id of the given Root Event
        /// </summary>
        /// <remarks>introduced with the Expired Rooot events</remarks>
        public string ClientId { get; set; }
    }
}