﻿using Nest;
using System;
using System.Collections.Generic;

namespace Solacom.InfoHub.EventReceiver.ElasticSearch.Entities
{
    /// <summary>
    /// Location specific data from Logs.
    /// </summary>
    /// <seealso cref="https://www.rfc-editor.org/rfc/rfc5491"/>
    public class LocationData
    {

        /// <summary>
        /// Main contructor, setting source of the Location information
        /// </summary>
        /// <param name="locationEventSource"></param>
        public LocationData(BusinessLogic.Enums.LocationEventSource locationEventSource)
        {
            LocationEventSource = locationEventSource;
        }

        /// <summary>
        /// Stores the Source of the Location data.  
        /// </summary>
        /// <remarks>Set to Ignore so it does not populate to the final ES index</remarks>
        [Ignore]
        public BusinessLogic.Enums.LocationEventSource LocationEventSource { get; set; }

        /// <summary>
        /// Displays Location Source as string
        /// </summary>
        public string LocationEventSourceString 
        { 
            get 
            {
                return LocationEventSource.ToString();
            } 
        }
        
        /// <summary>
        /// Time of the source event that generated location data
        /// </summary>
        public DateTime? TimeStamp { get; set; }

        /// <summary>
        /// The PidfloMethod of the call
        /// </summary>
        public string Method { get; set; }

        /// <summary>
        /// The Class of Service
        /// </summary>
        /// <remarks>Calculation is part of the core EventReciever definition</remarks>
        public string COS { get; set; }

        /// <summary>
        /// Civic address location data
        /// </summary>
        public CivicAddress CivicAddress { get; set; }

        public Ellipsoid Ellipsoid { get; set; }
        public Point Point { get; set; }
        public Circle Circle { get; set; }
        public Ellipse Ellipse { get; set; }
        public ArcBand ArcBand { get; set; }
        public Prism Prism { get; set; }
        public Sphere Sphere { get; set; }
        public Polygon Polygon { get; set; }
        /// <summary>
        /// data accuracy for the location source
        /// </summary>
        public Confidence Confidence { get; set; }

        /// <summary>
        /// Service information associated to the given Response
        /// </summary>
        public ServiceInfo ServiceInfo { get; set; }

        /// <summary>
        /// Device information from the given response.
        /// </summary>
        public DeviceInfo DeviceInfo { get; set; }

        /// <summary>
        /// Provider information
        /// </summary>
        public ProviderInfo ProviderInfo { get; set; }
    }
    public class Confidence
    {
        public string Pdf { get; set; }
        public string Text { get; set; }
    }

    /// <summary>
    /// Defines the Service Information related to the given geo Point information (ADR data set)
    /// </summary>
    /// <remarks>Main data point used for Class Of Service (COS) calculations.</remarks>
    public class ServiceInfo
    {
        public string ServiceType { get; set; }
        public string ServiceEnvironment { get; set; }
        public string ServiceMobility { get; set; }
        public string Legacy_Class_Of_service { get; set; }
        public override string ToString()
        {
            return $"Type:{ServiceType} - Env:{ServiceEnvironment} - Mobility:{ServiceMobility}";
        }
    }

    /// <summary>
    /// Defines the Device information related to the given information (ADR data set)
    /// </summary>
    /// <remarks>Data is used to populate the ESN / Unqiue device information</remarks>
    public class DeviceInfo
    {
        public string DataProviderReference { get; set; }
        public string DeviceClassification { get; set; }
        public string DeviceMfgr { get; set; }
        public string DeviceModelNr { get; set; }
        public string TypeOfDeviceID { get; set; }
        public string UniqueDeviceID { get; set; }

        public override string ToString()
        {
            return $"DeviceInfo.Type:{TypeOfDeviceID} .ID: {UniqueDeviceID}";
        }
    }


    /// <summary>
    /// Defines the Provider information (ADR data set)
    /// </summary>
    public class ProviderInfo
    {
        public string DataProviderReference { get; set; }
        public string DataProviderString { get; set; }
        public string ProviderID { get; set; }
        public string ProviderIDSeries { get; set; }
        public string TypeOfProvider { get; set; }
        public string ContactURI { get; set; }
        public string Language { get; set; }

        public override string ToString()
        {
            return $"ProviderInfo.DataProviderString:{DataProviderString} ";
        }
    }

    /// <summary>
    /// Civic address location data
    /// </summary>
    public class CivicAddress
    {
        public string Country { get; set; }
        public string A1 { get; set; }
        public string A2 { get; set; }
        public string A3 { get; set; }
        public string A4 { get; set; }
        public string A5 { get; set; }
        public string A6 { get; set; }
        public string PRM { get; set; }
        public string PRD { get; set; }
        public string RD { get; set; }
        public string STS { get; set; }
        public string POD { get; set; }
        public string POM { get; set; }
        public string RDSEC { get; set; }
        public string RDSUBBR { get; set; }
        public string HNO { get; set; }
        public string HNS { get; set; }
        public string LMK { get; set; }
        public string LOC { get; set; }
        public string FLR { get; set; }
        public string NAM { get; set; }
        public string PC { get; set; }
        public string BLD { get; set; }
        public string UNIT { get; set; }
        public string ROOM { get; set; }
        public string SEAT { get; set; }
        public string PLC { get; set; }
        public string PCN { get; set; }
        public string POBOX { get; set; }
        public string ADDCODE { get; set; }
    }

    /// <summary>
    /// Defines a single point in geographic space
    /// </summary>
    public class Point
    {
        /// <summary>
        /// X-axis (north to south) position
        /// </summary>
        public double Latitude { get; set; }
        /// <summary>
        /// Y-axis (east to west) position 
        /// </summary>
        public double Longitude { get; set; }
        /// <summary>
        /// Z-axis (relative to sea level vertical) position
        /// </summary>
        public double? Altitude { get; set; }

        /// <summary>
        /// Processes a space deliminted point string 
        /// </summary>
        /// <param name="pointAsString">Input string format expected is "{Lat} {Long} {Alt}", Alt is optional.</param>
        /// <returns>Valid Point object</returns>
        /// <exception cref="Exception"></exception>
        public static Point ParsePointString(string pointAsString)
        {
            if (string.IsNullOrEmpty(pointAsString))
            {
                return null;    
            }

            Point rtnPoint = new Point();

            pointAsString = pointAsString.Trim();   //fix INFO-1860, trailing spaces are possible; which can result in parse failures.

            string[] pointSplit = pointAsString.Split(' ');

            if (pointSplit.Length >= 2)
            {
                rtnPoint.Latitude = double.Parse(pointSplit[0]);
                rtnPoint.Longitude = double.Parse(pointSplit[1]);
                if (pointSplit.Length == 3)
                {
                    rtnPoint.Altitude = double.Parse(pointSplit[2]);
                }
            }

            return rtnPoint;
        }
    }

    /// <summary>
    /// Core location object for point position
    /// </summary>
    public class GML
    {
        public Point Point { get; set; }
    }

    /// <summary>
    /// Ellipse object.
    /// </summary>
    public class Ellipse : GML
    {
        public double? SemiMajorAxis { get; set; }
        public double? SemiMinorAxis { get; set; }
        public double? Orientation { get; set; }
    }
    /// <summary>
    /// Ellipsoid, 3D extension of an ellipse
    /// </summary>
    public class Ellipsoid : Ellipse
    {
        public double? VerticalAxis { get; set; }
    }
    
    /// <summary>
    /// arcBand object, a zone for expanding distance from a single point.
    /// </summary>
    /// <remarks>Commonly used for wireless systems from a central point.</remarks>
    public class ArcBand : GML
    {
        public double? InnerRadius { get; set; }
        public double? OuterRadius { get; set; }
        public double? StartAngle { get; set; }
        public double? OpeningAngle { get; set; }
    }

    /// <summary>
    /// Circle object.
    /// </summary>
    /// <remarks>Primary 2D usage, Sphere should be used for 3D representations.</remarks>
    public class Circle : GML
    {
        public double? Radius { get; set; }
    }

    /// <summary>
    /// Sphere object, 3D extensionof the circle.
    /// </summary>
    public class Sphere : Circle
    {
    }


    /// <summary>
    /// Series of points representing a object. 
    /// </summary>
    /// <remarks>Normally used for building or coverage outline</remarks>
    public class Polygon : GML
    {
        /// <summary>
        /// Stores the collection of points that make up the polygon.  Expectation is the first and last point are the closed loop.
        /// </summary>
        public List<Point> PointList{ get;set; }

        public Polygon()
        {
            PointList = new List<Point>();
        }

        /// <summary>
        /// Calculates the center point based on the series of points of the shape.  This does not calculate the Centroid/nor does a full polygon central calculation
        /// </summary>
        /// <returns>center point, null of not available</returns>
        /// <remarks>https://www.omnicalculator.com/math/centroid</remarks>
        public Point CalculateCenterPointBasedOnPoints()
        {
            if( this.PointList == null || this.PointList.Count == 0)
            {
                return null;
            }

            double centerLat = 0;
            double centerLong = 0;
            foreach (Point pt in this.PointList)
            {
                centerLat += pt.Latitude;
                centerLong += pt.Longitude;
            }

            if( centerLat == 0 || centerLong == 0)
            {
                return null;
            }

            return new Point() { Latitude = centerLat/this.PointList.Count, Longitude = centerLong / this.PointList.Count };
        }

        /// <summary>
        /// Processes collection of a space deliminted point strings 
        /// </summary>
        /// <param name="pointAsStringList">Collection of input strings; format is "{Lat} {Long} {Alt}", Alt is optional.</param>
        /// <returns>Valid Poolygon object</returns>
        /// <exception cref="Exception">Exception - fails fully on any exception with the collection</exception>
        public static List<Point> ProcessStringToPolygon(List<string> polygonStringList)
        {
            List<Point> rtnPointList = new List<Point>();

            if( polygonStringList != null && polygonStringList.Count > 0)
            {
                Point pt;
                foreach (string polygonStr in polygonStringList)
                {
                    pt = Point.ParsePointString(polygonStr);
                    rtnPointList.Add(pt);
                }
            }

            return rtnPointList;
        }

    }

    /// <summary>
    /// A 3D polygon shape object
    /// </summary>
    /// <remarks>commonly used to represent a 3D building</remarks>
    public class Prism : Polygon
    {
        public double? Height { get; set; }
    }
}