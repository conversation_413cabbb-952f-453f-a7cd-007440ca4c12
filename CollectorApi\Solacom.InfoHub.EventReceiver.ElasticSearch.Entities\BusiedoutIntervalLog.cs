﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Solacom.InfoHub.EventReceiver.ElasticSearch.Entities
{
    public class BusiedoutIntervalLog
    {
        public int Id { get; set; }
        public DateTime? StartBusyInterval { get; set; }
        public DateTime? EndBusyInterval { get; set; }
        public float? IntervalTimeInMinutes { get; set; }
        public string BusiedOutAction { get; set; }
    }
}
